{"name": "cadetlabs", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "start": "turbo run start", "lint": "turbo run lint", "check-types": "turbo run check-types", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"prettier": "^3.6.0", "prettier-plugin-tailwindcss": "^0.6.11", "turbo": "^2.5.6", "typescript": "^5.9.2"}, "packageManager": "pnpm@8.15.6", "engines": {"node": ">=18"}}