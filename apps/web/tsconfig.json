{
  "extends": "@repo/typescript-config/nextjs.json",
  "compilerOptions": {
 
    "paths": {
      "@/*": ["./*"],
      "@repo/ui/*": ["../../packages/ui/src/*"],
      "@api-types": ["./lib/api-types"],

    },
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "next-env.d.ts",
    "next.config.ts",
    ".next/types/**/*.ts",
    "../../.types/api/**/*.d.ts"
  ],
  "exclude": ["node_modules"]
}
