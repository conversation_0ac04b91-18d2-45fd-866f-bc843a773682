"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import logo from "../../public/logo.png";
import { useLogout } from "@/hooks/use-logout";

interface ForbiddenPageProps {
  title?: string;
  message?: string;
  showContactSupport?: boolean;
  onRetry?: () => void;
  showBackButton?: boolean;
  showLogoutButton?: boolean;
  showSecurityNotice?: boolean;
}

export function ForbiddenPage({
  title = "Access Denied",
  message = "You don't have permission to access this resource. Please contact your administrator if you believe this is an error.",
  showContactSupport = true,
  showBackButton = true,
  showLogoutButton = false,
  showSecurityNotice = true,
  onRetry,
}: ForbiddenPageProps) {
  const logout = useLogout();
  return (
    <div className="to-destructive/5 flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <Card className="border-0 bg-white/80 text-center shadow-lg backdrop-blur-sm">
          <CardHeader className="pb-6">
            {/* Logo */}
            <div className="mb-6 flex justify-center">
              <div className="bg-destructive/10 rounded-2xl p-4">
                <Image src={logo} alt="Cadetlabs" className="h-12 w-14 object-contain" />
              </div>
            </div>

            {/* 403 Illustration */}
            <div className="relative mb-6">
              <div className="flex items-center justify-center space-x-4">
                {/* Large 403 Text with decorative elements */}
                <div className="relative">
                  <div className="text-destructive/20 select-none text-8xl font-bold">4</div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Icons.shieldX className="text-destructive h-12 w-12" />
                  </div>
                </div>
                <div className="text-destructive/20 select-none text-8xl font-bold">0</div>
                <div className="relative">
                  <div className="text-destructive/20 select-none text-8xl font-bold">3</div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Icons.lock className="text-destructive h-12 w-12" />
                  </div>
                </div>
              </div>

              {/* Floating decorative elements */}
              <div className="bg-destructive/10 absolute -left-4 -top-4 h-8 w-8 animate-pulse rounded-full"></div>
              <div className="bg-destructive/20 absolute -right-6 -top-2 h-6 w-6 animate-pulse rounded-full delay-300"></div>
              <div className="bg-destructive/15 absolute -bottom-2 left-8 h-4 w-4 animate-pulse rounded-full delay-700"></div>
            </div>

            {/* Error Message */}
            <div className="space-y-2">
              <h1 className="text-foreground text-3xl font-bold">{title}</h1>
              <p className="text-muted-foreground mx-auto max-w-md text-lg">{message}</p>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Security Notice */}
            {showSecurityNotice && (
              <div className="bg-destructive/5 border-destructive/20 rounded-lg border p-4 text-left">
                <h3 className="text-foreground mb-3 flex items-center gap-2 font-semibold">
                  <Icons.shield className="text-destructive h-4 w-4" />
                  Security Notice:
                </h3>
                <ul className="text-muted-foreground space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <div className="bg-destructive h-1.5 w-1.5 rounded-full"></div>
                    This area requires special permissions
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="bg-destructive h-1.5 w-1.5 rounded-full"></div>
                    Your access level may have changed
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="bg-destructive h-1.5 w-1.5 rounded-full"></div>
                    Contact your administrator for access
                  </li>
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col justify-center gap-3 sm:flex-row">
              {showBackButton && (
                <Button
                  onClick={() => window.history.back()}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Icons.arrowLeft className="h-4 w-4" />
                  Go Back
                </Button>
              )}
              {onRetry && (
                <Button onClick={onRetry} variant="outline" className="flex items-center gap-2">
                  <Icons.RefreshCw className="h-4 w-4" />
                  Try Again
                </Button>
              )}
              {showBackButton && (
                <Link href="/dashboard">
                  <Button className="flex w-full items-center gap-2 sm:w-auto">
                    <Icons.home className="h-4 w-4" />
                    Back to Dashboard
                  </Button>
                </Link>
              )}
              {showLogoutButton && (
                <Button
                  onClick={() => logout()}
                  className="flex w-full items-center gap-2 sm:w-auto"
                >
                  <Icons.logout className="h-4 w-4" />
                  Logout
                </Button>
              )}
            </div>

            {/* Contact Support */}
            {showContactSupport && (
              <div className="border-border/50 border-t pt-4">
                <p className="text-muted-foreground text-sm">
                  Need access to this resource?{" "}
                  <Link href="/help" className="text-primary hover:text-primary/80 font-medium">
                    Contact Support
                  </Link>{" "}
                  or reach out to your administrator.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
// Default 403 page component
export default function ForbiddenPageDefault() {
  return <ForbiddenPage />;
}
