"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import logo from "../../public/logo.png";

interface ServerErrorPageProps {
  title?: string;
  message?: string;
  showContactSupport?: boolean;
  onRetry?: () => void;
}

export function ServerErrorPage({
  title = "Something Went Wrong",
  message = "Sorry, something went wrong. Please try again later.",
  showContactSupport = true,
  onRetry,
}: ServerErrorPageProps) {
  return (
    <div className="to-destructive/5 flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <Card className="border-0 bg-white/80 text-center shadow-lg backdrop-blur-sm">
          <CardHeader className="pb-6">
            {/* Logo */}
            <div className="mb-6 flex justify-center">
              <div className="bg-destructive/10 rounded-2xl p-4">
                <Image src={logo} alt="Cadetlabs" className="h-12 w-14 object-contain" />
              </div>
            </div>

            {/* 403 Illustration */}
            <div className="relative mb-6">
              <div className="flex items-center justify-center space-x-4">
                {/* Large 403 Text with decorative elements */}
                <div className="relative">
                  <div className="text-destructive/20 select-none text-8xl font-bold">5</div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Icons.shieldX className="text-destructive h-12 w-12" />
                  </div>
                </div>
                <div className="text-destructive/20 select-none text-8xl font-bold">0</div>
                <div className="relative">
                  <div className="text-destructive/20 select-none text-8xl font-bold">0</div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Icons.lock className="text-destructive h-12 w-12" />
                  </div>
                </div>
              </div>

              {/* Floating decorative elements */}
              <div className="bg-destructive/10 absolute -left-4 -top-4 h-8 w-8 animate-pulse rounded-full"></div>
              <div className="bg-destructive/20 absolute -right-6 -top-2 h-6 w-6 animate-pulse rounded-full delay-300"></div>
              <div className="bg-destructive/15 absolute -bottom-2 left-8 h-4 w-4 animate-pulse rounded-full delay-700"></div>
            </div>

            {/* Error Message */}
            <div className="space-y-2">
              <h1 className="text-foreground text-3xl font-bold">{title}</h1>
              <p className="text-muted-foreground mx-auto max-w-md text-lg">{message}</p>
            </div>
          </CardHeader>
        </Card>
      </div>
    </div>
  );
}

// Default 403 page component
export default function ServerErrorPageDefault() {
  return <ServerErrorPage />;
}
