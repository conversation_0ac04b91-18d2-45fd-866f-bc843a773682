"use client";
import React from "react";
import { Button } from "@repo/ui/components/button";
import { useRouter, usePathname } from "next/navigation";
import { Icons } from "@repo/ui/components/icons";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { paths } from "@/routes/paths";

interface NavButton {
  name: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: () => void;
}

interface SubNavBarProps {
  buttons?: NavButton[];
  children?: React.ReactNode;
}

const MENU_ITEMS = [
  // {
  //   title: "Configurations",
  //   icon: Icons.workFlow,
  //   path: paths.configuration.root,
  // },
  {
    title: "Glossary",
    icon: Icons.ChartBarIncreasing,
    path: paths.glossary.root,
  },
];

export const SubNavBar: React.FC<SubNavBarProps> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname(); // ✅ Current route

  const isActive = (path: string) => pathname.startsWith(path);

  return (
    <div className="flex w-full items-center justify-between py-2">
      {/* Left Nav Buttons */}
      <div className="flex items-center gap-3">
        {[
          { title: "Dashboard", path: "/dashboard" },
          { title: "Project", path: "/project" },
        ].map(({ title, path }) => (
          <Button
            key={title}
            onClick={() => router.push(path)}
            variant={isActive(path) ? "default" : "outline"}
            className={`${
              isActive(path) ? "bg-primary text-white" : "text-gray-500 hover:bg-gray-100"
            }`}
          >
            {title}
          </Button>
        ))}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className={`flex items-center gap-1 transition-colors ${
                MENU_ITEMS.some((i) => isActive(i.path))
                  ? "bg-primary text-white"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Tools
              {/* ✅ Inherit parent color using text-current */}
              <Icons.chevronDown className="h-4 w-4 text-current transition-transform" />
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent>
            {MENU_ITEMS.map(({ title, icon: Icon, path }) => (
              <DropdownMenuItem
                key={title}
                className={`cursor-pointer ${isActive(path) ? "bg-gray-100" : ""}`}
                onClick={() => router.push(path)}
              >
                <div className="flex items-start gap-3 p-2">
                  <Icon
                    className={`h-5 w-5 ${isActive(path) ? "text-primary" : "text-teal-500"}`}
                  />
                  <div className="flex flex-col">
                    <h1
                      className={`text-sm font-medium ${
                        isActive(path) ? "text-primary" : "text-gray-900"
                      }`}
                    >
                      {title}
                    </h1>
                  </div>
                </div>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      {/* Right Action Button */}
      <div className="flex items-center gap-3">{children}</div>
    </div>
  );
};
