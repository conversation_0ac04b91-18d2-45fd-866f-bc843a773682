import React, { useEffect, useRef, useCallback, useLayoutEffect } from "react";
import { useReactTable, TableOptions, flexRender } from "@tanstack/react-table";
import { useVirtualizer } from "@tanstack/react-virtual";
import { calculateTableSizing } from "./calculateTableSizing";
import { useWindowSize } from "usehooks-ts";

interface CustomVirtualListProps<TData extends object> {
  options: TableOptions<TData>;
  rowHeight?: number;
  rowGap?: number;
  onEndReached?: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  onRowClick?: (row: TData) => void; // ✅ new prop
}

const CustomVirtualList = <TData extends object>({
  options,
  rowHeight = 48,
  rowGap = 8,
  onEndReached,
  hasNextPage = false,
  isFetchingNextPage = false,
  onRowClick,
}: CustomVirtualListProps<TData>) => {
  const table = useReactTable(options);
  const containerRef = useRef<HTMLDivElement>(null);

  const { rows } = table.getRowModel();
  const headers = table.getHeaderGroups();

  const windowDimensions = useWindowSize();
  const headersFlat = table.getFlatHeaders();

  useLayoutEffect(() => {
    if (containerRef.current) {
      const initialColumnSizing = calculateTableSizing(
        headersFlat,
        containerRef.current?.clientWidth
      );
      table.setColumnSizing(initialColumnSizing);
    }
  }, [headersFlat, windowDimensions.width]);

  // Initialize virtualizer
  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => containerRef.current,
    estimateSize: () => rowHeight + rowGap,
    overscan: 5,
  });

  // Auto-load pages when there's available space
  const autoLoadIfSpaceAvailable = useCallback(() => {
    const container = containerRef.current;
    if (!container || !onEndReached || !hasNextPage || isFetchingNextPage) return;

    if (container.scrollHeight <= container.clientHeight) {
      console.log("Auto-loading next page - space available");
      onEndReached();
    }
  }, [onEndReached, hasNextPage, isFetchingNextPage]);

  // Detect scroll bottom and auto-load
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !onEndReached) return;

    const handleScroll = () => {
      const { scrollHeight, scrollTop, clientHeight } = container;
      if (scrollHeight - scrollTop - clientHeight < 50 && !isFetchingNextPage && hasNextPage) {
        onEndReached();
      }
    };

    container.addEventListener("scroll", handleScroll);
    const timeoutId = setTimeout(autoLoadIfSpaceAvailable, 100);

    return () => {
      container.removeEventListener("scroll", handleScroll);
      clearTimeout(timeoutId);
    };
  }, [onEndReached, autoLoadIfSpaceAvailable, isFetchingNextPage, hasNextPage]);

  useEffect(() => {
    autoLoadIfSpaceAvailable();
  }, [autoLoadIfSpaceAvailable]);

  return (
    <div className="w-full">
      <div ref={containerRef} className="relative h-[400px] overflow-auto">
        <div style={{ display: "grid" }}>
          {/* Header */}
          <div className="sticky top-0 z-10 bg-white" style={{ display: "grid" }}>
            {headers.map((headerGroup) => (
              <div key={headerGroup.id} className="flex w-full">
                {headerGroup.headers.map((header) => (
                  <div
                    key={header.id}
                    className="p-2 text-left text-sm font-medium text-gray-500"
                    style={{
                      width: header.getSize(),
                    }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </div>
                ))}
              </div>
            ))}
          </div>

          {/* Body */}
          <div
            style={{
              display: "grid",
              height: `${rowVirtualizer.getTotalSize() + rows.length * rowGap}px`,
              position: "relative",
            }}
          >
            {rowVirtualizer.getVirtualItems().map((virtualRow) => {
              const row = rows[virtualRow.index];
              return (
                <div
                  key={row?.id}
                  data-index={virtualRow.index}
                  className={`bg-secondary flex items-center rounded-md transition-colors duration-150 ${
                    onRowClick ? "cursor-pointer hover:bg-gray-50" : ""
                  }`}
                  onClick={() => onRowClick?.(row?.original!)} // ✅ Added row click handler
                  style={{
                    position: "absolute",
                    transform: `translateY(${virtualRow.start + virtualRow.index * rowGap}px)`,
                    width: "100%",
                    height: `${rowHeight}px`,
                  }}
                >
                  {row?.getVisibleCells().map((cell) => (
                    <div
                      key={cell.id}
                      className="truncate p-2"
                      style={{
                        width: cell.column.getSize(),
                      }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </div>
                  ))}
                </div>
              );
            })}

            {/* Loading indicator */}
            {isFetchingNextPage && (
              <div
                className="absolute left-0 flex items-center justify-center rounded-md bg-gray-100"
                style={{
                  top: rowVirtualizer.getTotalSize() + rows.length * rowGap,
                  height: rowHeight,
                  width: "100%",
                }}
              >
                <div className="text-sm text-gray-600">Loading more...</div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomVirtualList;
