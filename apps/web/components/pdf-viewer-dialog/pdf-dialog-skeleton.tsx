"use client";

import { Skeleton } from "@repo/ui/components/skeleton";

export function PdfDialogSkeleton() {
  return (
    <div className="flex h-full w-full flex-col">
      {/* Header with navigation and filter */}
      <div className="border-border flex items-center justify-between border-b p-4">
        <div className="flex items-center gap-4">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-8 w-8" />
        </div>
        <Skeleton className="h-8 w-32" />
      </div>

      {/* Main content area with two pages */}
      <div className="flex flex-1 gap-6 p-6">
        {/* Left Page */}
        <div className="relative flex w-1/2 min-w-0 flex-col">
          {/* Toolbar Skeleton */}
          <div className="mb-3 flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-8 w-8" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-8" />
              <Skeleton className="h-4 w-12" />
            </div>
            <Skeleton className="h-8 w-8" />
          </div>
          {/* Page Skeleton */}
          <div className="bg-background ring-border relative flex flex-1 items-center justify-center overflow-hidden rounded-lg p-3 ring-1">
            <Skeleton className="h-[600px] w-[450px]" />
          </div>
        </div>

        {/* Right Page */}
        <div className="relative flex w-1/2 min-w-0 flex-col">
          {/* Toolbar Skeleton */}
          <div className="mb-3 flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-8 w-8" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-8" />
              <Skeleton className="h-4 w-12" />
            </div>
            <Skeleton className="h-8 w-8" />
          </div>
          {/* Page Skeleton */}
          <div className="bg-background ring-border relative flex flex-1 items-center justify-center overflow-hidden rounded-lg p-3 ring-1">
            <Skeleton className="h-[600px] w-[450px]" />
          </div>
        </div>
      </div>
    </div>
  );
}
