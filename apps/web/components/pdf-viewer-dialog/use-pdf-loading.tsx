"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { instance } from "@/axios-instance";

interface UsePdfLoaderOptions {
  open: boolean;
  projectId?: string;
  fileId?: string;
}

interface UsePdfLoaderReturn {
  pdfUrl: string | null;
  isLoading: boolean;
  pdfError: string | null;
  reloadPdf: () => Promise<void>;
}

export function usePdfLoader({ open, projectId, fileId }: UsePdfLoaderOptions): UsePdfLoaderReturn {
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [pdfError, setPdfError] = useState<string | null>(null);
  const objectUrlRef = useRef<string | null>(null);

  // Cleanup helper
  const cleanupPdfUrl = useCallback(() => {
    if (objectUrlRef.current) {
      URL.revokeObjectURL(objectUrlRef.current);
      objectUrlRef.current = null;
      setPdfUrl(null);
    }
  }, []);

  // Fetch PDF
  const fetchPdf = useCallback(async () => {
    if (!open || !projectId || !fileId) return;

    setIsLoading(true);
    setPdfError(null);

    try {
      const response = await instance.get(`/projects/${projectId}/files/${fileId}`, {
        responseType: "blob",
      });
      const blob = new Blob([response.data], { type: "application/pdf" });

      // Cleanup any previous URL before assigning new one
      cleanupPdfUrl();

      const url = URL.createObjectURL(blob);
      objectUrlRef.current = url;
      setPdfUrl(url);
    } catch (error) {
      console.error("Error loading PDF:", error);
      setPdfError("Failed to load PDF file");
    } finally {
      setIsLoading(false);
    }
  }, [open, projectId, fileId, cleanupPdfUrl]);

  // Load when opened
  useEffect(() => {
    if (open) {
      fetchPdf();
    } else {
      cleanupPdfUrl();
      setPdfError(null);
    }

    // Clean up on unmount
    return () => cleanupPdfUrl();
  }, [open, fetchPdf, cleanupPdfUrl]);

  return { pdfUrl, isLoading, pdfError, reloadPdf: fetchPdf };
}
