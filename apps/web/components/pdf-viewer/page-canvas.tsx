export function PageCanvas({
  pageNum,
  canvasRef,
  overlayRef,
  scale,
}: {
  pageNum: number;
  canvasRef: React.RefObject<HTMLCanvasElement | null>;
  overlayRef: React.RefObject<HTMLDivElement | null>;
  scale: number;
}) {
  return (
    <div className="bg-muted mx-auto">
      <div
        className="relative"
        style={{
          transform: `scale(${scale})`,
          transformOrigin: "center center",
        }}
      >
        <canvas ref={canvasRef} className="mx-auto block max-w-none" />
        <div ref={overlayRef} className="absolute inset-0" />
      </div>
    </div>
  );
}
