"use client";

import { Minus, Plus, RotateCw } from "lucide-react";
import { But<PERSON> } from "@repo/ui/components/button";

export function Toolbar({
  scale,
  onZoomIn,
  onZoomOut,
  onRotate,
  checked,
  onChangeChecked,
}: {
  pageNum?: number;
  scale: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onRotate: () => void;
  checked: { job: boolean; spare: boolean };
  onChangeChecked: (key: "job" | "spare", val: boolean) => void;
  disabled?: boolean;
}) {
  return (
    <div className="flex items-center justify-between gap-2">
      <div className="flex items-center gap-2">
        <Button onClick={onZoomOut} variant="outline" size="icon" disabled={scale <= 0.5}>
          <Minus className="h-4 w-4" />
        </Button>
        <span className="min-w-[4rem] text-center font-mono text-sm">
          {Math.round(scale * 100)}%
        </span>
        <Button onClick={onZoomIn} variant="outline" size="icon" disabled={scale >= 3}>
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex items-center gap-2">
        <label className="flex items-center gap-1 text-sm">
          <input
            type="checkbox"
            checked={checked.job}
            onChange={(e) => onChangeChecked("job", e.target.checked)}
          />
          Job
        </label>
        <label className="flex items-center gap-1 text-sm">
          <input
            type="checkbox"
            checked={checked.spare}
            onChange={(e) => onChangeChecked("spare", e.target.checked)}
          />
          Spare
        </label>
      </div>

      <Button onClick={onRotate} variant="outline" size="icon">
        <RotateCw className="h-4 w-4" />
      </Button>
    </div>
  );
}
