"use client";

import React from "react";
import Image from "next/image";

import dayjs from "dayjs";
import updateLocale from "dayjs/plugin/updateLocale";

import logo from "../../public/LOGOPMS.png";

import { useMetaData } from "@/providers/meta-data";
import { TenantSelector } from "@/providers/tenant-selector";
import { CanAccess } from "@/providers/access-control";

import type { Action, Resource } from "@/types";
import UserProfile from "./user-profile";

import Link from "next/link";

dayjs.extend(updateLocale);
dayjs.locale("en");

const Navbar: React.FC = () => {
  const { metaData: user } = useMetaData();

  return (
    <nav className="flex h-[80px] shrink-0 border bg-white px-4">
      <div className="flex w-full items-center justify-between px-4">
        {/* Left: Logo */}
        <Link href="/dashboard" className="flex flex-row items-center">
          <Image src={logo} alt="Cadetlabs" className="h-[49px] w-[56px] object-contain" priority />
          <h1 className="text-primary ml-2 text-2xl font-bold">
            {user?.tenant?.name || "PMS Asset Builder"}
          </h1>
        </Link>
        {/* Right: Actions */}

        <div className="flex items-center space-x-6">
          <CanAccess resource={"tenant" as Resource.Tenant} privilege={"read" as Action.Read}>
            <TenantSelector />
          </CanAccess>
          {/* Divider */}
          <span className="h-6 w-px bg-gray-300" />

          {/* User Profile */}

          <UserProfile user={user} />
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
