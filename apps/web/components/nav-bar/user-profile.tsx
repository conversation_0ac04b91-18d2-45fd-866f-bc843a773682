"use client";

import { But<PERSON> } from "@repo/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { Icons } from "@repo/ui/components/icons";
import React from "react";
import { useRouter, usePathname } from "next/navigation";
import { useLogout } from "@/hooks/use-logout";
import { Resource, Action, UserProfileResponse } from "@/types";
import { CanAccess } from "@/providers/access-control";

type MenuItem = {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
  access?: {
    resource: Resource;
    privilege: Action;
  };
};

export const MENU_ITEMS: MenuItem[] = [
  {
    title: "User Management",
    description: "View & manage users here.",
    icon: Icons.users,
    path: "/users",
    access: { resource: "user" as Resource.User, privilege: "read" as Action.Read },
  },
  {
    title: "Tenant Management",
    description: "View & manage tenants here.",
    icon: Icons.boxes,
    path: "/tenant",
    access: { resource: "tenant" as Resource.Tenant, privilege: "read" as Action.Read },
  },
  {
    title: "Help & Support",
    description: "Get help & support here.",
    icon: Icons.headset,
    path: "/help",
  },
];

export default function UserProfile({ user }: { user: UserProfileResponse | null }) {
  const router = useRouter();
  const pathname = usePathname(); // 👈 Current active route
  const logout = useLogout();

  const handleRoute = (path: string) => router.push(path);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (err) {
      console.error("Logout failed:", err);
    }
  };

  return (
    <div className="flex items-center space-x-3">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="flex items-center space-x-2 rounded-md px-2 py-1 transition hover:bg-gray-50 focus:outline-none">
            <div className="flex h-[54px] w-[54px] items-center justify-center rounded-xl border bg-gray-50">
              <span className="text-primary text-2xl font-semibold">
                {user?.displayName?.charAt(0).toUpperCase() || "A"}
              </span>
            </div>

            <div className="text-left leading-tight">
              <h1 className="text-sm font-medium capitalize text-gray-800">
                {user?.displayName || "NA"}
              </h1>
              <p className="text-[11px] text-gray-500">{user?.role || "NA"}</p>
            </div>

            <Icons.chevronDown className="size-4 text-gray-500" />
          </button>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          className="w-[320px] rounded-2xl border border-gray-100 bg-white p-0 shadow-lg"
          side="bottom"
          align="end"
          sideOffset={6}
        >
          {/* Profile Section */}
          <div className="flex flex-col border-b border-gray-100 p-5">
            <div className="flex flex-row gap-4">
              <div className="flex h-[64px] w-[64px] items-center justify-center rounded-xl border bg-gray-50">
                <span className="text-primary text-2xl font-semibold">
                  {user?.displayName?.charAt(0).toUpperCase() || "A"}
                </span>
              </div>
              <div>
                <h1 className="text-base font-semibold text-gray-800">
                  {user?.displayName || "NA"}
                </h1>
                <p className="text-xs capitalize text-gray-500">{user?.role || "NA"}</p>
                <p className="mt-1 w-[200px] truncate text-sm text-gray-500">
                  {user?.email || "NA"}
                </p>

                <Button
                  className="mt-3 rounded-md text-sm font-medium"
                  size="sm"
                  onClick={handleLogout}
                >
                  <Icons.logout className="mr-2 h-4 w-4" />
                  Log Out
                </Button>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="divide-y divide-gray-200">
            {MENU_ITEMS.map(({ title, description, icon: Icon, path, access }) => {
              const isActive = pathname.startsWith(path); // ✅ highlight logic
              const menuItem = (
                <DropdownMenuItem
                  key={title}
                  onClick={() => handleRoute(path)}
                  className={`cursor-pointer rounded-none p-4 transition ${
                    isActive ? "bg-teal-50 text-teal-700" : "text-gray-800 hover:bg-gray-50"
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <Icon className={`size-5 ${isActive ? "text-teal-700" : "text-teal-600"}`} />
                    <div className="flex flex-col">
                      <h1 className="text-sm font-semibold">{title}</h1>
                      <p className="text-xs text-gray-500">{description}</p>
                    </div>
                  </div>
                </DropdownMenuItem>
              );

              return access ? (
                <CanAccess key={title} resource={access.resource} privilege={access.privilege}>
                  {menuItem}
                </CanAccess>
              ) : (
                menuItem
              );
            })}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Notification + Settings */}
      <div className="flex items-center space-x-3">
        {[Icons.bell].map((Icon, i) => (
          <div
            key={i}
            className="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full border border-gray-200 transition hover:bg-gray-100"
          >
            <Icon className="size-4 text-gray-600" />
          </div>
        ))}
      </div>
    </div>
  );
}
