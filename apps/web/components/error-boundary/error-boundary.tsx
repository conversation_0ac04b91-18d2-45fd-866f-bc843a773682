"use client";

import { useState } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { cn } from "@repo/ui/lib/utils";
import { Icons } from "@repo/ui/components/icons";
import Image from "next/image";
import logo from "../../public/logo.png";

interface ErrorBoundaryProps {
  error: Error | null;
  onRetry: () => void;
  className?: string;
}

export function ErrorBoundary({ error, onRetry, className }: ErrorBoundaryProps) {
  const [showFullError, setShowFullError] = useState(false);

  if (!error) return null;

  return (
    <div className={cn("flex min-h-[400px] items-center justify-center p-4", className)}>
      <Card className="w-full max-w-md text-center">
        <CardHeader className="pb-4">
          {/* Company Logo Placeholder */}
          <div className="mb-4 flex justify-center">
            <div className="bg-primary/10 flex h-16 w-16 items-center justify-center rounded-lg">
              <Image src={logo} alt="Company Logo" className="h-10 w-10" />
            </div>
          </div>

          {/* Error Icon and Title */}
          <div className="mb-2 flex items-center justify-center gap-2">
            <Icons.AlertTriangle className="text-destructive h-5 w-5" />
            <h2 className="text-foreground text-lg font-semibold">Something went wrong</h2>
          </div>

          <p className="text-muted-foreground text-sm">
            We encountered an error while loading your data. Please try again.
          </p>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Action Buttons */}
          <div className="flex flex-col gap-2">
            <Button onClick={onRetry} className="w-full" size="sm">
              <Icons.RefreshCw className="mr-2 h-4 w-4" />
              Reload
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFullError(!showFullError)}
              className="w-full"
            >
              {showFullError ? (
                <>
                  <Icons.ChevronUp className="mr-2 h-4 w-4" />
                  Hide Error Details
                </>
              ) : (
                <>
                  <Icons.chevronDown className="mr-2 h-4 w-4" />
                  Show Error Details
                </>
              )}
            </Button>
          </div>

          {/* Full Error Details */}
          {showFullError && (
            <div className="bg-muted mt-4 rounded-md p-3 text-left">
              <div className="text-muted-foreground break-all font-mono text-xs">
                <div className="mb-1 font-semibold">Error Message:</div>
                <div className="mb-2">{error.message}</div>

                {error.stack && (
                  <>
                    <div className="mb-1 font-semibold">Stack Trace:</div>
                    <pre className="whitespace-pre-wrap text-xs">{error.stack}</pre>
                  </>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
