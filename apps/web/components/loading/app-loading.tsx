"use client";

import React from "react";
import { Spinner } from "@repo/ui/components/spinner";

interface AppLoadingProps {
  message?: string;
  showProgress?: boolean;
}

export default function AppLoading({
  message = "Loading your workspace...",
  showProgress = false,
}: AppLoadingProps) {
  return (
    <div className="flex h-screen items-center justify-center bg-[#f0f7f6]">
      <div className="text-center">
        <div className="mb-6">
          <Spinner className="text-primary mx-auto h-8 w-8" />
        </div>
        <h2 className="mb-2 text-lg font-semibold text-gray-900">{message}</h2>
        <p className="text-sm text-gray-600">Setting up your App...</p>
        {showProgress && (
          <div className="mx-auto mt-4 w-64">
            <div className="h-2 rounded-full bg-gray-200">
              <div
                className="bg-primary h-2 animate-pulse rounded-full"
                style={{ width: "60%" }}
              ></div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Lightweight loading for smaller components
export function InlineLoading({ message = "Loading..." }: { message?: string }) {
  return (
    <div className="flex items-center justify-center p-4">
      <Spinner className="mr-2 h-4 w-4" />
      <span className="text-sm text-gray-600">{message}</span>
    </div>
  );
}

// Loading skeleton for content areas
export function ContentLoading() {
  return (
    <div className="animate-pulse space-y-4 p-6">
      <div className="h-4 w-3/4 rounded bg-gray-200"></div>
      <div className="space-y-2">
        <div className="h-4 rounded bg-gray-200"></div>
        <div className="h-4 w-5/6 rounded bg-gray-200"></div>
      </div>
      <div className="h-32 rounded bg-gray-200"></div>
    </div>
  );
}
