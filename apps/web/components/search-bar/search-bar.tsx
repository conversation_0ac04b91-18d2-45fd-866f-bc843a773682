"use client";
import React from "react";
import { Icons } from "@repo/ui/components/icons";
import { Input } from "@repo/ui/components/input";

interface SearchBarProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({ value = "", onChange, placeholder = "Search" }) => {
  return (
    <div className="w-full">
      <div className="relative flex w-full items-center gap-2">
        <Input
          id="search"
          type="search"
          placeholder={placeholder}
          value={value}
          className="h-[45px] w-full bg-white px-10"
          onChange={(e) => onChange?.(e.target.value)}
        />
        <Icons.search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
      </div>
    </div>
  );
};

export default SearchBar;
