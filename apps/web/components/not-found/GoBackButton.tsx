"use client";

import { But<PERSON> } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { useRouter } from "next/navigation";

export default function GoBackButton() {
  const router = useRouter();

  return (
    <Button onClick={() => router.back()} variant="outline" className="flex items-center gap-2">
      <Icons.arrowLeft className="h-4 w-4" />
      Go Back
    </Button>
  );
}
