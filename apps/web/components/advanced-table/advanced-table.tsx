"use client";

import React, { useEffect, useRef, useMemo, useCallback } from "react";
import cx from "clsx";
import { flexRender, getCoreRowModel, useReactTable, type ColumnDef } from "@tanstack/react-table";

interface AdvancedTableProps<TData> {
  data: TData[];
  columns: ColumnDef<TData>[];
  isLoading?: boolean;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  onLoadMore?: () => void;
  onRowSelect?: (row: TData, selected: boolean) => void;
  selectedRows?: Set<string>;
  getRowId?: (row: TData) => string;
  isRowDisabled?: (row: TData) => boolean;
  isSelectionDisabled?: (row: TData) => boolean;
  onRowClick?: (row: TData) => void;
  activeRowId?: string;
  className?: string;
  emptyMessage?: string;
  loadingMessage?: string;
}

export function AdvancedTable<TData>({
  data,
  columns,
  isLoading = false,
  hasNextPage = false,
  isFetchingNextPage = false,
  onLoadMore,
  onRowSelect,
  selectedRows,
  getRowId,
  isRowDisabled,
  isSelectionDisabled,
  onRowClick,
  activeRowId,
  className,
  emptyMessage = "No results found.",
  loadingMessage = "Loading...",
}: AdvancedTableProps<TData>) {
  const loadingRef = useRef<HTMLTableRowElement>(null);

  // Row selection logic - use isSelectionDisabled if provided, otherwise fall back to isRowDisabled
  const selectableRows = useMemo(() => {
    const disabledCheck = isSelectionDisabled || isRowDisabled;
    if (!disabledCheck) return data;
    return data.filter((row) => !disabledCheck(row));
  }, [data, isRowDisabled, isSelectionDisabled]);

  const allRowsSelected = useMemo(() => {
    if (!selectedRows || !getRowId || selectableRows.length === 0) return false;
    return selectableRows.every((row) => selectedRows.has(getRowId(row)));
  }, [selectedRows, getRowId, selectableRows]);

  const someRowsSelected = useMemo(() => {
    if (!selectedRows || !getRowId || selectableRows.length === 0) return false;
    return selectableRows.some((row) => selectedRows.has(getRowId(row))) && !allRowsSelected;
  }, [selectedRows, getRowId, selectableRows, allRowsSelected]);

  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (!onRowSelect || !getRowId) return;
      selectableRows.forEach((row) => onRowSelect(row, checked));
    },
    [selectableRows, onRowSelect, getRowId]
  );

  // Add selection column dynamically
  const tableColumns = useMemo(() => {
    if (!onRowSelect || !getRowId) return columns;

    const selectionColumn: ColumnDef<TData> = {
      id: "select",
      header: () => (
        <div className="flex items-center justify-center">
          <input
            type="checkbox"
            checked={allRowsSelected}
            ref={(el) => {
              if (el) el.indeterminate = someRowsSelected;
            }}
            onChange={(e) => handleSelectAll(e.target.checked)}
            className="h-4 w-4 rounded border-gray-400 text-blue-600 focus:ring-2 focus:ring-blue-500"
          />
        </div>
      ),
      cell: ({ row }) => {
        const rowId = getRowId(row.original);
        // Use isSelectionDisabled if provided, otherwise fall back to isRowDisabled
        const isSelectionDisabledCheck = isSelectionDisabled || isRowDisabled;
        const isDisabled = isSelectionDisabledCheck?.(row.original) || false;
        return (
          <div className="flex items-center justify-center">
            <input
              type="checkbox"
              checked={selectedRows?.has(rowId) || false}
              onChange={(e) => onRowSelect(row.original, e.target.checked)}
              disabled={isDisabled}
              className={cx(
                "h-4 w-4 rounded border-gray-400 text-blue-600 focus:ring-2 focus:ring-blue-500",
                isDisabled && "cursor-not-allowed opacity-50"
              )}
            />
          </div>
        );
      },
      size: 40,
    };

    return [selectionColumn, ...columns];
  }, [
    columns,
    onRowSelect,
    getRowId,
    selectedRows,
    allRowsSelected,
    someRowsSelected,
    handleSelectAll,
    isRowDisabled,
    isSelectionDisabled,
  ]);

  // Setup TanStack Table
  const table = useReactTable({
    data,
    columns: tableColumns,
    getCoreRowModel: getCoreRowModel(),
  });

  // Infinite scroll (load more)
  useEffect(() => {
    if (!loadingRef.current || !hasNextPage || isFetchingNextPage || !onLoadMore) return;
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting) onLoadMore();
      },
      { threshold: 0.1 }
    );
    observer.observe(loadingRef.current);
    return () => observer.disconnect();
  }, [hasNextPage, isFetchingNextPage, onLoadMore]);

  // Loading placeholder
  if (isLoading && data.length === 0) {
    return (
      <div className="flex h-64 items-center justify-center text-gray-500">{loadingMessage}</div>
    );
  }

  return (
    <div className={cx("relative", className)}>
      <div className="relative h-full max-h-[500px] overflow-auto border-gray-300">
        <table className="w-full border-collapse text-sm text-gray-700">
          {/* Sticky header */}
          <thead className="sticky top-0 z-20 bg-white shadow-sm">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id} className="border-b border-gray-200">
                {headerGroup.headers.map((header) => (
                  <th key={header.id} className="px-3 py-3 text-left font-medium text-gray-600">
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </thead>

          {/* Table body */}
          <tbody className="bg-white text-sm">
            {table.getRowModel().rows.length ? (
              <>
                {table.getRowModel().rows.map((row) => {
                  const isDisabled = isRowDisabled?.(row.original) || false;
                  const rowId = getRowId?.(row.original);
                  const isSelected = rowId && selectedRows?.has(rowId);
                  const isActive = rowId && activeRowId === rowId;

                  return (
                    <tr
                      key={row.id}
                      onClick={() => !isDisabled && onRowClick?.(row.original)}
                      className={cx(
                        "border-b border-gray-200 transition-colors",
                        !isDisabled && onRowClick && "cursor-pointer",
                        isDisabled
                          ? "bg-gray-50 opacity-60"
                          : isActive
                            ? "bg-blue-100 hover:bg-blue-200"
                            : isSelected
                              ? "bg-blue-50 hover:bg-blue-100"
                              : "hover:bg-gray-50"
                      )}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <td key={cell.id} className="whitespace-nowrap px-3 py-2">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </td>
                      ))}
                    </tr>
                  );
                })}

                {/* Load more trigger */}
                {(hasNextPage || isFetchingNextPage) && (
                  <tr ref={loadingRef}>
                    <td colSpan={tableColumns.length} className="h-16 text-center text-gray-500">
                      {isFetchingNextPage ? "Loading more..." : "Scroll to load more"}
                    </td>
                  </tr>
                )}
              </>
            ) : (
              <tr>
                <td
                  colSpan={tableColumns.length}
                  className="h-24 bg-gray-50 text-center text-gray-500"
                >
                  {emptyMessage}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default AdvancedTable;
