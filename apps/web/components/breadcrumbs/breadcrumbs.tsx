import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { Separator } from "@repo/ui/components/separator";
import { useRouter } from "next/navigation";
import React from "react";

export function BreadCrumbs({ path }: { path: string[] }) {
  const router = useRouter();
  return (
    <div className="flex items-center gap-4">
      <Button variant="ghost" size="sm" className="p-2" onClick={() => router.back()}>
        <Icons.arrowLeft className="h-4 w-4" />
      </Button>
      <div className="flex flex-row">
        {path.map((item, index) => (
          <div key={index} className="flex items-center gap-2">
            <h1 className="text-foreground my-0 text-xl font-semibold">{item}</h1>
            {index < path.length - 1 && <Icons.ChevronRight className="mr-2 h-4 w-4" />}
          </div>
        ))}
      </div>
    </div>
  );
}
