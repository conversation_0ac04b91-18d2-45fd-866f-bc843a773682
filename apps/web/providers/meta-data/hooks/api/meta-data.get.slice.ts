import { useQuery } from "@tanstack/react-query";
import { instance } from "@/axios-instance";
import type { UserProfileResponse, BaseResponse } from "@/types/index";
// GET request with useQuery
export function useGetMetaData() {
  return useQuery({
    queryKey: ["me"], // key used for caching
    queryFn: async () => {
      const response = await instance.get("/user/me");
      return response.data as BaseResponse<UserProfileResponse>;
    },
    staleTime: 1000 * 60 * 10, // 10 minutes: data considered fresh
    gcTime: 1000 * 60 * 30, // 30 minutes: keep in cache
    refetchOnWindowFocus: false, // don't refetch on tab focus
    refetchOnReconnect: true, // refetch on network reconnect
    retry: 2, // retry failed requests twice
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // exponential backoff
  });
}
