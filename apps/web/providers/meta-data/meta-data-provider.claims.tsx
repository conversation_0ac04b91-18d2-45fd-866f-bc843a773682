//Not in use

"use client";

import React, { createContext, ReactNode } from "react";
import { useSessionContext } from "supertokens-auth-react/recipe/session";
import { AppLoading } from "@/components/loading";

export interface MetaDataContextType {
  metaData: any | null;
  isLoading: boolean;
  error: any;
  refetch: () => void;
}

interface MetaDataProviderProps {
  children: ReactNode;
}

export const MetaDataContext = createContext<MetaDataContextType | undefined>(undefined);

const MetaDataProvider: React.FC<MetaDataProviderProps> = ({ children }) => {
  const session = useSessionContext();

  if (session.loading) {
    return <AppLoading message="Loading user metadata..." />;
  }

  if (session.doesSessionExist === false) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="mb-2 text-lg font-semibold text-red-600">No active session</h2>
          <p className="mb-4 text-gray-600">Please log in again.</p>
        </div>
      </div>
    );
  }

  const user = session?.accessTokenPayload?.user || null;
  // const roles = accessTokenPayload[UserRoleClaim.key] || [];
  // const permissions = accessTokenPayload[PermissionClaim.key] || [];

  const metaData = {
    ...user,
    // roles, permissions
  };

  return (
    <MetaDataContext.Provider
      value={{
        metaData,
        isLoading: session.loading,
        error: null,
        refetch: () => {}, // No-op since we’re reading directly
      }}
    >
      {children}
    </MetaDataContext.Provider>
  );
};

export default MetaDataProvider;
