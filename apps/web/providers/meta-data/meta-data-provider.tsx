"use client";
import React, { createContext, ReactNode } from "react";
import { useGetMetaData } from "@/providers/meta-data";
import { useLogout } from "@/hooks/use-logout";
import { AppLoading } from "@/components/loading";
import type { UserProfileResponse } from "@/types/index";
import { Button } from "@repo/ui/components/button";
import { APIError } from "@/axios-instance";
import { ForbiddenPage } from "@/components/error-pages";
import { toast } from "sonner";

export interface MetaDataContextType {
  metaData: UserProfileResponse | null;
  isLoading: boolean;
  error: any;
  refetch: () => void;
}

interface MetaDataProviderProps {
  children: ReactNode;
}

export const MetaDataContext = createContext<MetaDataContextType | undefined>(undefined);

const MetaDataProvider: React.FC<MetaDataProviderProps> = ({ children }) => {
  const { data, isLoading, error, refetch } = useGetMetaData();
  const logout = useLogout();

  // Show loading state while fetching
  if (isLoading) {
    return <AppLoading message="Loading user data..." />;
  }

  // Handle error state
  if (error instanceof APIError) {
    if (error.code === "INVALID_USER") {
      toast.error("Your account is not active. Please contact your administrator.", {
        id: "invalid-user",
      });
      return (
        <ForbiddenPage
          title="Account Not Active"
          message="Your account is not active. Please contact your administrator."
          showContactSupport={false}
          showLogoutButton={true}
          showSecurityNotice={false}
          showBackButton={false}
        />
      );
      // logout();
    }
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="mb-2 text-lg font-semibold text-red-600">Failed to load user data</h2>
          <p className="mb-4 text-gray-600">Please check your connection and try again.</p>
          <p className="mb-4 text-xs text-gray-500">Error: {error?.message || "Unknown error"}</p>
          <Button onClick={() => refetch()} variant="default">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <MetaDataContext.Provider
      value={{
        metaData: data?.data ?? null,
        isLoading,
        error,
        refetch,
      }}
    >
      {children}
    </MetaDataContext.Provider>
  );
};

export default MetaDataProvider;
