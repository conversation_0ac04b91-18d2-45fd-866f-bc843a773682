"use client";

import React from "react";
import { GlossaryContext, useGlossaryProvider } from "./use-glossary-category";

interface GlossaryProviderProps {
  children: React.ReactNode;
}

export const GlossaryProvider: React.FC<GlossaryProviderProps> = ({ children }) => {
  const glossaryData = useGlossaryProvider();

  return <GlossaryContext.Provider value={glossaryData}>{children}</GlossaryContext.Provider>;
};

export default GlossaryProvider;
