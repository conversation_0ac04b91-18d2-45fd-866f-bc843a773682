# Access Control System

This directory contains a unified access control system for the application.

## Components

### 1. `AccessContext` (from withAccessControl.tsx)

The main context that provides permissions throughout the app.

### 2. `CanAccess` Component

A declarative component for conditional rendering based on permissions.

```tsx
import { CanAccess } from "@/HOC";

<CanAccess resource="user" action="create" fallback={<span>No access</span>}>
  <CreateUserButton />
</CanAccess>;
```

### 3. `withAccessControl` HOC

A higher-order component for protecting entire components.

```tsx
import { withAccessControl } from "@/HOC";

const ProtectedComponent = withAccessControl(MyComponent, "user", "delete");
```

### 4. `useAccessContext` Hook

A custom hook to access permissions directly.

```tsx
import { useAccessContext, hasAccess } from "@/HOC";

const MyComponent = () => {
  const { permissions } = useAccessContext();
  const canEdit = hasAccess(permissions, "user", "edit");

  return <div>{canEdit ? "Can edit" : "Cannot edit"}</div>;
};
```

## Setup

The AccessProvider is already configured in the protected layout:

```tsx
// app/(protected)/layout.tsx
<AccessProvider>{children}</AccessProvider>
```

The AccessProvider automatically provides permissions to all child components. You can also import it directly:

```tsx
import { AccessProvider } from "@/components/access-provider";
```

## Permission Structure

```typescript
const permissions = {
  resource: {
    tenant: ["all", "create", "update", "delete", "read"],
    user: ["all", "create", "update", "delete", "read"],
  },
};
```

- `"all"` grants all permissions for that resource
- Specific actions can be listed individually
