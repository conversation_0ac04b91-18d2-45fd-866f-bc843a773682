"use client";
import React, { ReactNode } from "react";
import { useAccessContext, hasAccess } from "./access-provider";
import { ForbiddenPage } from "@/components/error-pages";
import type { Action, Resource } from "@/types";

// 🔑 CanAccess component
interface CanAccessProps {
  resource: Resource;
  privilege: Action;
  children: ReactNode;
  fallback?: ReactNode; // optional fallback UI
  showForbiddenPage?: boolean; // show full 403 page instead of fallback
}

export function CanAccess({
  resource,
  privilege,
  children,
  fallback = null,
  showForbiddenPage = false,
}: CanAccessProps) {
  const access = useAccessContext();
  const allowed = hasAccess(access.permissions, resource, privilege);

  if (!allowed) {
    if (showForbiddenPage) {
      return (
        <ForbiddenPage
          title="Access Denied"
          message={`You don't have permission to ${privilege} ${resource} resources. Please contact your administrator if you believe this is an error.`}
        />
      );
    }
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// // ✅ Example usage
// const DeleteUserButton = () => <button>Delete User</button>;

// export default function App() {
//   // Simulated API response
//   const permissions: Permissions = {
//     resource: {
//       tenant: ["all"],
//       user: ["create", "read"], // no "delete"
//     },
//   };

//   return (
//     <AccessContext.Provider value={{ permissions }}>
//       <h1>Dashboard</h1>

//       <CanAccess resource="user" action="create">
//         <button>Create User</button>
//       </CanAccess>

//       <CanAccess resource="user" action="delete" fallback={<span>No delete permission</span>}>
//         <DeleteUserButton />
//       </CanAccess>
//     </AccessContext.Provider>
//   );
// }
