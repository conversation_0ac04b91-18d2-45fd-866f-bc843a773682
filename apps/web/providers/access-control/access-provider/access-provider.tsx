"use client";
import { all } from "axios";
import React, { createContext, ReactNode } from "react";
import { useMetaData } from "../../meta-data";
import type { RoleDefinition, ResourcePermissions, Resource, Action } from "@/types";

export type Permissions = {
  resources: ResourcePermissions;
};

export interface AccessContextValue {
  permissions: Permissions;
}

interface AccessProviderProps {
  children: ReactNode;
}

export const AccessContext = createContext<AccessContextValue | null>(null);

// Custom hook to use AccessContext
export function useAccessContext() {
  const context = React.useContext(AccessContext);
  if (!context) {
    throw new Error("useAccessContext must be used within an AccessProvider");
  }
  return context;
}

// Utility: check if action is allowed
export function hasAccess(permissions: Permissions, resource: Resource, action: Action): boolean {
  const allowedActions = permissions.resources?.[resource];
  if (!allowedActions) return false;

  // if (allowedActions.includes("all")) return true;
  return allowedActions.includes(action);
}

const AccessProvider: React.FC<AccessProviderProps> = ({ children }) => {
  const { metaData: user, isLoading } = useMetaData();

  // Provide empty permissions while loading, will update when metadata loads
  const permissions: RoleDefinition = user?.privileges || {};

  return <AccessContext.Provider value={{ permissions }}>{children}</AccessContext.Provider>;
};

export default AccessProvider;
