"use client";
import React from "react";
import { AlertTriangle } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { cn } from "@repo/ui/lib/utils"; // optional helper if you have it
import { useMetaData } from "@/providers/meta-data";
import { useTenant } from "@/providers/tenant-selector";

export const Impersonator: React.FC = () => {
  const { metaData } = useMetaData();
  const metaDataTenantId = metaData?.tenant.id;
  const { currentTenantData, setCurrentTenant } = useTenant();
  const currentTenantId = currentTenantData?.id;

  if (metaDataTenantId === currentTenantId) return null;

  const onStopImpersonation = () => {
    // TODO: Add logic to stop impersonation
    console.log("Stop impersonation");
    setCurrentTenant(metaData?.tenant!);
  };

  return (
    <div
      className={cn(
        "flex w-full items-center justify-between border-b border-amber-200 bg-amber-50 px-4 py-2 text-sm"
      )}
    >
      <div className="flex items-center gap-2 text-amber-800">
        <AlertTriangle className="h-4 w-4 text-amber-600" />
        <span>
          You are currently impersonating{" "}
          <strong className="font-semibold text-amber-900">{currentTenantData?.name}</strong>.
        </span>
      </div>
      <Button
        variant="destructive"
        size="sm"
        onClick={onStopImpersonation}
        className="bg-red-600 text-white hover:bg-red-700"
      >
        Stop Impersonation
      </Button>
    </div>
  );
};
