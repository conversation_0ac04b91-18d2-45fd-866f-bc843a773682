"use client";

import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useMetaData } from "../meta-data";
import { clearTenantSpecificStores } from "@/stores/store-manager";
import type { Tenant } from "@/types";

export type CurrentTenantData = Partial<Omit<Tenant, "currentActiveUsers">>;

interface TenantContextType {
  currentTenantId: string | null;
  currentTenantData: CurrentTenantData | null;
  setCurrentTenant: (tenant: CurrentTenantData | null) => void;
  setCurrentTenantId: (tenantId: string) => void;
  clearTenantData: () => void;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

const STORAGE_KEYS = {
  id: "currentTenantId",
  data: "currentTenantData",
} as const;

export function TenantProvider({ children }: { children: React.ReactNode }) {
  const [currentTenantId, setCurrentTenantId] = useState<string | null>(null);
  const [currentTenantData, setCurrentTenantData] = useState<CurrentTenantData | null>(null);
  const queryClient = useQueryClient();
  const { metaData, isLoading } = useMetaData();

  // --- Memoized helper functions ---
  const saveTenant = useCallback((tenant: CurrentTenantData | null) => {
    if (tenant) {
      localStorage.setItem(STORAGE_KEYS.data, JSON.stringify(tenant));
      localStorage.setItem(STORAGE_KEYS.id, tenant.id!);
    } else {
      localStorage.removeItem(STORAGE_KEYS.data);
      localStorage.removeItem(STORAGE_KEYS.id);
    }
  }, []);

  const loadTenant = useCallback((): CurrentTenantData | null => {
    const saved = localStorage.getItem(STORAGE_KEYS.data);
    if (!saved) return null;
    try {
      return JSON.parse(saved) as CurrentTenantData;
    } catch {
      localStorage.removeItem(STORAGE_KEYS.data);
      return null;
    }
  }, []);

  // --- Initialize tenant from storage or metadata ---
  useEffect(() => {
    const storedTenant = loadTenant();
    if (storedTenant) {
      setCurrentTenantData(storedTenant);
      setCurrentTenantId(storedTenant.id!);
      return;
    }

    const legacyTenantId = localStorage.getItem(STORAGE_KEYS.id);
    if (legacyTenantId) {
      setCurrentTenantId(legacyTenantId);
      return;
    }

    // Use tenant from metaData if available
    if (!isLoading && metaData?.tenant?.id) {
      setCurrentTenantData(metaData.tenant);
      setCurrentTenantId(metaData.tenant.id);
      saveTenant(metaData.tenant);
    }
  }, [metaData, isLoading, loadTenant, saveTenant]);

  // --- Core update logic ---
  const updateTenant = useCallback(
    (tenant: CurrentTenantData | null, tenantIdOverride?: string | null) => {
      const newTenantId = tenant?.id || tenantIdOverride || null;
      if (newTenantId === currentTenantId) return;

      setCurrentTenantId(newTenantId);
      setCurrentTenantData(tenant || null);
      saveTenant(tenant);

      // Clear tenant-specific Zustand stores to prevent data leakage
      clearTenantSpecificStores();

      queryClient.invalidateQueries();
    },
    [currentTenantId, saveTenant, queryClient]
  );

  const setCurrentTenant = useCallback(
    (tenant: CurrentTenantData | null) => updateTenant(tenant),
    [updateTenant]
  );

  const setCurrentTenantIdLegacy = useCallback(
    (tenantId: string | null) => updateTenant(null, tenantId),
    [updateTenant]
  );

  const clearTenantData = useCallback(() => updateTenant(null), [updateTenant]);

  // --- Memoized context value ---
  const contextValue = useMemo(
    () => ({
      currentTenantId,
      currentTenantData,
      setCurrentTenant,
      //deprecated
      setCurrentTenantId: setCurrentTenantIdLegacy,
      clearTenantData,
    }),
    [
      currentTenantId,
      currentTenantData,
      setCurrentTenant,
      setCurrentTenantIdLegacy,
      clearTenantData,
    ]
  );

  return <TenantContext.Provider value={contextValue}>{children}</TenantContext.Provider>;
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (!context) throw new Error("useTenant must be used within a TenantProvider");
  return context;
}
