"use client";
import React from "react";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { useRouter } from "next/navigation";
import { UploadTable } from "./upload-table";
import { FileDropZone } from "./file-drop-zone";
import { useUploadFiles } from "../hooks/use-upload-files";
import { BreadCrumbs } from "@/components/breadcrumbs";

interface UploadContainerProps {
  projectId: string;
}

export const UploadContainer = ({ projectId }: UploadContainerProps) => {
  const router = useRouter();
  const {
    files,
    selectedFiles,
    isUploading,
    uploadProgress,
    addFiles,
    removeFile,
    toggleFileSelection,
    selectAllFiles,
    clearSelection,
    startUpload,
    cancelUpload,
  } = useUploadFiles(projectId);

  const handleCancel = () => {
    // if (isUploading) {
    //   cancelUpload();

    // }
    clearSelection();
    // router.push(`/project/${projectId}`);
  };

  const handleUpload = () => {
    startUpload();
  };

  const hasSelectedFiles = selectedFiles.size > 0;

  return (
    <div className="flex h-full flex-row gap-4">
      {/* Main Upload Table - matching file-page styling */}
      <div className="bg-background flex flex-1 flex-col space-y-4 overflow-hidden rounded-xl px-6 pt-6">
        {/* Header - matching file-page header */}
        <div className="flex items-center justify-between">
          <BreadCrumbs path={["Upload Files"]} />
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="rounded-2xl text-gray-500"
              onClick={handleCancel}
              disabled={isUploading}
            >
              Clear All
              <Icons.x className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Table Container */}
        <div className="flex-1 overflow-hidden">
          <div className="flex h-full flex-col justify-between">
            <div className="flex-1">
              <UploadTable
                files={files}
                selectedFiles={selectedFiles}
                uploadProgress={uploadProgress}
                onFileSelect={toggleFileSelection}
                onSelectAll={selectAllFiles}
                onClearSelection={clearSelection}
                onRemoveFile={removeFile}
                isUploading={isUploading}
              />
            </div>

            {/* Upload Button - matching file-page classify button position */}
            <div className="flex flex-shrink-0 justify-end py-6">
              <Button
                onClick={handleUpload}
                disabled={!hasSelectedFiles || isUploading}
                className="w-100"
                variant="default"
              >
                {isUploading
                  ? "Uploading..."
                  : `Upload ${selectedFiles.size > 0 ? `(${selectedFiles.size})` : ""}`}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Drop Zone - always visible like sidebar */}
      <div className="w-[400px]">
        <FileDropZone onFilesAdded={addFiles} />
      </div>
    </div>
  );
};
