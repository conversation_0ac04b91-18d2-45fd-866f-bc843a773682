"use client";
import React, { useEffect } from "react";
import { AdvancedTable } from "@/components/advanced-table";
import { uploadTableConfig } from "../config/upload-table-config";
import type { UploadFile, UploadProgress } from "../types/upload.types";

interface UploadTableProps {
  files: UploadFile[];
  selectedFiles: Set<string>;
  uploadProgress: UploadProgress;
  onFileSelect: (fileId: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  onClearSelection: () => void;
  onRemoveFile: (fileId: string) => void;
  isUploading: boolean;
}

export const UploadTable = ({
  files,
  selectedFiles,
  uploadProgress,
  onFileSelect,
  onSelectAll,
  onClearSelection,
  onRemoveFile,
  isUploading,
}: UploadTableProps) => {
  // Merge upload progress with file data
  const filesWithProgress = files.map((file) => ({
    ...file,
    progress: uploadProgress[file.id]?.progress || 0,
    status: uploadProgress[file.id]?.status || file.status,
    error: uploadProgress[file.id]?.error || file.error,
  }));

  // Automatically deselect uploaded files
  useEffect(() => {
    const uploadedFiles = filesWithProgress.filter((file) => file.status === "uploaded");
    uploadedFiles.forEach((file) => {
      if (selectedFiles.has(file.id)) {
        onFileSelect(file.id, false);
      }
    });
  }, [filesWithProgress, selectedFiles, onFileSelect]);

  const handleRowSelect = (row: UploadFile, selected: boolean) => {
    onFileSelect(row.id, selected);
  };

  // Determine if a row should be disabled
  const isRowDisabled = (row: UploadFile) => {
    // Disable uploaded files and all files during upload
    return row.status === "uploaded" || isUploading;
  };

  const columns = uploadTableConfig({
    onRemoveFile,
    isUploading,
  });

  return (
    <div className="h-full overflow-auto">
      {files.length === 0 ? (
        <div className="flex h-full items-center justify-center text-gray-500">
          <div className="text-center">
            <p className="text-lg font-medium">No files selected</p>
            <p className="text-sm">Drag and drop files to get started</p>
          </div>
        </div>
      ) : (
        <AdvancedTable
          data={filesWithProgress}
          columns={columns}
          onRowSelect={handleRowSelect}
          selectedRows={selectedFiles}
          getRowId={(file) => file.id}
          isRowDisabled={isRowDisabled}
          className="mt-4"
        />
      )}
    </div>
  );
};
