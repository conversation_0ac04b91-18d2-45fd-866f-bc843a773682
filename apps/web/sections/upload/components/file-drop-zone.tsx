"use client";
import React, { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Icons } from "@repo/ui/components/icons";
import { processFiles as processZipFiles, ZipProcessor } from "@/lib/zip-processor";
import { formatFileSize, generateFileId } from "@/lib/file-utils";
import type { UploadFile } from "../types/upload.types";
import { Spinner } from "@repo/ui/components/spinner";

interface FileDropZoneProps {
  onFilesAdded: (files: UploadFile[]) => void;
}

export const FileDropZone = ({ onFilesAdded }: FileDropZoneProps) => {
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const MAX_SIZE = 1 * 1024 * 1024 * 1024; // 1GB

  const createUploadFile = useCallback(
    (file: File, path: string = "Non-classified"): UploadFile => ({
      id: generateFileId(),
      name: file.name,
      size: file.size,
      sizeFormatted: formatFileSize(file.size),
      path,
      status: "queue",
      file,
      dateTime: new Date().toLocaleString(),
    }),
    []
  );

  const processFiles = useCallback(
    async (files: File[]) => {
      setError(null);
      setIsProcessing(true);

      try {
        console.log("Starting file processing for", files.length, "files");

        // Validate file sizes first
        for (const file of files) {
          console.log(`Validating file: ${file.name} (${file.size} bytes)`);

          if (file.size > MAX_SIZE) {
            setError(`❌ ${file.name} exceeds 1GB`);
            return;
          }

          if (!ZipProcessor.isZipFile(file) && !ZipProcessor.isPdfFile(file)) {
            setError(`❌ ${file.name} is not a supported file type (PDF or ZIP)`);
            return;
          }
        }

        console.log("File validation passed, starting ZIP processing...");

        // Add timeout to prevent hanging
        const processWithTimeout = Promise.race([
          processZipFiles(files, {
            maxFileSize: MAX_SIZE,
            allowedExtensions: [".pdf"],
            maxDepth: 5, // Reduced from 10 to prevent deep recursion issues
          }),
          new Promise<never>((_, reject) =>
            setTimeout(
              () => reject(new Error("Processing timeout - file may be corrupted or too complex")),
              30000
            )
          ),
        ]);

        const processedFiles = await processWithTimeout;
        console.log("ZIP processing completed, found", processedFiles.length, "files");

        // Convert to UploadFile format
        const uploadFiles = processedFiles.map((processedFile) =>
          createUploadFile(processedFile.file, processedFile.path)
        );

        if (uploadFiles.length > 0) {
          onFilesAdded(uploadFiles);
        } else {
          setError("No PDF files found in the selected files");
        }
      } catch (error) {
        console.error("File processing error:", error);
        const errorMessage =
          error instanceof Error ? error.message : "An error occurred processing files";
        setError(`❌ ${errorMessage}`);
      } finally {
        setIsProcessing(false);
      }
    },
    [onFilesAdded, createUploadFile, MAX_SIZE]
  );

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        processFiles(acceptedFiles);
      }
    },
    [processFiles]
  );

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
      "application/zip": [".zip"],
      "application/x-zip-compressed": [".zip"],
    },
    maxSize: MAX_SIZE,
    multiple: true,
    disabled: isProcessing,
  });

  return (
    <div className="bg-background h-full space-y-4 rounded-xl p-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900">Add Files</h3>
        <p className="text-sm text-gray-500">Drag & drop or click to upload</p>
      </div>

      {/* Drop Zone */}
      <div
        {...getRootProps()}
        className={`flex h-[500px] cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 text-center transition-colors ${
          isDragActive
            ? "border-primary bg-primary/5"
            : isDragReject
              ? "border-red-500 bg-red-50"
              : "border-gray-300 hover:border-gray-400"
        } ${isProcessing ? "pointer-events-none opacity-50" : ""}`}
      >
        <input {...getInputProps()} />

        {isProcessing ? (
          <>
            <Spinner className="size-14" />
            <h3 className="mb-2 text-lg font-medium text-gray-900">Processing Files...</h3>
            <p className="mb-4 text-sm text-gray-500">Extracting and validating files</p>
          </>
        ) : (
          <>
            <Icons.upload className="mb-4 h-12 w-12 text-gray-400" />
            <h3 className="mb-2 text-lg font-medium text-gray-900">
              {isDragActive ? "Drop files here" : "Drag n Drop Files"}
            </h3>
            <p className="mb-4 text-sm text-gray-500">
              {isDragReject ? (
                "Only PDF and ZIP files are supported"
              ) : (
                <>
                  <span className="block">Add .pdf or .zip file to begin the process</span>
                  <span className="mt-1 block text-xs text-blue-700">
                    PDF documents and ZIP archives up to 1GB
                  </span>
                  <span className="mt-1 block text-xs text-green-700">
                    ZIP files are automatically extracted and organized
                  </span>
                </>
              )}
            </p>
          </>
        )}

        {error && <div className="mt-2 text-sm text-red-600">{error}</div>}
      </div>

      {/* Instructions */}
      {/* <div className="space-y-3">
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex items-center gap-2">
            <Icons.alertCircle className="h-4 w-4 text-blue-600" />
            <h4 className="font-medium text-blue-900">Supported Files</h4>
          </div>
          <p className="mt-1 text-sm text-blue-700">PDF documents and ZIP archives up to 1GB</p>
        </div>

        <div className="rounded-lg border border-green-200 bg-green-50 p-4">
          <div className="flex items-center gap-2">
            <Icons.check className="h-4 w-4 text-green-600" />
            <h4 className="font-medium text-green-900">Auto Processing</h4>
          </div>
          <p className="mt-1 text-sm text-green-700">
            ZIP files are automatically extracted and organized
          </p>
        </div>
      </div> */}
    </div>
  );
};
