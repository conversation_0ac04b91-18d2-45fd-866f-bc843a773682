export type FileStatus = "queue" | "uploading" | "uploaded" | "failed" | "cancelled";

export interface UploadFile {
  id: string;
  name: string;
  size: number;
  sizeFormatted: string;
  path: string;
  status: FileStatus;
  file: File | Blob;
  progress?: number;
  error?: string;
  dateTime: string;
}

export interface UploadProgress {
  [fileId: string]: {
    progress: number;
    status: FileStatus;
    error?: string;
  };
}

export interface FileUploadResponse {
  id: string;
  name: string;
  createdAt: Date;
  size: number;
  path: string | null;
  status: string;
}
