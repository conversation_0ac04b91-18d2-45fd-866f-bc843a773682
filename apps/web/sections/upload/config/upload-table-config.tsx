import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Icons } from "@repo/ui/components/icons";
import { Progress } from "@repo/ui/components/progress";
import type { UploadFile } from "../types/upload.types";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@repo/ui/components/tooltip";

interface UploadTableConfigProps {
  onRemoveFile: (fileId: string) => void;
  isUploading: boolean;
}

export const uploadTableConfig = ({
  onRemoveFile,
  isUploading,
}: UploadTableConfigProps): ColumnDef<UploadFile>[] => [
  {
    accessorKey: "name",
    header: "File Name",
    cell: ({ row }) => (
      <div className="max-w-[200px]">
        <div className="truncate font-medium text-gray-900">{row.original.name}</div>
      </div>
    ),
    maxSize: 300,
    meta: {
      isGrow: true,
    },
  },
  {
    accessorKey: "dateTime",
    header: "Date & Time",
    cell: ({ row }) => <div className="text-sm text-gray-600">{row.original.dateTime}</div>,
    size: 150,
    meta: {
      isGrow: true,
    },
  },
  {
    accessorKey: "sizeFormatted",
    header: "File Size",
    cell: ({ row }) => <div className="text-sm text-gray-600">{row.original.sizeFormatted}</div>,
    size: 100,
    meta: {
      isGrow: true,
    },
  },
  {
    accessorKey: "path",
    header: "File Path",
    cell: ({ row }) => (
      <div className="max-w-[150px]">
        <div className="truncate text-sm text-gray-600">{row.original.path}</div>
      </div>
    ),
    maxSize: 200,
    meta: {
      isGrow: true,
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const { status, progress = 0, error } = row.original;

      const getStatusBadge = () => {
        switch (status) {
          case "queue":
            return <Badge variant="secondary">Queue</Badge>;
          case "uploading":
            return (
              <div className="flex items-center gap-2">
                <Badge variant="default" className="bg-blue-500">
                  Uploading
                </Badge>
                <Progress value={progress} className="w-16" />
                <span className="text-xs text-gray-500">{Math.round(progress)}%</span>
              </div>
            );
          case "uploaded":
            return (
              <Badge variant="default" className="bg-green-500">
                Uploaded
              </Badge>
            );
          case "failed":
            return (
              <TooltipProvider>
                <div className="flex items-center gap-1">
                  <Badge variant="destructive">Failed</Badge>
                  {error && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Icons.alertCircle className="h-4 w-4 cursor-pointer text-red-500" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{error}</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
              </TooltipProvider>
            );
          case "cancelled":
            return <Badge variant="outline">Cancelled</Badge>;
          default:
            return <Badge variant="secondary">Unknown</Badge>;
        }
      };

      return <div className="flex items-center">{getStatusBadge()}</div>;
    },
    size: 200,
    meta: {
      isGrow: true,
    },
  },
  {
    id: "actions",
    header: "",
    cell: ({ row }) => {
      const isDisabled =
        row.original.status === "uploaded" || (isUploading && row.original.status === "uploading");

      return (
        <div className="flex items-center justify-end">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRemoveFile(row.original.id)}
            disabled={isDisabled}
            className="h-8 w-8 p-0 text-gray-400 hover:text-red-500 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <Icons.x className="h-4 w-4" />
          </Button>
        </div>
      );
    },
    size: 60,
    enableSorting: false,
  },
];
