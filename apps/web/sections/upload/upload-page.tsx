"use client";
import React from "react";
import { SearchBar } from "@/components/search-bar";
import { SubNavBar } from "@/components/sub-nav-bar";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { useRouter } from "next/navigation";
import { UploadContainer } from "./components/upload-container";

interface UploadPageProps {
  projectId: string;
}

export const UploadPage = ({ projectId }: UploadPageProps) => {
  const router = useRouter();

  const handleCancel = () => {
    router.push(`/project/${projectId}`);
  };

  return (
    <div className="flex h-full flex-col space-y-4">
      <SearchBar />

      <SubNavBar />

      <div className="min-w-0 flex-1">
        <UploadContainer projectId={projectId} />
      </div>
    </div>
  );
};
