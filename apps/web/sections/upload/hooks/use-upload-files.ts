"use client";
import { useState, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import pLimit from "p-limit";
import type { UploadFile, UploadProgress, FileStatus } from "../types/upload.types";
import { uploadFileToProject } from "../api/upload-file";

export const useUploadFiles = (projectId: string) => {
  const router = useRouter();
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({});
  const [isUploading, setIsUploading] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Limit concurrent uploads to 3
  const uploadLimit = pLimit(3);

  const addFiles = useCallback((newFiles: UploadFile[]) => {
    setFiles((prev) => [...prev, ...newFiles]);
    // Auto-select new files
    setSelectedFiles((prev) => {
      const newSet = new Set(prev);
      newFiles.forEach((file) => newSet.add(file.id));
      return newSet;
    });
  }, []);

  const removeFile = useCallback((fileId: string) => {
    setFiles((prev) => prev.filter((file) => file.id !== fileId));
    setSelectedFiles((prev) => {
      const newSet = new Set(prev);
      newSet.delete(fileId);
      return newSet;
    });
    setUploadProgress((prev) => {
      const newProgress = { ...prev };
      delete newProgress[fileId];
      return newProgress;
    });
  }, []);

  const toggleFileSelection = useCallback((fileId: string, selected: boolean) => {
    setSelectedFiles((prev) => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(fileId);
      } else {
        newSet.delete(fileId);
      }
      return newSet;
    });
  }, []);

  const selectAllFiles = useCallback(
    (selected: boolean) => {
      if (selected) {
        setSelectedFiles(new Set(files.map((file) => file.id)));
      } else {
        setSelectedFiles(new Set());
      }
    },
    [files]
  );

  const clearSelection = useCallback(() => {
    setSelectedFiles(new Set());
  }, []);

  const updateFileProgress = useCallback(
    (fileId: string, progress: number, status: FileStatus, error?: string) => {
      setUploadProgress((prev) => ({
        ...prev,
        [fileId]: { progress, status, error },
      }));
    },
    []
  );

  const uploadSingleFile = useCallback(
    async (file: UploadFile, signal: AbortSignal) => {
      try {
        updateFileProgress(file.id, 0, "uploading");

        const result = await uploadFileToProject({
          projectId,
          file: file.file,
          fileName: file.name,
          filePath: file.path,
          batchNumber: 1, // For now, using batch number 1 for all uploads
          onProgress: (progress) => {
            updateFileProgress(file.id, progress, "uploading");
          },
          signal,
        });

        if (signal.aborted) {
          updateFileProgress(file.id, 0, "cancelled");
          return;
        }

        updateFileProgress(file.id, 100, "uploaded");
        return result;
      } catch (error) {
        if (signal.aborted) {
          updateFileProgress(file.id, 0, "cancelled");
          return;
        }

        const errorMessage = error instanceof Error ? error.message : "Upload failed";
        updateFileProgress(file.id, 0, "failed", errorMessage);
        throw error;
      }
    },
    [projectId, updateFileProgress]
  );

  const startUpload = useCallback(async () => {
    if (selectedFiles.size === 0 || isUploading) return;

    setIsUploading(true);
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    const selectedFilesList = files.filter((file) => selectedFiles.has(file.id));

    try {
      // Upload files in parallel with concurrency limit
      const uploadPromises = selectedFilesList.map((file) =>
        uploadLimit(() => uploadSingleFile(file, signal))
      );

      await Promise.allSettled(uploadPromises);

      // Check if all selected files were uploaded successfully
      const allUploaded = selectedFilesList.every((file) => {
        const progress = uploadProgress[file.id];
        return progress?.status === "uploaded";
      });

      if (allUploaded && !signal.aborted) {
        // Redirect back to project page after successful upload
        setTimeout(() => {
          router.push(`/project/${projectId}`);
        }, 1000);
      }
    } catch (error) {
      console.error("Upload error:", error);
    } finally {
      setIsUploading(false);
      abortControllerRef.current = null;
    }
  }, [
    selectedFiles,
    files,
    isUploading,
    uploadLimit,
    uploadSingleFile,
    uploadProgress,
    router,
    projectId,
  ]);

  const cancelUpload = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsUploading(false);
  }, []);

  return {
    files,
    selectedFiles,
    uploadProgress,
    isUploading,
    addFiles,
    removeFile,
    toggleFileSelection,
    selectAllFiles,
    clearSelection,
    startUpload,
    cancelUpload,
  };
};
