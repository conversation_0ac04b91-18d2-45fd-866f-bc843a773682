import { instance } from "@/axios-instance";
import type { FileUploadResponse } from "../types/upload.types";

interface UploadFileParams {
  projectId: string;
  file: File | Blob;
  fileName: string;
  filePath: string;
  batchNumber?: number;
  onProgress?: (progress: number) => void;
  signal?: AbortSignal;
}

export const uploadFileToProject = async ({
  projectId,
  file,
  fileName,
  filePath,
  batchNumber = 1,
  onProgress,
  signal,
}: UploadFileParams): Promise<FileUploadResponse> => {
  const formData = new FormData();

  // Add metadata as JSON string (as expected by the API)
  const metadata = {
    path: filePath,
    batchNumber: batchNumber.toString(),
  };
  const metaDataString = JSON.stringify(metadata);
  console.log("metadata", metaDataString);
  formData.append("metadata", metaDataString);

  // Add file to form data
  formData.append("file", file, fileName);

  try {
    const response = await instance.post(`/projects/${projectId}/upload`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      signal,
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data.data;
  } catch (error) {
    if (signal?.aborted) {
      throw new Error("Upload cancelled");
    }

    if (error instanceof Error) {
      throw error;
    }

    throw new Error("Upload failed");
  }
};
