"use client";
import { useEffect, useState } from "react";
import Session from "supertokens-auth-react/recipe/session";
import { useRouter } from "next/navigation";
import { AppLoading } from "@/components/loading";

export function AuthRedirect({ children }: { children: React.ReactNode }) {
  const [checking, setChecking] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function checkSession() {
      const exists = await Session.doesSessionExist();
      if (exists) router.replace("/dashboard");
      else setChecking(false);
    }
    checkSession().catch(() => setChecking(false));
  }, [router]);

  if (checking) return <AppLoading message="Checking authentication..." />;

  return children;
}
