"use server";

import { redirect } from "next/navigation";
import { Suspense } from "react";
import { TryRefreshComponent } from "./try-refresh-component";
import { SessionAuthForNextJS } from "./components/protected-page/super-token-session-auth-wrapper";
import { getSSRSessionHelper } from "@/sections/auth/lib/session-helper";
import { AppLoading } from "@/components/loading";

import { ServerErrorPage } from "@/components/error-pages/server-error";

interface ProtectedPageProps {
  children: React.ReactNode;
}

async function AuthChecker({ children }: ProtectedPageProps) {
  const { accessTokenPayload, hasToken, error } = await getSSRSessionHelper();

  if (error) {
    return <ServerErrorPage />;
  }

  // `accessTokenPayload` will be undefined if it the session does not exist or has expired
  if (accessTokenPayload === undefined) {
    if (!hasToken) {
      /**
       * This means that the user is not logged in. If you want to display some other UI in this
       * case, you can do so here.
       */
      return redirect("/login");
    }

    /**
     * This means that the session does not exist but we have session tokens for the user. In this case
     * the `TryRefreshComponent` will try to refresh the session.
     *
     * To learn about why the 'key' attribute is required refer to: https://github.com/supertokens/supertokens-node/issues/826#issuecomment-2092144048
     */
    return <TryRefreshComponent key={Date.now()} />;
  }

  // Authenticated user — render protected content inside session boundary
  return <SessionAuthForNextJS>{children}</SessionAuthForNextJS>;
}

export async function ProtectedPage({ children }: ProtectedPageProps) {
  return (
    <Suspense fallback={<AppLoading message="Authenticating..." />}>
      <AuthChecker>{children}</AuthChecker>
    </Suspense>
  );
}
