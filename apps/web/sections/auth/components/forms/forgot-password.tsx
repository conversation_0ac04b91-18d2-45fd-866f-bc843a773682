"use client";
import * as React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import { Input } from "@repo/ui/components/input";
import EmailPassword from "supertokens-auth-react/recipe/emailpassword";
import { useRouter } from "next/navigation";
import logo from "@/public/LOGOPMS.png";
import Image from "next/image";
import { forgotPasswordSchema, ForgotPasswordFormValues } from "./validation/forgot-password";

export interface ForgotPasswordFormProps {}

const ForgotPasswordForm = React.forwardRef<HTMLDivElement, ForgotPasswordFormProps>(({}, ref) => {
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState<string | null>(null);

  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError: setFormError,
  } = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (data: ForgotPasswordFormValues) => {
    setError(null);
    setSuccess(null);

    try {
      const response = await EmailPassword.sendPasswordResetEmail({
        formFields: [{ id: "email", value: data.email }],
      });

      switch (response.status) {
        case "FIELD_ERROR": {
          const fieldError = response?.formFields[0];
          if (fieldError) {
            setFormError(fieldError.id as keyof ForgotPasswordFormValues, {
              type: "server",
              message: fieldError.error,
            });
          }
          break;
        }

        case "OK": {
          setSuccess("Password reset email sent! Check your inbox.");
          break;
        }

        case "PASSWORD_RESET_NOT_ALLOWED": {
          setError("Please verify your email before resetting your password.");
          break;
        }

        default: {
          setError("Something went wrong. Please try again.");
        }
      }
    } catch (err) {
      setError("Something went wrong. Please try again.");
    }
  };

  const handleBackToLogin = () => {
    router.replace("/login");
  };

  return (
    <div ref={ref} className="w-full max-w-lg">
      <Card className="w-full border-0 shadow-none">
        <CardHeader className="text-left">
          <div className="mb-1 flex flex-col items-start">
            <Image
              src={logo}
              alt="Cadet Labs"
              width={68}
              height={77}
              className="mr-2 object-cover"
            />
          </div>
          <div>
            {/* <h1 className="text-primary mb-0 text-3xl font-bold leading-snug">PMS Asset Builder</h1> */}
            <h1 className="text-primary text-2xl leading-snug">Forgot Password?</h1>
            <p className="mt-2 text-xs text-gray-500">
              No worries, we'll send you reset instructions
            </p>
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="relative">
              <Input
                id="email"
                type="email"
                placeholder="Email Address"
                disabled={isSubmitting}
                icon={<Icons.mail className="h-4 w-4" />}
                iconPosition="left"
                {...register("email")}
                className={(error || errors.email) && "border-red-500"}
              />

              {/* Shared message area */}
              <p
                className={`my-1 min-h-[1rem] text-xs transition-colors duration-200 ${
                  errors?.email || error
                    ? "text-red-500"
                    : success
                      ? "text-green-600"
                      : "text-transparent"
                }`}
              >
                {errors?.email?.message || error || success || "\u00A0"}
              </p>
            </div>

            <div className="mt-2">
              <Button type="submit" className="w-full rounded-lg" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    Sending reset email...
                  </>
                ) : (
                  <>
                    Reset Password
                    <Icons.arrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>

              <div className="mt-2 text-left">
                <button
                  type="button"
                  onClick={handleBackToLogin}
                  className="cursor-pointer text-xs text-gray-500 underline hover:text-gray-700"
                  disabled={isSubmitting}
                >
                  Remember your password? Back to login
                </button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
});

ForgotPasswordForm.displayName = "ForgotPasswordForm";
export { ForgotPasswordForm };
