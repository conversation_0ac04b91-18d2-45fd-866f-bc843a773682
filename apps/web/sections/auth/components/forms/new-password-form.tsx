"use client";
import * as React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import { Input } from "@repo/ui/components/input";
import EmailPassword from "supertokens-auth-react/recipe/emailpassword";
import { useRouter, useSearchParams } from "next/navigation";
import logo from "@/public/LOGOPMS.png";
import Image from "next/image";

import { newPasswordSchema, NewPasswordFormValues } from "./validation/new-password";

export interface NewPasswordFormProps {}

const NewPasswordForm = React.forwardRef<HTMLDivElement, NewPasswordFormProps>(({}, ref) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState<string | null>(null);

  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError: setFormError,
  } = useForm<NewPasswordFormValues>({
    resolver: zodResolver(newPasswordSchema),
  });

  const onSubmit = async (data: NewPasswordFormValues) => {
    setError(null);
    setSuccess(null);

    if (!token) {
      setError("Invalid reset token. Please request a new password reset link.");
      return;
    }

    try {
      const response = await EmailPassword.submitNewPassword({
        formFields: [{ id: "password", value: data.password }],
      });

      switch (response.status) {
        case "FIELD_ERROR": {
          const fieldError = response?.formFields[0];
          if (fieldError) {
            setFormError(fieldError.id as keyof NewPasswordFormValues, {
              type: "server",
              message: fieldError.error,
            });
          }
          break;
        }

        case "RESET_PASSWORD_INVALID_TOKEN_ERROR": {
          setError("This password reset link has expired. Please request a new one.");
          break;
        }

        case "OK": {
          setSuccess("Password updated successfully! Redirecting to login...");
          setTimeout(() => {
            router.replace("/login");
          }, 2000);
          break;
        }

        default: {
          setError("Something went wrong. Please try again.");
        }
      }
    } catch (err) {
      setError("Something went wrong. Please try again.");
    }
  };

  // If no token, redirect to forgot password
  React.useEffect(() => {
    if (!token) {
      router.push("/forgot-password");
    }
  }, [token, router]);

  if (!token) {
    return null; // Will redirect
  }

  return (
    <div ref={ref} className="w-full max-w-lg">
      <Card className="w-full border-0 shadow-none">
        <CardHeader className="text-left">
          <div className="mb-1 flex flex-col items-start">
            <Image
              src={logo}
              alt="Cadet Labs"
              width={68}
              height={77}
              className="mr-2 object-cover"
            />
          </div>
          <div>
            {/* <h1 className="text-primary mb-0 text-3xl leading-snug font-bold">PMS Asset Builder</h1> */}
            <h1 className="text-primary text-2xl leading-snug">Set New Password</h1>
            <p className="mt-2 text-xs text-gray-500">Please enter your new password below</p>
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Password */}
            <div>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="New Password"
                  disabled={isSubmitting}
                  icon={<Icons.lock className="h-4 w-4" />}
                  iconPosition="left"
                  {...register("password")}
                  className={(error || errors.password) && "border-red-500"}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 hover:text-gray-600"
                  disabled={isSubmitting}
                >
                  {showPassword ? (
                    <Icons.eyeOff className="h-4 w-4" />
                  ) : (
                    <Icons.eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              <p className="my-1 text-xs text-red-500">{errors?.password?.message || "\u00A0"}</p>
            </div>

            {/* Confirm Password */}
            <div>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm New Password"
                  disabled={isSubmitting}
                  icon={<Icons.lock className="h-4 w-4" />}
                  iconPosition="left"
                  {...register("confirmPassword")}
                  className={(error || errors.confirmPassword) && "border-red-500"}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 hover:text-gray-600"
                  disabled={isSubmitting}
                >
                  {showConfirmPassword ? (
                    <Icons.eyeOff className="h-4 w-4" />
                  ) : (
                    <Icons.eye className="h-4 w-4" />
                  )}
                </button>
              </div>

              {/* Shared message area for error/success */}
              <p
                className={`my-1 min-h-[1rem] text-xs transition-colors duration-200 ${
                  errors?.confirmPassword || error
                    ? "text-red-500"
                    : success
                      ? "text-green-600"
                      : "text-transparent"
                }`}
              >
                {errors?.confirmPassword?.message || error || success || "\u00A0"}
              </p>
            </div>

            <div className="mt-2">
              <Button
                type="submit"
                className="w-full rounded-lg"
                disabled={isSubmitting || !!success}
              >
                {isSubmitting ? (
                  <>
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    Setting Password...
                  </>
                ) : (
                  <>
                    Set New Password
                    <Icons.arrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
});

NewPasswordForm.displayName = "NewPasswordForm";
export { NewPasswordForm };
