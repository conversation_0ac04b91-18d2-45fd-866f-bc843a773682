"use client";

import React, { useEffect, useState } from "react";
import { verifyEmail } from "supertokens-auth-react/recipe/emailverification";
import { useSearchParams, useRouter } from "next/navigation";

export function VerifyEmailPage() {
  const [status, setStatus] = useState<
    "base" | "checking" | "loading" | "success" | "error" | "invalid"
  >("checking");
  const [message, setMessage] = useState("");
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    async function handleEmailVerification() {
      try {
        const token = searchParams.get("token");

        if (!token) {
          // router.push("/");
          setStatus("base");
          setMessage("Check Email For Verification Link...");
          return;
        }

        // Token exists - attempt verification
        setStatus("loading");
        setMessage("🔄 Verifying your email...");

        const response = await verifyEmail();
        const data = await response.fetchResponse.json();

        switch (response.status) {
          case "EMAIL_VERIFICATION_INVALID_TOKEN_ERROR": {
            setStatus("invalid");
            setMessage(
              "❌ This verification link has expired or is invalid. Please request a new verification email."
            );
            break;
          }

          case "OK": {
            setStatus("success");
            setMessage("✅ Your email has been verified successfully!");

            // Redirect after a brief delay to show success message
            setTimeout(() => {
              router.push(
                `/auth/reset-password?token=${encodeURIComponent(data?.token)}&rid=reset-password`
              );
            }, 2000);
            break;
          }

          default: {
            setStatus("error");
            setMessage(`❌ Verification failed. Please try again.`);
          }
        }
      } catch (err: any) {
        console.error("Email verification error:", err);
        setStatus("error");

        if (err?.isSuperTokensGeneralError === true) {
          setMessage(`❌ ${err.message}`);
        } else {
          setMessage("❌ Something went wrong during verification. Please try again.");
        }
      }
    }

    handleEmailVerification();
  }, [searchParams]);

  const getStatusColor = () => {
    switch (status) {
      case "success":
        return "text-green-600";
      case "error":
      case "invalid":
        return "text-red-600";
      case "loading":
        return "text-blue-600";
      default:
        return "text-gray-700";
    }
  };

  const getIconForStatus = () => {
    switch (status) {
      case "success":
        return "✅";
      case "error":
      case "invalid":
        return "❌";
      case "loading":
        return "🔄";
      default:
        return "📧";
    }
  };

  const handleRetryAction = () => {
    if (status === "invalid") {
      router.push("/resend-verification");
    } else if (status === "error") {
      // Retry verification
      window.location.reload();
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md rounded-2xl bg-white p-8 text-center shadow-lg">
        <div className="mb-6">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
            <span className="text-2xl">{getIconForStatus()}</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-800">Email Verification</h1>
        </div>

        {status !== "checking" && (
          <>
            <p className={`mb-6 text-lg font-medium ${getStatusColor()}`}>{message}</p>

            {/* Loading state */}
            {status === "loading" && (
              <div className="flex items-center justify-center">
                <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
              </div>
            )}

            {/* Success state with countdown */}
            {status === "success" && (
              <div className="space-y-4">
                <div className="flex items-center justify-center">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-green-600 border-t-transparent"></div>
                </div>
                <p className="text-sm text-gray-500">Redirecting to set password...</p>
              </div>
            )}

            {/* Error states with action buttons */}
            {(status === "error" || status === "invalid") && (
              <div className="space-y-4">
                {/* <button
                  onClick={handleRetryAction}
                  className="w-full rounded-lg bg-blue-600 px-4 py-2 font-medium text-white transition-colors hover:bg-blue-700"
                >
                  {status === "error" ? "Try Again" : "Get New Verification Link"}
                </button>

                <button
                  onClick={() => router.push("/login")}
                  className="w-full rounded-lg border border-gray-300 px-4 py-2 font-medium text-gray-700 transition-colors hover:bg-gray-50"
                >
                  Back to Login
                </button> */}
              </div>
            )}
          </>
        )}

        {/* Initial checking state */}
        {status === "checking" && (
          <div className="flex items-center justify-center">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
          </div>
        )}
      </div>
    </div>
  );
}
