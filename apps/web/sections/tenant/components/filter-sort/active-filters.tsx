"use client";

import React from "react";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { useTenantFilterStore, useTenantFilterSelectors } from "@/stores/tenant-filter-store";
import type { TenantFilters } from "@/types";

const filterLabels: Record<keyof TenantFilters, string> = {
  name: "Name",
  code: "Code",
  subscriptionType: "Subscription",
  status: "Status",
};

const ActiveFilters: React.FC = () => {
  const filters = useTenantFilterStore((state) => state.filters);
  const removeFilter = useTenantFilterStore((state) => state.removeFilter);
  const clearAllFilters = useTenantFilterStore((state) => state.clearAllFilters);
  const { hasActiveFilters } = useTenantFilterSelectors();

  if (!hasActiveFilters) return null;

  const renderFilterBadge = (key: keyof TenantFilters, value: string | string[] | undefined) => {
    if (!value) return null;

    if (Array.isArray(value)) {
      return value.map((item) => (
        <Badge key={`${key}-${item}`} className="flex items-center gap-1">
          {filterLabels[key]}: {item}
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0 hover:bg-transparent"
            onClick={() => removeFilter(key, item)}
          >
            <Icons.x className="h-3 w-3" />
          </Button>
        </Badge>
      ));
    }

    return (
      <Badge key={key} className="flex items-center gap-1">
        {filterLabels[key]}: {value}
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0 hover:bg-transparent"
          onClick={() => removeFilter(key)}
        >
          <Icons.x className="h-3 w-3" />
        </Button>
      </Badge>
    );
  };

  return (
    <div className="flex flex-wrap items-center gap-2 rounded-lg bg-gray-50 p-4">
      {Object.entries(filters).map(([key, value]) =>
        renderFilterBadge(key as keyof TenantFilters, value)
      )}

      <Button variant="outline" size="sm" onClick={clearAllFilters} className="ml-2 text-xs">
        Clear All
      </Button>
    </div>
  );
};

export default ActiveFilters;
