"use client";

import React from "react";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { useTenantFilterStore, useTenantFilterSelectors } from "@/stores/tenant-filter-store";

const sortFieldLabels: Record<string, string> = {
  name: "Name",
  code: "Code",
  adminName: "Admin Name",
  adminMailId: "Admin Email",
  subscriptionType: "Subscription",
};

const ActiveSort: React.FC = () => {
  const sort = useTenantFilterStore((state) => state.sort);
  const clearSort = useTenantFilterStore((state) => state.clearSort);
  const { hasActiveSort } = useTenantFilterSelectors();

  if (!hasActiveSort) return null;

  const fieldLabel = sort?.field ? sortFieldLabels[sort.field] || sort.field : "";
  const DirectionIcon = sort!.direction === "asc" ? Icons.arrowUpIcon : Icons.arrowDownIcon;

  return (
    <div className="flex flex-wrap items-center gap-2 rounded-lg bg-gray-50 p-4">
      <span className="text-muted-foreground text-sm">Sorted by:</span>
      <Badge className="flex items-center space-x-1">
        <span>{fieldLabel}</span>
        <DirectionIcon className="h-3 w-3" />
        <Button
          variant="ghost"
          size="sm"
          className="h-auto p-0 hover:bg-transparent"
          onClick={clearSort}
        >
          <Icons.x className="h-3 w-3" />
        </Button>
      </Badge>
    </div>
  );
};

export default ActiveSort;
