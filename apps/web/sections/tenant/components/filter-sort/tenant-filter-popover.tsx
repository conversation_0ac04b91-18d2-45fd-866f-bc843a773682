"use client";

import React, { useState, useCallback } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { Badge } from "@repo/ui/components/badge";
import { useTenantFilterStore, useTenantFilterSelectors } from "@/stores/tenant-filter-store";
import type { TenantFilters } from "@/types";
import TenantFilterForm from "../forms/tenant-filter-form";

const TenantFilterPopover: React.FC = () => {
  const filters = useTenantFilterStore((state) => state.filters);
  const setFilters = useTenantFilterStore((state) => state.setFilters);
  const clearAllFilters = useTenantFilterStore((state) => state.clearAllFilters);
  const { activeFilterCount } = useTenantFilterSelectors();

  const [isOpen, setIsOpen] = useState(false);

  const handleApply = useCallback(
    (filters: TenantFilters) => {
      setFilters(filters);
      setIsOpen(false);
    },
    [setFilters]
  );

  const handleCancel = useCallback(() => {
    setIsOpen(false);
  }, []);

  const handleClearAll = useCallback(() => {
    clearAllFilters();
    setIsOpen(false);
  }, [clearAllFilters]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="relative rounded-2xl text-gray-500">
          Filter
          <Icons.filter className="h-4 w-4" />
          {activeFilterCount > 0 && (
            <Badge
              variant="default"
              className="absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs"
            >
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>

      <PopoverContent className="h-[500px] w-80 overflow-auto p-4" align="end">
        <TenantFilterForm
          defaultValues={filters}
          onApply={handleApply}
          onCancel={handleCancel}
          onClearAll={handleClearAll}
        />
      </PopoverContent>
    </Popover>
  );
};

export default TenantFilterPopover;
