import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@repo/ui/components/dialog";
import AddTenantForm from "../forms/add-tenant-form";

interface AddTenantDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export default function AddTenantDialog({ open, setOpen }: AddTenantDialogProps) {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="sm:max-w-[700px]"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-center">Tenant Registry</DialogTitle>
        </DialogHeader>
        <AddTenantForm setOpen={setOpen} />
      </DialogContent>
    </Dialog>
  );
}
