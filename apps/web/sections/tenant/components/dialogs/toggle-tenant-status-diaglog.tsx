"use client";

import { useActivateTenant, useDeactivateTenant } from "@/sections/tenant/hooks/api/index";
import { Button } from "@repo/ui/components/button";
import { Dialog, DialogContent, DialogTitle } from "@repo/ui/components/dialog";
import { Input } from "@repo/ui/components/input";
import React from "react";

import { type Tenant } from "@/types";
import { toast } from "sonner";

interface ToggleTenantStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedTenant: Tenant | undefined;
}

const ToggleTenantStatusDialog: React.FC<ToggleTenantStatusDialogProps> = ({
  open,
  onOpenChange,
  selectedTenant,
}) => {
  const activateTenant = useActivateTenant();
  const deactivateTenant = useDeactivateTenant();

  const handleConfirm = () => {
    if (!selectedTenant) return;

    if (selectedTenant.status === "Active") {
      deactivateTenant.mutate(
        { id: selectedTenant.id },
        {
          onSuccess: () => {
            onOpenChange(false);
            toast.success("Tenant de-activated successfully");
          },
          onError: (err) => {
            toast.error(err.message);
          },
        }
      );
    } else {
      activateTenant.mutate(
        { id: selectedTenant.id },
        {
          onSuccess: () => {
            onOpenChange(false);
            toast.success("Tenant activated successfully");
          },
          onError: (err) => {
            toast.error(err.message);
          },
        }
      );
    }
  };

  const isLoading = activateTenant.isPending || deactivateTenant.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="text-center">
          {selectedTenant?.status === "Active" ? "Deactivate Tenant" : "Activate Tenant"}
        </DialogTitle>
        <p className="mt-2 text-center text-sm text-gray-600">
          Notification will be sent to the registered tenant admin email.
        </p>
        <div className="flex justify-between gap-2">
          <Input value={"Sending Email To All Admins"} disabled />

          <Button onClick={handleConfirm} disabled={isLoading}>
            {isLoading ? "Confirming..." : "Confirm"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ToggleTenantStatusDialog;
