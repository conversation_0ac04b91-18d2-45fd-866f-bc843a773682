"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@repo/ui/components/dialog";
import EditTenantForm from "../forms/edit-tenant-form";
import { Tenant } from "@/types";

interface EditTenantDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedTenant: Tenant | undefined;
}

const EditTenantDialog: React.FC<EditTenantDialogProps> = ({
  open,
  onOpenChange,
  selectedTenant,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[700px]"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-center">Edit Tenant</DialogTitle>
        </DialogHeader>
        {selectedTenant ? (
          <EditTenantForm selectedTenant={selectedTenant} onClose={() => onOpenChange(false)} />
        ) : (
          <p className="text-muted-foreground">No tenant selected</p>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditTenantDialog;
