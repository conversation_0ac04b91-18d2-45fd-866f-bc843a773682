"use client";

import React, { useState } from "react";
import { SearchBar } from "@/components/search-bar";
import { SubNavBar } from "@/components/sub-nav-bar";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@repo/ui/components/dialog";
import AddTenantForm from "./forms/add-tenant-form";
import { Icons } from "@repo/ui/components/icons";
import { Button } from "@repo/ui/components/button";
import { useTenantFilterStore, useTenantFilterSelectors } from "@/stores/tenant-filter-store";

const TenantSearchHeader: React.FC = () => {
  const setSearch = useTenantFilterStore((state) => state.setSearch);
  const { searchValue } = useTenantFilterSelectors();

  return (
    <SearchBar value={searchValue} onChange={setSearch} placeholder="Search tenants by name..." />
  );
};

export default TenantSearchHeader;
