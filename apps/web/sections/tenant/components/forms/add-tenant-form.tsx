"use client";
import { useCreateTenant } from "@/sections/tenant/hooks/api";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import React from "react";
import { Controller, useForm } from "react-hook-form";
import { toast } from "sonner";
import { twMerge } from "tailwind-merge";
import { addTenantValidation, CreateTenantPayload } from "./validation/add-tenant-validation";

type AddTenantFormProps = {
  setOpen: (open: boolean) => void;
};

const AddTenantForm: React.FC<AddTenantFormProps> = ({ setOpen }) => {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors },
  } = useForm<CreateTenantPayload>({
    resolver: zodResolver(addTenantValidation),
    defaultValues: { maxUsers: 1 },
  });

  const userCapacity = watch("maxUsers");

  const createTenant = useCreateTenant();

  const { isPending } = createTenant;

  const onSubmit = (data: CreateTenantPayload) => {
    const payload = {
      ...data,
    };
    createTenant.mutate(payload, {
      onSuccess: () => {
        setOpen(false);
        toast.success("Tenant created successfully");
      },
      onError: (err) => {
        toast.error(err.message);
      },
    });
  };

  // Increment / Decrement
  const increment = () => setValue("maxUsers", (userCapacity || 0) + 1);
  const decrement = () => setValue("maxUsers", Math.max(1, (userCapacity || 1) - 1));

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="p-2">
      <div className="grid grid-cols-1 gap-x-6 gap-y-2 md:grid-cols-2">
        {/* Tenant Code */}
        {/* <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Tenant Code <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("code")}
            placeholder="Company Name"
            className={twMerge("h-10", errors.code && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.code?.message || "\u00A0"} </p>
        </div> */}

        <div className="col-span-2 flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Tenant Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("name")}
            placeholder="Tenant Name"
            className={twMerge("h-10", errors.name && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.name?.message || "\u00A0"} </p>
        </div>

        {/* Admin Name */}
        <div className="col-span-2 flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Admin Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("adminName", { required: "Admin name is required" })}
            placeholder="Admin Name"
            className={twMerge("h-10", errors.adminName && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.adminName?.message || "\u00A0"} </p>
        </div>

        {/* Admin Email */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Email ID <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("adminEmailId")}
            placeholder="Admin Email"
            className={twMerge("h-10", errors.adminEmailId && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.adminEmailId?.message || "\u00A0"} </p>
        </div>

        {/* Contact Number */}
        {/* <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Contact Number<span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("adminContactNumber")}
            placeholder="Contact Number"
            className={twMerge("h-10", errors.adminContactNumber && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.adminContactNumber?.message || "\u00A0"} </p>
        </div> */}

        {/* Subscription Type - Dropdown */}
        <div className="flex w-full flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Subscription <span className="ml-1 text-red-500">*</span>
          </label>

          <Controller
            name="subscriptionType"
            control={control}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger
                  className={twMerge("h-10 w-full", errors.subscriptionType && "border-red-500")}
                >
                  <SelectValue placeholder="Select Subscription" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Trial">Trial</SelectItem>
                  <SelectItem value="Standard">Standard</SelectItem>
                  <SelectItem value="Premium">Premium</SelectItem>
                </SelectContent>
              </Select>
            )}
          />

          <p className="text-xs text-red-500">{errors?.subscriptionType?.message || "\u00A0"}</p>
        </div>

        {/* User Capacity with + / - */}
        <div className="mt-2 flex flex-col space-y-1">
          <div className="flex items-center justify-between">
            <label
              htmlFor="maxUsers"
              className="flex items-center text-sm font-medium text-gray-700"
            >
              User Cap
            </label>
            <div className="flex items-center gap-2">
              <Button type="button" variant="outline" onClick={decrement}>
                -
              </Button>
              <Input
                type="text"
                {...register("maxUsers")}
                value={userCapacity}
                readOnly
                className={twMerge("w-15 text-center", errors.maxUsers && "border-red-500")}
              />
              <Button type="button" variant="outline" onClick={increment}>
                +
              </Button>
            </div>
          </div>
          <p className="text-xs text-red-500">{errors?.maxUsers?.message || "\u00A0"} </p>
        </div>
      </div>

      <div className="flex justify-center">
        <Button type="submit" className="w-full px-8 md:w-auto" disabled={isPending}>
          {isPending ? "Registering..." : "Register Tenant"}
        </Button>
      </div>
    </form>
  );
};

export default AddTenantForm;
