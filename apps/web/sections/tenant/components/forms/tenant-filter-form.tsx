"use client";

import React from "react";
import { useForm, Controller } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Button } from "@repo/ui/components/button";
import type { TenantFilters, TenantStatus, TenantSubscriptionType } from "@/types";

export { type TenantFilters };

const subscriptionTypeOptions = [
  { value: "Trial", label: "Trial" },
  { value: "Standard", label: "Standard" },
  { value: "Premium", label: "Premium" },
];

const statusOptions = [
  { value: "Active", label: "Active" },
  { value: "Inactive", label: "Inactive" },
];

interface TenantFilterFormProps {
  defaultValues: TenantFilters;
  onApply: (filters: TenantFilters) => void;
  onCancel: () => void;
  onClearAll: () => void;
}

const TenantFilterForm: React.FC<TenantFilterFormProps> = ({
  defaultValues,
  onApply,
  onCancel,
  onClearAll,
}) => {
  const { control, handleSubmit, reset, register } = useForm<TenantFilters>({
    defaultValues,
  });

  const handleApply = (data: TenantFilters) => {
    onApply(data);
  };

  const handleClearAllClick = () => {
    reset({});
    onClearAll();
  };

  return (
    <form onSubmit={handleSubmit(handleApply)} className="space-y-4">
      <div className="space-y-2">
        <h4 className="font-medium leading-none">Filter Tenants</h4>
        <p className="text-muted-foreground text-sm">
          Apply filters to narrow down the tenant list.
        </p>
      </div>

      {/* Text Inputs */}
      <div className="space-y-3">
        <div className="space-y-2">
          <Label htmlFor="name">Tenant Name</Label>
          <Input id="name" {...register("name")} placeholder="Search by tenant name..." />
        </div>

        <div className="space-y-2">
          <Label htmlFor="code">Tenant Code</Label>
          <Input id="code" {...register("code")} placeholder="Search by tenant code..." />
        </div>
      </div>

      {/* Subscription Type */}
      <div className="space-y-2">
        <Label>Subscription Type</Label>
        <Controller
          name="subscriptionType"
          control={control}
          render={({ field }) => (
            <div className="space-y-2">
              {subscriptionTypeOptions.map((option) => {
                const checked =
                  field.value?.includes(option.value as TenantSubscriptionType) || false;
                return (
                  <div key={option.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`subscription-${option.value}`}
                      checked={checked}
                      onCheckedChange={(checked) => {
                        const current = field.value || [];
                        field.onChange(
                          checked
                            ? [...current, option.value]
                            : current.filter((v) => v !== option.value)
                        );
                      }}
                    />
                    <Label htmlFor={`subscription-${option.value}`} className="text-sm font-normal">
                      {option.label}
                    </Label>
                  </div>
                );
              })}
            </div>
          )}
        />
      </div>

      {/* Status */}
      <div className="space-y-2">
        <Label>Status</Label>
        <Controller
          name="status"
          control={control}
          render={({ field }) => (
            <div className="space-y-2">
              {statusOptions.map((option) => {
                const checked = field.value?.includes(option.value as TenantStatus) || false;
                return (
                  <div key={option.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${option.value}`}
                      checked={checked}
                      onCheckedChange={(checked) => {
                        const current = field.value || [];
                        field.onChange(
                          checked
                            ? [...current, option.value]
                            : current.filter((v) => v !== option.value)
                        );
                      }}
                    />
                    <Label htmlFor={`status-${option.value}`} className="text-sm font-normal">
                      {option.label}
                    </Label>
                  </div>
                );
              })}
            </div>
          )}
        />
      </div>

      {/* Actions */}
      <div className="flex justify-between pt-4">
        <Button type="button" variant="outline" size="sm" onClick={handleClearAllClick}>
          Clear All
        </Button>
        <div className="space-x-2">
          <Button type="button" variant="outline" size="sm" onClick={() => reset(defaultValues)}>
            Cancel
          </Button>
          <Button type="submit" size="sm">
            Apply
          </Button>
        </div>
      </div>
    </form>
  );
};

export default TenantFilterForm;
