import { z } from "zod";

export const editTenantValidation = z.object({
  code: z.string().optional(),
  name: z
    .string()
    .min(1, "Tenant name is required")
    .max(100, "Tenant name must be at most 100 characters"),
  // adminName: z
  //   .string()
  //   .min(1, "Admin name is required")
  //   .max(100, "Admin name must be at most 100 characters"),
  // adminEmailId: z.string().min(1, "Admin email is required").email("Invalid email format"),
  // adminContactNumber: z.string().regex(/^[0-9]{10}$/, "Contact number must be 10 digits"),
  subscriptionType: z.enum(["Trial", "Standard", "Premium"], {
    errorMap: () => ({ message: "Subscription type is required" }),
  }),

  maxUsers: z.number().min(1, "User capacity must be at least 1"),
});

export type UpdateTenantPayload = z.infer<typeof editTenantValidation>;
