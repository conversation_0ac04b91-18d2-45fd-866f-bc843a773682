"use client";

import { getCoreRowModel } from "@tanstack/react-table";
import React from "react";

import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { getColumns } from "../config/table-config";

import { CustomVirtualList } from "@/components/custom-virtual-list";
import { useTenant } from "@/providers/tenant-selector";
import { useTenantFilterStore } from "@/stores/tenant-filter-store";
import { Tenant } from "@/types";
import { useRouter } from "next/navigation";
import ActiveFilters from "./filter-sort/active-filters";
import ActiveSort from "./filter-sort/active-sort";
import TenantFilterPopover from "./filter-sort/tenant-filter-popover";
import TenantSortPopover from "./filter-sort/tenant-sort-popover";
import NoTenantData from "./no-data/no-tenant-data";
import { BreadCrumbs } from "@/components/breadcrumbs";

interface TenantTableProps {
  tenantData: Tenant[];
  onEndReached?: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  onRowClick: (tenant: Tenant) => void;
  onStatusChange: (tenant: Tenant) => void;
}

const TenantTable: React.FC<TenantTableProps> = ({
  tenantData,
  onEndReached,
  hasNextPage,
  isFetchingNextPage,
  onRowClick,
  onStatusChange,
}) => {
  const filters = useTenantFilterStore((state) => state.filters);

  const { setCurrentTenant } = useTenant();

  const router = useRouter();

  const columns = getColumns({
    onRowClick,
    router,
    setCurrentTenant,
    onStatusChange,
  });

  return (
    <div className="bg-background rounded-xl p-6">
      <div className="mx-auto">
        {/* Header */}
        <div className="mb-8 flex items-center justify-between">
          <BreadCrumbs path={["Tenant Management"]} />

          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="rounded-2xl text-gray-500">
              Export
              <Icons.download className="h-4 w-4" />
            </Button>
            <TenantSortPopover />
            <TenantFilterPopover />
          </div>
        </div>

        {/* Active Filters */}
        <ActiveFilters />

        {/* Active Sort */}
        <ActiveSort />

        {/* Table */}
        {tenantData.length === 0 ? (
          <NoTenantData filters={filters} />
        ) : (
          <CustomVirtualList
            options={{
              data: tenantData,
              columns,
              getCoreRowModel: getCoreRowModel(),
            }}
            onEndReached={onEndReached}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
          />
        )}
      </div>
    </div>
  );
};

export default TenantTable;
