"use client";

import { ErrorBoundary } from "@/components/error-boundary";
import { useTenantFilterStore } from "@/stores/tenant-filter-store";
import { Tenant } from "@/types";
import React from "react";
import { useFilteredTenants } from "../hooks/api";
import TenantTableSkeleton from "./skeleton/tenant-table-skeleton";
import TenantTable from "./tenant-table";

interface TenantListProps {
  onRowClick: (tenant: Tenant) => void;
  onStatusChange: (tenant: Tenant) => void;
}

const TenantList: React.FC<TenantListProps> = ({ onRowClick, onStatusChange }) => {
  const filters = useTenantFilterStore((state) => state.filters);
  const sort = useTenantFilterStore((state) => state.sort);

  const { tenants, isLoading, error, hasNextPage, isFetchingNextPage, refetch, handleScrollEnd } =
    useFilteredTenants({
      limit: 10,
      filters,
      sort,
    });

  if (isLoading) return <TenantTableSkeleton />;

  if (error) return <ErrorBoundary error={error} onRetry={() => refetch()} />;

  return (
    <TenantTable
      tenantData={tenants ?? []}
      onEndReached={handleScrollEnd}
      hasNextPage={hasNextPage}
      isFetchingNextPage={isFetchingNextPage}
      onRowClick={onRowClick}
      onStatusChange={onStatusChange}
    />
  );
};

export default TenantList;
