"use client";

import { SubNavBar } from "@/components/sub-nav-bar";
import { Tenant } from "@/types";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import React, { useCallback, useState } from "react";
import AddTenantDialog from "./components/dialogs/add-tenant-dialog";
import EditTenantDialog from "./components/dialogs/edit-tenant-dialog";
import ToggleTenantStatusDialog from "./components/dialogs/toggle-tenant-status-diaglog";
import TenantList from "./components/tenant-list";
import TenantSearchHeader from "./components/tenant-search-header";

const TenantPage: React.FC = () => {
  const [openAdd, setOpenAdd] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openStatus, setOpenStatus] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | undefined>();

  const handleRowClick = useCallback((tenant: Tenant) => {
    setSelectedTenant(tenant);
    setOpenEdit(true);
  }, []);

  const handleStatusChange = useCallback((tenant: Tenant) => {
    setSelectedTenant(tenant);
    setOpenStatus(true);
  }, []);
  return (
    <div className="flex h-full flex-col space-y-2">
      <TenantSearchHeader />
      <SubNavBar>
        <Button onClick={() => setOpenAdd(true)} className="bg-primary text-white">
          Add Tenant
          <Icons.plus />
        </Button>
      </SubNavBar>
      <TenantList onRowClick={handleRowClick} onStatusChange={handleStatusChange} />
      <AddTenantDialog open={openAdd} setOpen={setOpenAdd} />
      <EditTenantDialog
        open={openEdit}
        onOpenChange={setOpenEdit}
        selectedTenant={selectedTenant}
      />
      {/* Toggle Tenant Status Dialog */}
      <ToggleTenantStatusDialog
        open={openStatus}
        onOpenChange={setOpenStatus}
        selectedTenant={selectedTenant}
      />
    </div>
  );
};

export default TenantPage;
