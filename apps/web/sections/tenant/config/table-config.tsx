import { TableColumnHeader } from "@/components/custom-virtual-list";
import { Tenant, Action, Resource } from "@/types";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { Icons } from "@repo/ui/components/icons";
import { ColumnDef } from "@tanstack/react-table";
import { CanAccess } from "@/providers/access-control";

interface TenantTableConfigProps {
  onRowClick: (tenant: Tenant) => void;
  onStatusChange: (tenant: Tenant) => void;
  router: any;
  setCurrentTenant: (tenant: Tenant) => void;
}

export const getColumns = ({
  onRowClick,
  router,
  setCurrentTenant,
  onStatusChange,
}: TenantTableConfigProps) => {
  const columns: ColumnDef<Tenant>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => <TableColumnHeader column={column} title="Tenant Name" />,
      cell: ({ row }) => <p className="text-lg font-semibold">{row?.original?.name || "-"}</p>,
      enableSorting: false,
      minSize: 200,
      meta: {
        isGrow: true,
      },
    },
    {
      accessorKey: "code",
      header: ({ column }) => <TableColumnHeader column={column} title="Tenant Code" />,
      cell: ({ row }) => row?.original?.code || "-",
      enableSorting: false,
      minSize: 100,
      meta: {
        isGrow: true,
      },
    },

    // {
    //   accessorKey: "tenantId",
    //   header: ({ column }) => <TableColumnHeader column={column} title="Tenant ID" />,
    //   cell: ({ row }) => row?.original?.tenantId || "-",
    //   enableSorting: false,
    //   size: 200,
    // },
    // {
    //   accessorKey: "adminName",
    //   header: ({ column }) => <TableColumnHeader column={column} title="Admin Name" />,
    //   cell: ({ row }) => row?.original?.adminName || "-",
    //   enableSorting: false,
    //   size: 200,
    // },
    {
      accessorKey: "subscriptionType",
      header: ({ column }) => <TableColumnHeader column={column} title="Subscription" />,
      cell: ({ row }) => row?.original?.subscriptionType || "-",
      enableSorting: false,
      minSize: 200,
      meta: {
        isGrow: true,
      },
    },
    // {
    //   accessorKey: "adminMailId",
    //   header: ({ column }) => <TableColumnHeader column={column} title="Email" />,
    //   cell: ({ row }) => row?.original?.adminMailId || "-",
    //   enableSorting: false,
    //   size: 300,
    // },
    // {
    //   accessorKey: "adminContactNumber",
    //   header: ({ column }) => <TableColumnHeader column={column} title="Contact" />,
    //   cell: ({ row }) => row?.original?.adminContactNumber || "-",
    //   enableSorting: false,
    //   size: 200,
    // },
    {
      accessorKey: "maxActiveUsers",
      header: ({ column }) => <TableColumnHeader column={column} title="Users" />,
      cell: ({ row }) => (
        <p>
          {row?.original?.currentActiveUsers || "-"}/{row?.original?.maxActiveUsers || "-"}
        </p>
      ),
      enableSorting: false,
      minSize: 150,
      meta: {
        isGrow: true,
      },
    },
    {
      accessorKey: "status",
      minSize: 200,
      meta: {
        isGrow: true,
      },
      header: ({ column }) => <TableColumnHeader column={column} title="Status" />,
      cell: ({ row }) => {
        const status = row.original?.status;

        // Determine color based on status
        const bgColor = status === "Active" ? "bg-green-100" : "bg-red-100"; // inactive or any other fallback
        const color = status === "Active" ? "text-green-800" : "text-red-800";
        return (
          <Badge className={bgColor}>
            <span className={color}>{status}</span>
          </Badge>
        );
      },
      enableSorting: false,
    },
    {
      id: "actions",
      header: "",
      size: 50,
      cell: ({ row }) => {
        const tenant = row?.original;

        return (
          <CanAccess privilege={"update" as Action.Update} resource={"tenant" as Resource.Tenant}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <Icons.moreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    onRowClick(tenant);
                  }}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    setCurrentTenant(tenant);
                    router.push(`/users`);
                  }}
                >
                  Manage Users
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    onStatusChange(tenant);
                  }}
                >
                  {tenant?.status === "Active" ? "Deactivate Tenant" : "Activate Tenant"}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CanAccess>
        );
      },
    },
  ];

  return columns;
};
