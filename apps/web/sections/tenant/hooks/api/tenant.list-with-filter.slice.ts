"use client";

import { useCallback, useMemo } from "react";
import { useInfiniteQuery } from "@tanstack/react-query";
import { useTenantQueryKey, useHasTenant } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";
import type { BaseResponse, TenantListResponse } from "@/types";
import type { TenantFilters, TenantSort } from "@/types";

interface UseFilteredTenantsProps {
  limit?: number;
  filters?: TenantFilters;
  sort?: TenantSort;
}

export function useFilteredTenants({
  limit = 10,
  filters = {},
  sort,
}: UseFilteredTenantsProps = {}) {
  // Remove empty filters
  const activeFilters = useMemo(() => {
    const cleanFilters: TenantFilters = {};

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== "") {
        if (Array.isArray(value) && value.length > 0) {
          (cleanFilters as any)[key] = value;
        } else if (!Array.isArray(value)) {
          (cleanFilters as any)[key] = value;
        }
      }
    });

    return cleanFilters;
  }, [filters]);

  // Build request payload
  const buildRequestPayload = useCallback(
    (pageParam?: string) => {
      const payload: any = {
        limit,
        cursor: pageParam,
      };

      // Add filters if they exist
      if (Object.keys(activeFilters).length > 0) {
        payload.filters = activeFilters;
      }

      // Add sort if provided
      if (sort) {
        payload.sort = sort;
      }

      return payload;
    },
    [limit, activeFilters, sort]
  );

  // Create stable query key
  const baseQueryKey = useMemo(() => {
    const key = ["tenants", "filtered"];

    if (Object.keys(activeFilters).length > 0) {
      key.push(JSON.stringify(activeFilters));
    }

    if (sort) {
      key.push(JSON.stringify(sort));
    }

    key.push(limit.toString());

    return key;
  }, [activeFilters, sort, limit]);

  const queryKey = useTenantQueryKey(baseQueryKey);
  const hasTenant = useHasTenant();

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,

    error,
    refetch,
  } = useInfiniteQuery({
    queryKey,
    initialPageParam: undefined as string | undefined,
    queryFn: async ({ pageParam }: { pageParam?: string | undefined }) => {
      const payload = buildRequestPayload(pageParam);

      const response = await instance.post<BaseResponse<TenantListResponse>>(
        "/tenants/find",
        payload
      );

      return response?.data?.data;
    },
    getNextPageParam: (lastPage) => lastPage?.nextCursor ?? undefined,
    staleTime: 1000 * 60 * 1, // 5 minutes
    enabled: hasTenant,
  });

  // Flatten pages into one list
  const tenants = useMemo(() => {
    return data?.pages?.flatMap((page) => page?.tenants || []) ?? [];
  }, [data]);

  // Handle scroll end for infinite loading
  const handleScrollEnd = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  return {
    tenants,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    handleScrollEnd,
  };
}
