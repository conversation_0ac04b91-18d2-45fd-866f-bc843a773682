import { useQuery } from "@tanstack/react-query";
import { useTenantQuery<PERSON>ey, useHasTenant } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";
import type { ProjectFileResponse } from "@/types";

interface UseProjectFileParams {
  projectId: string;
  fileId: string;
  enabled?: boolean;
}

export function useProjectFile(
  projectId: string,
  fileId: string,
  options: { enabled?: boolean } = {}
) {
  const queryKey = useTenantQueryKey(["project-file", projectId, fileId]);
  const hasTenant = useHasTenant();

  return useQuery({
    queryKey,
    queryFn: async () => {
      // We'll use the existing file list endpoint to get file metadata
      // since there's no dedicated single file metadata endpoint
      const response = await instance.post<{
        success: boolean;
        data: {
          files: ProjectFileResponse[];
          hasMore: boolean;
          nextCursor?: string;
        };
        message: string;
      }>(`/projects/${projectId}/files/find`, {
        filters: { id: [fileId] },
        limit: 1,
      });

      const file = response.data.data.files[0];
      if (!file) {
        throw new Error("File not found");
      }

      return file;
    },
    enabled: hasTenant && !!projectId && !!fileId && (options.enabled !== false),
  });
}

// Alternative hook that directly constructs the file URL without metadata
export function useProjectFileUrl(projectId: string, fileId: string) {
  const hasTenant = useHasTenant();
  
  if (!hasTenant || !projectId || !fileId) {
    return null;
  }

  return `/api/v1/projects/${projectId}/files/${fileId}`;
}
