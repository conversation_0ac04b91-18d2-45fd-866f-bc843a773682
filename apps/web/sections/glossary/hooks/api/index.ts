// Glossary API hooks
import { useCreateGlossary } from "./glossary.create.slice";
import { useEditGlossary } from "./glossary.update.slice";
import { useActivateGlossary } from "./glossary.activate.slice";
import { useDeactivateGlossary } from "./glossary.deactivate.slice";

import { useFilteredGlossaries } from "./glossary.list-with-filter.slice";

export {
  // Glossary exports
  useCreateGlossary,
  useEditGlossary,
  useActivateGlossary,
  useDeactivateGlossary,
  useFilteredGlossaries,
};
