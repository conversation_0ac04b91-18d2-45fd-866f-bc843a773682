"use client";

import React, { useCallback, useState } from "react";
import GlossarySearchHeader from "./components/glossary-search-header";
import GlossaryList from "./components/glossary-list";
import { SubNavBar } from "@/components/sub-nav-bar";
import { CanAccess } from "@/providers/access-control";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { Action, Resource } from "@/types";
import { AddGlossaryDialog } from "./components/dialogs/add-glossary-dialog";
import { EditGlossaryDialog } from "./components/dialogs/edit-glossary-dialog";
import ToggleGlossaryStatusDialog from "./components/dialogs/toggle-glossary-status-dialog";
import { GlossaryResponse } from "@/types";

const GlossaryPage: React.FC = () => {
  const [openAdd, setOpenAdd] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openStatusToggle, setOpenStatusToggle] = useState(false);
  const [selectedGlossary, setSelectedGlossary] = useState<GlossaryResponse | undefined>();

  const handleRowClick = useCallback((glossaryCategory: GlossaryResponse) => {
    setSelectedGlossary(glossaryCategory);
    setOpenEdit(true);
  }, []);

  const handleStatusChange = useCallback((glossary: GlossaryResponse) => {
    setSelectedGlossary(glossary);
    setOpenStatusToggle(true);
  }, []);

  return (
    <div className="flex h-full flex-col space-y-2">
      <GlossarySearchHeader />

      <SubNavBar>
        <CanAccess privilege={"create" as Action.Create} resource={"glossary" as Resource.Glossary}>
          <Button onClick={() => setOpenAdd(true)} className="bg-primary text-white">
            Add Glossary
            <Icons.plus />
          </Button>
        </CanAccess>
      </SubNavBar>

      <GlossaryList
        onRowClick={handleRowClick}
        selectedGlossary={selectedGlossary}
        onStatusChange={handleStatusChange}
      />
      <AddGlossaryDialog open={openAdd} setOpen={setOpenAdd} />
      <EditGlossaryDialog
        open={openEdit}
        setOpen={setOpenEdit}
        selectedGlossary={selectedGlossary}
      />
      <ToggleGlossaryStatusDialog
        open={openStatusToggle}
        onOpenChange={setOpenStatusToggle}
        selectedGlossary={selectedGlossary}
      />
    </div>
  );
};

export default GlossaryPage;
