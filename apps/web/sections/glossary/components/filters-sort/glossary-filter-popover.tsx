"use client";

import React, { useState } from "react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Icons } from "@repo/ui/components/icons";
import GlossaryFilterForm from "../forms/glossary-filter-form";
import { useGlossaryFilterStore, useGlossaryFilterSelectors } from "@/stores/glossary-filter-store";

const GlossaryFilterPopover: React.FC = () => {
  const filters = useGlossaryFilterStore((state) => state.filters);
  const setFilters = useGlossaryFilterStore((state) => state.setFilters);
  const clearAllFilters = useGlossaryFilterStore((state) => state.clearAllFilters);
  const { hasActiveFilters, activeFilterCount } = useGlossaryFilterSelectors();

  const [isOpen, setIsOpen] = useState(false);

  const handleSubmit = (data: any) => {
    setFilters(data);
    setIsOpen(false);
  };

  const handleClear = () => {
    clearAllFilters();
    setIsOpen(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="relative rounded-2xl text-gray-500">
          Filter
          <Icons.filter className="ml-1 h-4 w-4" />
          {hasActiveFilters && (
            <Badge className="bg-primary absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs text-white">
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>

      <PopoverContent className="max-h-[500px] w-80 overflow-y-auto p-4" align="end">
        <GlossaryFilterForm
          defaultValues={filters}
          onSubmit={handleSubmit}
          onClear={handleClear}
          onCancel={() => setIsOpen(false)}
        />
      </PopoverContent>
    </Popover>
  );
};

export default GlossaryFilterPopover;
