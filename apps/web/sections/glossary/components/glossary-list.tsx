"use client";

import React from "react";
import GlossaryTable from "./glossary-table";
import GlossaryTableSkeleton from "./skeleton/glossary-table-skeleton";
import { ErrorBoundary } from "@/components/error-boundary";
import { useFilteredGlossaries } from "../hooks/api";
import { useGlossaryFilterStore } from "@/stores/glossary-filter-store";
import { GlossaryResponse } from "@/types";

interface GlossaryListProps {
  onRowClick: (glossaryCategory: GlossaryResponse) => void;
  selectedGlossary: GlossaryResponse | undefined;
  onStatusChange: (glossary: GlossaryResponse) => void;
}

const GlossaryList: React.FC<GlossaryListProps> = ({
  onRowClick,
  selectedGlossary,
  onStatusChange,
}) => {
  const filters = useGlossaryFilterStore((state) => state.filters);
  const sort = useGlossaryFilterStore((state) => state.sort);

  const { glossaries, isLoading, error, hasNextPage, isFetchingNextPage, refetch, fetchNextPage } =
    useFilteredGlossaries({
      limit: 10,
      filters,
      sort,
    });

  if (isLoading) return <GlossaryTableSkeleton />;

  if (error) return <ErrorBoundary error={error} onRetry={() => refetch()} />;

  return (
    <GlossaryTable
      glossaryData={glossaries ?? []}
      onEndReached={fetchNextPage}
      hasNextPage={hasNextPage}
      isFetchingNextPage={isFetchingNextPage}
      onRowClick={onRowClick}
      selectedGlossary={selectedGlossary}
      onStatusChange={onStatusChange}
    />
  );
};

export default GlossaryList;
