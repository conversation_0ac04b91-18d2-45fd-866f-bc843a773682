"use client";

import React from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@repo/ui/components/dialog";

import AddGlossaryForm from "../forms/add-glossary-form";

interface AddGlossaryDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const AddGlossaryDialog: React.FC<AddGlossaryDialogProps> = ({ open, setOpen }) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="sm:max-w-[700px]"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-center">Glossary Registry</DialogTitle>
        </DialogHeader>
        <AddGlossaryForm setOpen={setOpen} />
      </DialogContent>
    </Dialog>
  );
};
