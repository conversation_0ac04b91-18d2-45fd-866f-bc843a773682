"use client";

import { useActivateGlossary, useDeactivateGlossary } from "@/sections/glossary/hooks/api";
import { But<PERSON> } from "@repo/ui/components/button";
import { Dialog, DialogContent, DialogTitle } from "@repo/ui/components/dialog";
import React from "react";
import { GlossaryResponse } from "@/types";
import { toast } from "sonner";

interface ToggleGlossaryStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedGlossary: GlossaryResponse | undefined;
}

const ToggleGlossaryStatusDialog: React.FC<ToggleGlossaryStatusDialogProps> = ({
  open,
  onOpenChange,
  selectedGlossary,
}) => {
  const activateGlossary = useActivateGlossary();
  const deactivateGlossary = useDeactivateGlossary();

  const handleConfirm = () => {
    if (!selectedGlossary) return;

    if (selectedGlossary.status === "Active") {
      deactivateGlossary.mutate(selectedGlossary.id, {
        onSuccess: () => {
          onOpenChange(false);
          toast.success("Glossary deactivated successfully");
        },
        onError: (err: any) => {
          toast.error(err.message || "Failed to deactivate glossary");
        },
      });
    } else {
      activateGlossary.mutate(selectedGlossary.id, {
        onSuccess: () => {
          onOpenChange(false);
          toast.success("Glossary activated successfully");
        },
        onError: (err: any) => {
          toast.error(err.message || "Failed to activate glossary");
        },
      });
    }
  };

  const isLoading = activateGlossary.isPending || deactivateGlossary.isPending;
  const isDeactivating = selectedGlossary?.status === "Active";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="text-center">
          {isDeactivating ? "Deactivate Glossary" : "Activate Glossary"}
        </DialogTitle>
        <div className="space-y-4">
          <p className="text-center text-sm text-gray-600">
            Are you sure you want to {isDeactivating ? "deactivate" : "activate"} the glossary "
            <span className="font-semibold">{selectedGlossary?.name}</span>"?
          </p>
          {isDeactivating && (
            <p className="text-center text-xs text-red-600">
              Deactivating this glossary will make it unavailable for use.
            </p>
          )}
        </div>
        <div className="flex justify-between gap-2 pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={isLoading} variant="default">
            {isLoading ? "Processing..." : isDeactivating ? "Deactivate" : "Activate"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ToggleGlossaryStatusDialog;
