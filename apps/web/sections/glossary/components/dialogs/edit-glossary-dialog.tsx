"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@repo/ui/components/dialog";
import EditGlossaryForm from "../forms/edit-glossary-form";
import { GlossaryResponse } from "@/types";

// import { type Glossary } from "@/types";

interface EditGlossaryDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedGlossary: GlossaryResponse | undefined;
}

export const EditGlossaryDialog: React.FC<EditGlossaryDialogProps> = ({
  open,
  setOpen,
  selectedGlossary,
}) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="sm:max-w-[700px]"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-center">Edit Glossary</DialogTitle>
        </DialogHeader>
        {selectedGlossary ? (
          <EditGlossaryForm selectedGlossary={selectedGlossary} setOpen={setOpen} />
        ) : (
          <p className="text-muted-foreground">No glossary selected</p>
        )}
      </DialogContent>
    </Dialog>
  );
};
