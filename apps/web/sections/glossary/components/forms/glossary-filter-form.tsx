"use client";

import React from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Checkbox } from "@repo/ui/components/checkbox";
import {
  GlossaryType,
  GlossaryStatus,
  glossaryType,
  glossaryStatus,
  GlossaryFilters,
} from "@/types";

interface GlossaryFilterFormProps {
  defaultValues: GlossaryFilters;
  onSubmit: (data: GlossaryFilters) => void;
  onClear: () => void;
  onCancel: () => void;
}

const GlossaryFilterForm: React.FC<GlossaryFilterFormProps> = ({
  defaultValues,
  onSubmit,
  onClear,
  onCancel,
}) => {
  const { handleSubmit, control, reset, watch, register } = useForm<GlossaryFilters>({
    defaultValues: { ...defaultValues },
  });

  const handleClear = () => {
    reset({});
    onClear();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {/* Header */}
      <div className="space-y-2">
        <h4 className="font-medium leading-none">Filter Glossaries</h4>
        <p className="text-muted-foreground text-sm">
          Apply filters to narrow down the glossary list.
        </p>
      </div>

      {/* Name Filter */}
      <div className="space-y-2">
        <Label htmlFor="name-filter" className="text-sm font-medium">
          Name
        </Label>
        <Input {...register("name")} placeholder="Search by name..." />
      </div>

      {/* Type Filter */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Type</Label>
        <div className="space-y-2">
          {Object.entries(glossaryType).map(([key, value]) => (
            <Controller
              key={key}
              name="type"
              control={control}
              render={({ field }) => {
                const current = field.value || [];
                const checked = current.includes(value as GlossaryType);
                return (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${key}`}
                      checked={checked}
                      onCheckedChange={(isChecked) => {
                        if (isChecked) {
                          field.onChange([...current, value]);
                        } else {
                          field.onChange(current.filter((t: string) => t !== value));
                        }
                      }}
                    />
                    <Label htmlFor={`type-${key}`} className="cursor-pointer text-sm font-normal">
                      {key}
                    </Label>
                  </div>
                );
              }}
            />
          ))}
        </div>
      </div>

      {/* Status Filter */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Status</Label>
        <div className="space-y-2">
          {Object.entries(glossaryStatus).map(([key, value]) => (
            <Controller
              key={key}
              name="status"
              control={control}
              render={({ field }) => {
                const current = field.value || [];
                const checked = current.includes(value as GlossaryStatus);
                return (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${key}`}
                      checked={checked}
                      onCheckedChange={(isChecked) => {
                        if (isChecked) {
                          field.onChange([...current, value]);
                        } else {
                          field.onChange(current.filter((s: string) => s !== value));
                        }
                      }}
                    />
                    <Label htmlFor={`status-${key}`} className="cursor-pointer text-sm font-normal">
                      {key}
                    </Label>
                  </div>
                );
              }}
            />
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between pt-4">
        <Button variant="outline" size="sm" type="button" onClick={handleClear}>
          Clear All
        </Button>
        <div className="space-x-2">
          <Button variant="outline" size="sm" type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button size="sm" type="submit">
            Apply
          </Button>
        </div>
      </div>
    </form>
  );
};

export default GlossaryFilterForm;
