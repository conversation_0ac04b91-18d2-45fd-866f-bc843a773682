"use client";

import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@repo/ui/components/select";

import {
  UpdateGlossaryPayload,
  editGlossaryValidation,
} from "../../validation/edit-glossary-validation";

import { GlossaryResponse, glossaryType, userRole } from "@/types";
import { twMerge } from "tailwind-merge";
import { useMetaData } from "@/providers/meta-data";
import { useEditGlossary } from "../../hooks/api";
import { toast } from "sonner";

type EditGlossaryFormProps = {
  open?: boolean;
  setOpen: (open: boolean) => void;
  selectedGlossary: GlossaryResponse;
};

const EditGlossaryForm: React.FC<EditGlossaryFormProps> = ({ open, setOpen, selectedGlossary }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<UpdateGlossaryPayload>({
    resolver: zodResolver(editGlossaryValidation),
    defaultValues: {
      name: selectedGlossary.name,
      remark: selectedGlossary.remark!,
    },
  });

  const editGlossary = useEditGlossary();
  const { isPending } = editGlossary;

  const onSubmit = (data: UpdateGlossaryPayload) => {
    editGlossary.mutate(
      {
        ...data,
        id: selectedGlossary.id,
      },
      {
        onSuccess: () => {
          setOpen(false);
          toast.success("Glossary updated successfully");
          console.log("sssssss");
        },
        onError: (err) => {
          toast.error(err.message);
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl p-2">
      <div className="grid grid-cols-1 gap-x-6 gap-y-1 md:grid-cols-2">
        {/* First Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Glossary Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("name")}
            placeholder="Glossary Name"
            className={twMerge("h-10", errors.name && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.name?.message || "\u00A0"}</p>
        </div>

        {/* Type */}
        {/* <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Type <span className="ml-1 text-red-500">*</span>
          </label>
          <Controller
            name="type"
            control={control}
            render={({ field, fieldState }) => (
              <div className="w-full">
                <Select onValueChange={(val) => field.onChange(val)} value={field.value || ""}>
                  <SelectTrigger
                    className={twMerge("h-10 w-full", errors.type && "border-red-500")}
                  >
                    <SelectValue placeholder="Select Type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(glossaryType).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <p className="mt-1 text-xs text-red-500">
                  {fieldState?.error?.message || "\u00A0"}
                </p>
              </div>
            )}
          />
        </div> */}

        {/* Remarks */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Remark
            {/* <span className="ml-1 text-red-500">*</span> */}
          </label>
          <Input
            {...register("remark")}
            placeholder="Remarks"
            className={twMerge("h-10", errors.remark && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.remark?.message || "\u00A0"}</p>
        </div>
      </div>

      {/* Buttons */}
      <div className="flex w-full items-center justify-between">
        <Button
          type="button"
          variant="outline"
          className="px-8 md:w-auto"
          onClick={() => setOpen(false)}
          disabled={isPending}
        >
          Cancel
        </Button>
        <Button type="submit" className="px-8 md:w-auto" disabled={isPending}>
          {isPending ? "Saving..." : "Save"}
        </Button>
      </div>
    </form>
  );
};

export default EditGlossaryForm;
