"use client";

import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@repo/ui/components/select";
// import { useEditGlossary } from "@/sections/users/hooks/users-api";
import { toast } from "sonner";
import {
  UpdateGlossaryPayload,
  editGlossaryValidation,
} from "../../validation/edit-glossary-validation";

import { glossaryType, userRole } from "@/types";
import { twMerge } from "tailwind-merge";
import { useMetaData } from "@/providers/meta-data";
import { useCreateGlossary } from "../../hooks/api";
import {
  addGlossaryValidation,
  CreateGlossaryPayload,
} from "../../validation/add-glossary-validation";

type AddGlossaryFormProps = {
  open?: boolean;
  setOpen: (open: boolean) => void;
};

const AddGlossaryForm: React.FC<AddGlossaryFormProps> = ({ open, setOpen }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<CreateGlossaryPayload>({
    resolver: zodResolver(addGlossaryValidation), // ✅ use Zod schema
    defaultValues: {},
  });

  const createGlossary = useCreateGlossary();

  const { isPending } = createGlossary;

  const onSubmit = (data: CreateGlossaryPayload) => {
    createGlossary.mutate(
      {
        ...data,
      },
      {
        onSuccess: () => {
          setOpen(false);
          toast.success("Glossary updated successfully");
          console.log("sssssss");
        },
        onError: (err) => {
          toast.error(err.message);
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl p-2">
      <div className="grid grid-cols-1 gap-x-6 gap-y-1 md:grid-cols-2">
        {/* First Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Glossary Name
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("name")}
            placeholder="Glossary Name"
            className={twMerge("h-10", errors.name && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.name?.message || "\u00A0"}</p>
        </div>

        {/* Type */}

        {/* Remarks */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Remark
            {/* <span className="ml-1 text-red-500">*</span> */}
          </label>
          <Input
            {...register("remark")}
            placeholder="Remarks"
            className={twMerge("h-10", errors.remark && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.remark?.message || "\u00A0"}</p>
        </div>
      </div>

      {/* Buttons */}
      <div className="flex justify-center">
        <Button type="submit" className="w-full px-8 md:w-auto" disabled={isPending}>
          {isPending ? "Creating..." : "Create"}
        </Button>
      </div>
    </form>
  );
};

export default AddGlossaryForm;
