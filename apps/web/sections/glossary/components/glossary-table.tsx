"use client";

import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { getCoreRowModel } from "@tanstack/react-table";
import { useRouter } from "next/navigation";
import React, { useMemo, useState } from "react";

import { BreadCrumbs } from "@/components/breadcrumbs";
import { CustomVirtualList } from "@/components/custom-virtual-list";
import ActiveFilters from "./filters-sort/active-filters";
import ActiveSort from "./filters-sort/active-sort";
import GlossaryFilterPopover from "./filters-sort/glossary-filter-popover";
import GlossarySortPopover from "./filters-sort/glossary-sort-popover";
import NoGlossaryData from "./no-data/no-glossary-data";

import { useMetaData } from "@/providers/meta-data";
import { useTenant } from "@/providers/tenant-selector";
import { paths } from "@/routes/paths";
import { useGlossaryFilterStore } from "@/stores/glossary-filter-store";
import { GlossaryResponse } from "@/types";
import { getColumns } from "../config/table-config";

interface GlossaryTableProps {
  glossaryData: GlossaryResponse[];
  onEndReached?: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  onRowClick?: (glossaryCategory: GlossaryResponse) => void;
  selectedGlossary?: GlossaryResponse;
  onStatusChange: (glossary: GlossaryResponse) => void;
}

const GlossaryTable: React.FC<GlossaryTableProps> = ({
  glossaryData,
  onEndReached,
  hasNextPage,
  isFetchingNextPage,
  selectedGlossary,
  onRowClick,
  onStatusChange,
}) => {
  const router = useRouter();
  const [openEdit, setOpenEdit] = useState(false);

  const { currentTenantData } = useTenant();
  const { metaData } = useMetaData();
  const filters = useGlossaryFilterStore((state) => state.filters);

  const setSelectedGlossary = (glossary: GlossaryResponse) => {
    if (onRowClick) {
      onRowClick(glossary);
    }
  };

  const columns = useMemo(
    () => getColumns(metaData, currentTenantData, setSelectedGlossary, setOpenEdit, onStatusChange),
    [metaData, currentTenantData, onStatusChange]
  );

  return (
    <div className="bg-background min-h-[400px] rounded-xl p-6">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <BreadCrumbs path={["Glossary"]} />
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="rounded-2xl text-gray-500">
            Export
            <Icons.download className="h-4 w-4" />
          </Button>
          <GlossarySortPopover />
          <GlossaryFilterPopover />
        </div>
      </div>

      {/* Active Filters */}
      <ActiveFilters />

      {/* Active Sort */}
      <ActiveSort />

      {/* Table */}
      {glossaryData.length === 0 ? (
        <NoGlossaryData filters={filters} />
      ) : (
        <CustomVirtualList
          options={{
            data: glossaryData,
            columns,
            getCoreRowModel: getCoreRowModel(),
          }}
          onEndReached={onEndReached}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
          onRowClick={(row) => router.push(paths.glossary.view(row.id, row.name))}
        />
      )}
    </div>
  );
};

export default GlossaryTable;
