import React from "react";
import { SearchBar } from "@/components/search-bar";
import { useGlossaryFilterStore, useGlossaryFilterSelectors } from "@/stores/glossary-filter-store";

const GlossarySearchHeader: React.FC = () => {
  const setSearch = useGlossaryFilterStore((state) => state.setSearch);
  const { searchValue } = useGlossaryFilterSelectors();

  return (
    <SearchBar
      value={searchValue}
      onChange={setSearch}
      placeholder="Search Glossaries by name..."
    />
  );
};

export default GlossarySearchHeader;
