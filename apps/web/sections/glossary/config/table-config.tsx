import { TableColumnHeader } from "@/components/custom-virtual-list";
import { GlossaryResponse } from "@/types";
import { Button } from "@repo/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { Icons } from "@repo/ui/components/icons";
import { Badge } from "@repo/ui/components/badge";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { UserProfileResponse, Action, Resource } from "@/types";
import type { CurrentTenantData } from "@/providers/tenant-selector";
import { CanAccess } from "@/providers/access-control";

export const getColumns = (
  metaData: UserProfileResponse | null,
  currentTenantData: CurrentTenantData | null,
  setSelectedGlossary: (glossary: GlossaryResponse) => void,
  setOpenEdit: (bool: boolean) => void,
  onStatusChange: (glossary: GlossaryResponse) => void
) => {
  const columns: ColumnDef<GlossaryResponse>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => <TableColumnHeader column={column} title="Glossary Name" />,
      cell: ({ row }) => (
        <p className="truncate text-lg font-semibold">{row?.original?.name || "-"}</p>
      ),
      size: 300,
      enableSorting: false,
    },
    {
      accessorKey: "type",
      header: ({ column }) => <TableColumnHeader column={column} title="Type" />,
      cell: ({ row }) => row?.original?.type || "-",
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => <TableColumnHeader column={column} title="Last Updated" />,
      cell: ({ row }) => dayjs(row?.original?.updatedAt).format("DD-MM-YYYY") || "-",
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "remark",
      header: ({ column }) => <TableColumnHeader column={column} title="Remark" />,
      cell: ({ row }) => row?.original?.remark || "-",
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "status",
      header: ({ column }) => <TableColumnHeader column={column} title="Status" />,
      cell: ({ row }) => {
        const status = row.original?.status;
        const isActive = status === "Active";
        return (
          <Badge className={isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
            {status}
          </Badge>
        );
      },
      enableSorting: false,
      size: 100,
      meta: {
        isGrow: true,
      },
    },
    {
      id: "actions",
      header: "",
      size: 50,
      cell: ({ row }) => {
        const glossary = row.original;
        return (
          <CanAccess
            privilege={"update" as Action.Update}
            resource={"glossary-item" as Resource.GlossaryItem}
          >
            <DropdownMenu>
              <DropdownMenuTrigger
                asChild
                disabled={
                  !(
                    metaData?.role === "SuperAdmin" &&
                    row?.original?.type === "Global" &&
                    currentTenantData?.id === metaData?.tenant?.id
                  )
                }
              >
                <Button
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  onClick={(e) => e.stopPropagation()} // ✅ prevent row click when opening menu
                >
                  <span className="sr-only">Open menu</span>
                  <Icons.moreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedGlossary(glossary);
                    setOpenEdit(true);
                  }}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onStatusChange(glossary);
                  }}
                >
                  {glossary?.status === "Active" ? "Deactivate" : "Activate"}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CanAccess>
        );
      },
    },
  ];

  return columns;
};
