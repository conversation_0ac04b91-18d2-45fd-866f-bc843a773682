import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@repo/ui/components/badge";
import { Icons } from "@repo/ui/components/icons";
import { Button } from "@repo/ui/components/button";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@repo/ui/components/tooltip";
import type { ProjectFileResponse, ProjectFileStatus } from "@/types";

// Status color mapping
const getStatusColor = (status: ProjectFileStatus) => {
  switch (status) {
    case "Uploaded":
      return "bg-gray-100 text-gray-800";
    case "Classifying":
      return "bg-blue-50 text-blue-700";
    case "Classified":
      return "bg-blue-100 text-blue-800";
    case "Marking":
      return "bg-purple-50 text-purple-700";
    case "Marked":
      return "bg-purple-100 text-purple-800";
    case "Extracting":
      return "bg-green-50 text-green-700";
    case "Extracted":
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

// Format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

// Format file path to show category and assets
const formatFilePath = (path: string | null): { category: string; assets: string } => {
  if (!path) return { category: "-", assets: "-" };
  const parts = path.split("/").filter(Boolean);
  if (parts.length >= 2) {
    return {
      category: parts[parts.length - 2] || "-",
      assets: parts[parts.length - 1] || "-",
    };
  }
  return { category: "-", assets: "-" };
};

export const getFileColumns = (): ColumnDef<ProjectFileResponse>[] => [
  // File Name column
  {
    accessorKey: "name",
    header: "File Name",
    cell: ({ row }) => (
      <p className="max-w-[200px] truncate font-medium text-gray-900" title={row.original?.name}>
        {row.original?.name}
      </p>
    ),
    size: 200,
    minSize: 150,
    maxSize: 250,
  },
  // Total Pages column
  {
    id: "size",
    header: "Size",
    cell: ({ row }) => <span className="text-gray-600">{formatFileSize(row.original?.size)}</span>,
    size: 100,
  },
  // Assets column
  {
    id: "assets",
    header: "Assets",
    cell: ({ row }) => {
      return <p className="max-w-[80px] truncate text-gray-600"> {row.original?.assets || "-"}</p>;
    },
    size: 80,
  },
  // Category column
  {
    id: "category",
    header: "Category",
    cell: ({ row }) => {
      return <p className="max-w-[80px] truncate text-gray-600">{row.original?.catgory || "-"}</p>;
    },
    size: 80,
  },
  // File Path column
  {
    accessorKey: "path",
    header: "File Path",
    cell: ({ row }) => (
      <p className="max-w-[200px] truncate text-sm text-gray-600" title={row.original?.path || ""}>
        {row.original?.path || "-"}
      </p>
    ),
    size: 200,
  },
  // Status column
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.original?.status;
      const subStatus = row.original?.subStatus;

      // Show red badge with tooltip if substatus is Failed
      if (subStatus === "Failed") {
        return (
          <TooltipProvider>
            <div className="flex items-center gap-1">
              <Badge variant="destructive" className="border-0">
                {status}
              </Badge>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Icons.alertCircle className="h-4 w-4 cursor-pointer text-red-500" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Processing failed</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </TooltipProvider>
        );
      }

      // Default status display
      return (
        <div className="flex">
          <Badge variant="secondary" className={`${getStatusColor(status)} border-0`}>
            {status}
          </Badge>
        </div>
      );
    },
    size: 120,
  },
  // Actions column
  // {
  //   id: "actions",
  //   header: "",
  //   cell: () => (
  //     <div className="flex justify-center">
  //       <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600">
  //         <Icons.x className="h-4 w-4" />
  //       </Button>
  //     </div>
  //   ),
  //   size: 60,
  //   enableSorting: false,
  // },
];
