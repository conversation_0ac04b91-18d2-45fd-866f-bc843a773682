import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useInvalidateResourceQueries } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";

interface AddToMarkRequest {
  projectId: string;
  fileIds: string[];
}

interface AddToMarkResponse {
  success: boolean;
  message: string;
  data?: {
    processedCount: number;
    failedCount: number;
    failedFiles?: string[];
  };
}

export function useAddToMark() {
  const queryClient = useQueryClient();
  const invalidateProjectFileQueries = useInvalidateResourceQueries("project-files");

  return useMutation<AddToMarkResponse, Error, AddToMarkRequest>({
    mutationFn: async ({ projectId, fileIds }: AddToMarkRequest) => {
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.post<AddToMarkResponse>(
        `/projects/${projectId}/mark/`,
        { fileIds }
      );
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate all project file-related queries for the current tenant
      invalidateProjectFileQueries(queryClient);
      
      // Also invalidate specific project file queries
      queryClient.invalidateQueries({
        queryKey: ["project-files", variables.projectId],
      });
    },
  });
}
