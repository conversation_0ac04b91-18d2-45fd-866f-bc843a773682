import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useInvalidateResourceQueries } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";

interface AddToClassifyRequest {
  projectId: string;
  fileIds: string[];
}

interface AddToClassifyResponse {
  success: boolean;
  message: string;
  data?: {
    processedCount: number;
    failedCount: number;
    failedFiles?: string[];
  };
}

export function useAddToClassify() {
  const queryClient = useQueryClient();
  const invalidateProjectFileQueries = useInvalidateResourceQueries("project-files");

  return useMutation<AddToClassifyResponse, Error, AddToClassifyRequest>({
    mutationFn: async ({ projectId, fileIds }: AddToClassifyRequest) => {
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.post<AddToClassifyResponse>(
        `/projects/${projectId}/classify/`,
        { fileIds }
      );
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate all project file-related queries for the current tenant
      invalidateProjectFileQueries(queryClient);
      
      // Also invalidate specific project file queries
      queryClient.invalidateQueries({
        queryKey: ["project-files", variables.projectId],
      });
    },
  });
}
