import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useInvalidateResourceQueries } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";

interface AddToExtractRequest {
  projectId: string;
  fileIds: string[];
}

interface AddToExtractResponse {
  success: boolean;
  message: string;
  data?: {
    processedCount: number;
    failedCount: number;
    failedFiles?: string[];
  };
}

export function useAddToExtract() {
  const queryClient = useQueryClient();
  const invalidateProjectFileQueries = useInvalidateResourceQueries("project-files");

  return useMutation<AddToExtractResponse, Error, AddToExtractRequest>({
    mutationFn: async ({ projectId, fileIds }: AddToExtractRequest) => {
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.post<AddToExtractResponse>(
        `/projects/${projectId}/extract/`,
        { fileIds }
      );
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate all project file-related queries for the current tenant
      invalidateProjectFileQueries(queryClient);
      
      // Also invalidate specific project file queries
      queryClient.invalidateQueries({
        queryKey: ["project-files", variables.projectId],
      });
    },
  });
}
