import { useInfiniteQuery } from "@tanstack/react-query";
import { useTenantQueryKey, useHasTenant } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";
import type {
  ProjectFileListRequest,
  ProjectFileListResponse,
  ProjectFileFilter,
  ProjectFileSort,
} from "@/types";

interface UseFilteredFilesParams {
  projectId: string;
  limit?: number;
  filters?: ProjectFileFilter;
  sort?: ProjectFileSort;
}

export function useFilteredFiles({
  projectId,
  limit = 20,
  filters = {},
  sort,
}: UseFilteredFilesParams) {
  const queryKey = useTenantQueryKey(["project-files", projectId, { filters, sort, limit }]);
  const hasTenant = useHasTenant();

  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    status,
    refetch,
  } = useInfiniteQuery({
    queryKey,
    queryFn: async ({ pageParam }) => {
      const payload: ProjectFileListRequest = {
        filters,
        limit,
        cursor: pageParam,
        sort,
      };

      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.post<{
        success: boolean;
        data: ProjectFileListResponse;
        message: string;
      }>(`/projects/${projectId}/files/find`, payload);
      return response.data.data;
    },
    getNextPageParam: (lastPage) => {
      return lastPage.hasMore ? lastPage.nextCursor : undefined;
    },
    initialPageParam: undefined as string | undefined,
    enabled: hasTenant && !!projectId,
  });

  // Flatten the pages into a single array
  const files = data?.pages.flatMap((page) => page.files) ?? [];

  // Handle scroll to load more
  const handleScrollEnd = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  return {
    files,
    error,
    isLoading: status === "pending",
    isFetching,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    handleScrollEnd,
  };
}
