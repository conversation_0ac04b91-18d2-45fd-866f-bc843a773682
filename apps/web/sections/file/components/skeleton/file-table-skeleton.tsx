import React from "react";
import { Skeleton } from "@repo/ui/components/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";

const FileTableSkeleton: React.FC = () => {
  return (
    <div className="flex gap-4">
      {/* Main Table Skeleton */}
      <div className="bg-background flex-1 space-y-4 rounded-xl p-6">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="mt-1 h-4 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-16" />
            <Skeleton className="w-18 h-8" />
          </div>
        </div>

        {/* Table Skeleton */}
        <div className="relative max-h-[600px] overflow-auto rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-12 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-4" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-32" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-24 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-20" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-20 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-16" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-24 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-20" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-40" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-24 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-20" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-12 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-4" />
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 8 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Skeleton className="h-4 w-4" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-32" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-12" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-40" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-20 rounded-full" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-4" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Sidebar Skeleton */}
      <div className="bg-background w-80 space-y-4 rounded-xl p-6">
        {/* Project Info Skeleton */}
        <div>
          <Skeleton className="h-6 w-40" />
          <Skeleton className="mt-1 h-4 w-48" />
        </div>

        {/* File Statistics Skeleton */}
        <div className="space-y-3">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex items-center justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-16" />
            </div>
          ))}
        </div>

        {/* Status Cards Skeleton */}
        <div className="space-y-3">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="rounded-lg border p-4">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-5 w-20" />
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-8 w-8" />
                </div>
                <Skeleton className="h-5 w-5" />
              </div>
            </div>
          ))}
        </div>

        {/* Upload Statistics Skeleton */}
        <div className="border-t pt-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index}>
                <Skeleton className="mx-auto h-4 w-16" />
                <Skeleton className="mx-auto mt-1 h-6 w-8" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileTableSkeleton;
