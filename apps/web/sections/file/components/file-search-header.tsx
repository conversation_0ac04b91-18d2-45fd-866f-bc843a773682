import React from "react";
import { SearchBar } from "@/components/search-bar";
import { useFileFilterStore, useFileFilterSelectors } from "@/stores/file-filter-store";

const FileSearchHeader: React.FC = () => {
  const setSearch = useFileFilterStore((state) => state.setSearch);
  const { searchValue } = useFileFilterSelectors();

  return (
    <SearchBar placeholder="Search files by name..." value={searchValue} onChange={setSearch} />
  );
};

export default FileSearchHeader;
