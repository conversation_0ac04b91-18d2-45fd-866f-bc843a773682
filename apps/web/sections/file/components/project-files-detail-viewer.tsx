import React from "react";
import type { ProjectGetResponse } from "@/types";
import dayjs from "dayjs";
import { extractGlossaryItemValue } from "@/providers/glossary-provider/extract-glossary-value";
import { Separator } from "@repo/ui/components/separator";
import Link from "next/link";

interface ProjectFilesDetailsViewerProps {
  projectData?: ProjectGetResponse;
}

export function ProjectFilesDetailsViewer({ projectData }: ProjectFilesDetailsViewerProps) {
  // Show loading state if no project data
  if (!projectData) {
    return (
      <div className="bg-background w-[400px] space-y-4 rounded-xl p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 w-3/4 rounded bg-gray-200"></div>
          <div className="h-4 w-full rounded bg-gray-200"></div>
          <div className="space-y-2">
            <div className="h-4 rounded bg-gray-200"></div>
            <div className="h-4 rounded bg-gray-200"></div>
            <div className="h-4 rounded bg-gray-200"></div>
          </div>
        </div>
      </div>
    );
  }

  // Get the first category name for display (if any)
  const clientName = extractGlossaryItemValue("Client", projectData.categories);
  const projectHandler = projectData.assignee?.name || "Not assigned";
  const projectCode = projectData.code;

  return (
    <div className="bg-background w-80 space-y-4 rounded-xl p-6">
      {/* Project Info */}
      <div>
        <h3 className="text-lg font-semibold capitalize text-gray-900">{projectData.vessel}</h3>
        <p className="text-sm text-gray-500">
          <span> {clientName}</span> | <span>{projectCode}</span> | <span>{projectHandler}</span>
        </p>
      </div>

      <Separator />

      {/* Project Info Card */}

      <h1 className="text-base font-semibold text-gray-800">Project Info</h1>

      <div className="flex flex-row space-x-2">
        <p className="text-sm font-medium text-gray-600">Total Files:</p>
        <p className="text-sm text-gray-600">{projectData.fileCount || 0}</p>
      </div>

      <div className="flex flex-row space-x-2">
        <p className="text-sm font-medium text-gray-600">Start Date:</p>
        <p className="text-sm text-gray-600">
          {dayjs(projectData.createdAt).format("DD MMM YYYY")}
        </p>
      </div>

      <div className="flex flex-row space-x-2">
        <p className="text-sm font-medium text-gray-600">Project Status:</p>
        <p className="text-sm text-gray-600">
          <span
            className={`font-medium ${
              projectData.status === "New"
                ? "text-green-600"
                : projectData.status === "Completed"
                  ? "text-blue-600"
                  : "text-orange-600"
            }`}
          >
            {projectData.status}
          </span>
        </p>
      </div>

      <Separator />

      {/* File Status Cards */}
      <div className="space-y-3">
        {/* Classified */}
        <Link className="block cursor-pointer" href={`/project/${projectData.id}/classify`}>
          <div className="cursor-pointer rounded-lg border border-blue-200 bg-blue-50 p-4 transition-colors hover:bg-blue-100">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-blue-900">Classified</h4>
                <p className="text-sm text-blue-600">Files</p>
                <p className="text-2xl font-bold text-blue-900">
                  {projectData.fileCountMap?.Classified || 0}
                </p>
              </div>
              <div className="text-blue-400">
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
        </Link>

        {/* Marked */}
        <Link className="block cursor-pointer" href={`/project/${projectData.id}/mark`}>
          <div className="cursor-pointer rounded-lg border border-purple-200 bg-purple-50 p-4 transition-colors hover:bg-purple-100">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-purple-900">Marked</h4>
                <p className="text-sm text-purple-600">Files</p>
                <p className="text-2xl font-bold text-purple-900">
                  {projectData.fileCountMap?.Marked || 0}
                </p>
              </div>
              <div className="text-purple-400">
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
        </Link>

        {/* Extracted */}
        <Link className="block cursor-pointer" href={`/project/${projectData.id}/extract`}>
          <div className="cursor-pointer rounded-lg border border-green-200 bg-green-50 p-4 transition-colors hover:bg-green-100">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-green-900">Extracted</h4>
                <p className="text-sm text-green-600">Files</p>
                <p className="text-2xl font-bold text-green-900">
                  {projectData.fileCountMap?.Extracted || 0}
                </p>
              </div>
              <div className="text-green-400">
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* Upload Statistics */}
      {/* <div className="border-t pt-4">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-sm text-gray-500">Upload Attempted</p>
            <p className="text-lg font-bold">100</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Uploaded</p>
            <p className="text-lg font-bold">90</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Upload Failed</p>
            <p className="text-lg font-bold">10</p>
          </div>
        </div>
      </div> */}
    </div>
  );
}
