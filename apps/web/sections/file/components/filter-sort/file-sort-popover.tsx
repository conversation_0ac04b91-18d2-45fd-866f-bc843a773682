"use client";

import React, { useEffect } from "react";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { useFileFilterStore } from "@/stores/file-filter-store";
import type { ProjectFileSort } from "@/types";

const FileSortPopover: React.FC = () => {
  const sort = useFileFilterStore((state) => state.sort);
  const pendingSort = useFileFilterStore((state) => state.pendingSort);
  const setPendingSort = useFileFilterStore((state) => state.setPendingSort);
  const applyPendingSort = useFileFilterStore((state) => state.applyPendingSort);
  const clearSort = useFileFilterStore((state) => state.clearSort);

  const [open, setOpen] = React.useState(false);

  // Initialize pending sort when popover opens
  useEffect(() => {
    if (open) {
      setPendingSort(sort);
    }
  }, [open, sort, setPendingSort]);

  const handleFieldSelect = (field: string) => {
    setPendingSort({
      field,
      direction: pendingSort?.direction || "asc",
    });
  };

  const handleDirectionSelect = (direction: "asc" | "desc") => {
    if (!pendingSort?.field) return;

    setPendingSort({
      field: pendingSort.field,
      direction,
    });
  };

  const handleApply = () => {
    applyPendingSort();
    setOpen(false);
  };

  const handleClear = () => {
    clearSort();
    setOpen(false);
  };

  const hasChanges = JSON.stringify(pendingSort) !== JSON.stringify(sort);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Icons.arrowUpIcon className="h-4 w-4" />
          Sort
          {sort && <div className="h-2 w-2 rounded-full bg-blue-500" />}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="p-4">
          <div className="space-y-4">
            <div>
              <h4 className="mb-3 text-sm font-medium">Sort by</h4>

              {/* Current Sort Display */}
              {sort && (
                <div className="mb-4 rounded-lg bg-gray-50 p-3">
                  <div className="mb-1 text-sm text-gray-600">Current Sort</div>
                  <div className="text-sm font-medium">
                    {sort.field === "name" && "File Name"}
                    {sort.field === "size" && "File Size"}
                    {sort.field === "createdAt" && "Created Date"} (
                    {sort.direction === "asc" ? "A-Z" : "Z-A"})
                  </div>
                </div>
              )}

              {/* Sort Field Selection */}
              <div className="space-y-2">
                <div className="text-xs font-medium uppercase tracking-wide text-gray-500">
                  Field
                </div>
                <div className="grid grid-cols-1 gap-2">
                  {[
                    { value: "name", label: "File Name" },
                    { value: "size", label: "File Size" },
                    { value: "createdAt", label: "Created Date" },
                  ].map((field) => (
                    <Button
                      key={field.value}
                      variant={pendingSort?.field === field.value ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleFieldSelect(field.value)}
                      className="justify-start"
                    >
                      {field.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Sort Direction Selection */}
              {pendingSort?.field && (
                <div className="space-y-2">
                  <div className="text-xs font-medium uppercase tracking-wide text-gray-500">
                    Direction
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant={pendingSort?.direction === "asc" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleDirectionSelect("asc")}
                    >
                      <Icons.arrowUpIcon className="mr-2 h-4 w-4" />
                      A-Z
                    </Button>
                    <Button
                      variant={pendingSort?.direction === "desc" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleDirectionSelect("desc")}
                    >
                      <Icons.arrowDownIcon className="mr-2 h-4 w-4" />
                      Z-A
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex gap-2 border-t pt-2">
              {sort && (
                <Button variant="outline" size="sm" onClick={handleClear} className="flex-1">
                  Clear Sort
                </Button>
              )}
              <Button
                size="sm"
                onClick={handleApply}
                disabled={!hasChanges || !pendingSort?.field}
                className="flex-1"
              >
                Apply Sort
              </Button>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default FileSortPopover;
