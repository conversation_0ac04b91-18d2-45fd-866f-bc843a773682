"use client";

import React from "react";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Checkbox } from "@repo/ui/components/checkbox";
import { useFileFilterStore, useFileFilterSelectors } from "@/stores/file-filter-store";
import type { ProjectFileStatus } from "@/types";

const FileFilterPopover: React.FC = () => {
  const filters = useFileFilterStore((state) => state.filters);
  const updateFilters = useFileFilterStore((state) => state.updateFilters);
  const clearAllFilters = useFileFilterStore((state) => state.clearAllFilters);
  const { hasActiveFilters, activeFilterCount } = useFileFilterSelectors();

  const [open, setOpen] = React.useState(false);

  const handleStatusFilter = (status: ProjectFileStatus, checked: boolean) => {
    const currentStatuses = filters.status || [];
    
    if (checked) {
      updateFilters({
        status: [...currentStatuses, status],
      });
    } else {
      const newStatuses = currentStatuses.filter((s) => s !== status);
      updateFilters({
        status: newStatuses.length > 0 ? newStatuses : undefined,
      });
    }
  };

  const handlePathFilter = (path: string) => {
    updateFilters({
      path: path.trim() || undefined,
    });
  };

  const statusOptions: { value: ProjectFileStatus; label: string; color: string }[] = [
    { value: "Uploaded", label: "Uploaded", color: "bg-gray-100 text-gray-800" },
    { value: "Classified", label: "Classified", color: "bg-blue-100 text-blue-800" },
    { value: "Marked", label: "Marked", color: "bg-purple-100 text-purple-800" },
    { value: "Extracted", label: "Extracted", color: "bg-green-100 text-green-800" },
  ];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Icons.filter className="h-4 w-4" />
          Filter
          {hasActiveFilters && (
            <div className="flex items-center gap-1">
              <div className="h-2 w-2 rounded-full bg-blue-500" />
              <span className="text-xs">{activeFilterCount}</span>
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="p-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm">Filters</h4>
              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="h-auto p-1 text-xs text-gray-500 hover:text-gray-700"
                >
                  Clear all
                </Button>
              )}
            </div>

            {/* Status Filter */}
            <div className="space-y-3">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                Status
              </div>
              <div className="space-y-2">
                {statusOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${option.value}`}
                      checked={filters.status?.includes(option.value) || false}
                      onCheckedChange={(checked) =>
                        handleStatusFilter(option.value, !!checked)
                      }
                    />
                    <label
                      htmlFor={`status-${option.value}`}
                      className="flex items-center gap-2 text-sm cursor-pointer"
                    >
                      <span
                        className={`px-2 py-1 rounded text-xs font-medium ${option.color}`}
                      >
                        {option.label}
                      </span>
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Path Filter */}
            <div className="space-y-3">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                File Path
              </div>
              <div className="space-y-2">
                <input
                  type="text"
                  placeholder="Filter by path..."
                  value={filters.path || ""}
                  onChange={(e) => handlePathFilter(e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Active Filters Summary */}
            {hasActiveFilters && (
              <div className="pt-3 border-t">
                <div className="text-xs font-medium text-gray-500 mb-2">
                  Active Filters ({activeFilterCount})
                </div>
                <div className="space-y-1">
                  {filters.status?.map((status) => (
                    <div
                      key={status}
                      className="flex items-center justify-between text-xs bg-gray-50 px-2 py-1 rounded"
                    >
                      <span>Status: {status}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleStatusFilter(status, false)}
                        className="h-auto p-0 text-gray-400 hover:text-gray-600"
                      >
                        <Icons.x className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  {filters.path && (
                    <div className="flex items-center justify-between text-xs bg-gray-50 px-2 py-1 rounded">
                      <span>Path: {filters.path}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePathFilter("")}
                        className="h-auto p-0 text-gray-400 hover:text-gray-600"
                      >
                        <Icons.x className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default FileFilterPopover;
