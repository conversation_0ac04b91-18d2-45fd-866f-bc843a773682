"use client";
import React from "react";
import { useRouter } from "next/navigation";
import FileSearchHeader from "./components/file-search-header";
import { SubNavBar } from "@/components/sub-nav-bar";
import { CanAccess } from "@/providers/access-control";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import type { Action, Resource } from "@/types";
import FileTable from "./components/file-table";

export const FilePage = ({ projectId }: { projectId: string }) => {
  const router = useRouter();

  const handleUploadClick = () => {
    router.push(`/project/${projectId}/upload`);
  };

  return (
    <div className="flex h-full flex-col space-y-2">
      <FileSearchHeader />

      <SubNavBar>
        <CanAccess privilege={"create" as Action.Create} resource={"project" as Resource.Project}>
          <Button className="bg-primary text-white" onClick={handleUploadClick}>
            Upload Files
            <Icons.plus />
          </Button>
        </CanAccess>
      </SubNavBar>

      <div className="min-w-0 flex-1">
        <FileTable projectId={projectId} />
      </div>
    </div>
  );
};
