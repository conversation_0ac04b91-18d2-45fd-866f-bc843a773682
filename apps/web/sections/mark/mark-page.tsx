"use client";
import React from "react";
import { SearchBar } from "@/components/search-bar";
import { SubNavBar } from "@/components/sub-nav-bar";
import { MarkContainer } from "./components/mark-container";
import { BreadCrumbs } from "@/components/breadcrumbs";

interface MarkPageProps {
  projectId: string;
}

export const MarkPage = ({ projectId }: MarkPageProps) => {
  return (
    <div className="flex h-full flex-col space-y-2">
      <SearchBar />

      <SubNavBar />

      <div className="min-w-0 flex-1">
        <MarkContainer projectId={projectId} />
      </div>
    </div>
  );
};
