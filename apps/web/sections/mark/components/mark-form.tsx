"use client";
import React, { useState } from "react";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import type { ProjectFileResponse } from "@/types";

interface MarkFormData {
  markType: string;
  priority: string;
  notes: string;
  assignee: string;
}

interface MarkFormProps {
  file: ProjectFileResponse;
  onClose: () => void;
  onSave: (data: MarkFormData) => void;
}

export const MarkForm = ({ file, onClose, onSave }: MarkFormProps) => {
  const [formData, setFormData] = useState<MarkFormData>({
    markType: "Critical", // Default mark type
    priority: "High", // Default priority
    notes: "",
    assignee: "<PERSON> Do<PERSON>", // Default assignee
  });

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <div className="bg-background h-full space-y-4 rounded-xl p-2">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{file.name}</h3>
          <p className="text-sm text-gray-500">Total Files: 168</p>
        </div>
        <Button variant="outline" size="sm">
          View Manual
        </Button>
      </div>

      {/* Form Fields */}
      <div className="space-y-4">
        {/* Mark Type */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Mark Type</label>
          <Select
            value={formData.markType}
            onValueChange={(value) => setFormData((prev) => ({ ...prev, markType: value }))}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Mark Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Critical">Critical</SelectItem>
              <SelectItem value="Important">Important</SelectItem>
              <SelectItem value="Standard">Standard</SelectItem>
              <SelectItem value="Review">Review</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Priority */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Priority</label>
          <Select
            value={formData.priority}
            onValueChange={(value) => setFormData((prev) => ({ ...prev, priority: value }))}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="High">High</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="Low">Low</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Assignee */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Assignee</label>
          <Select
            value={formData.assignee}
            onValueChange={(value) => setFormData((prev) => ({ ...prev, assignee: value }))}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Assignee" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="John Doe">John Doe</SelectItem>
              <SelectItem value="Jane Smith">Jane Smith</SelectItem>
              <SelectItem value="Mike Johnson">Mike Johnson</SelectItem>
              <SelectItem value="Sarah Wilson">Sarah Wilson</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Notes */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Notes</label>
          <Textarea
            className="w-full"
            value={formData.notes}
            onChange={(e) => setFormData((prev) => ({ ...prev, notes: e.target.value }))}
            placeholder="Add any additional notes or comments..."
            rows={4}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-2 pt-4">
        <Button variant="outline" onClick={handleCancel}>
          Cancel
        </Button>
        <Button onClick={handleSave}>Save</Button>
      </div>
    </div>
  );
};
