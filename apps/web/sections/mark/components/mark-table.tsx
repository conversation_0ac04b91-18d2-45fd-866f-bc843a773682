"use client";
import React from "react";
import { AdvancedTable } from "@/components/advanced-table";
import { markTableConfig } from "../config/mark-table-config";
import { useFilteredFiles } from "@/sections/file/hooks/api";
import { useProjectData } from "@/sections/project/hooks/api";
import { extractGlossaryItemValue } from "@/providers/glossary-provider/extract-glossary-value";
import type { ProjectFileResponse, ProjectFileStatus } from "@/types";

import { Icons } from "@repo/ui/components/icons";
import { Button } from "@repo/ui/components/button";
import MarkTableSkeleton from "../skeleton/mark-table-skeleton";
import NoMarkData from "../no-data/no-mark-data";

interface MarkTableProps {
  projectId: string;
  selectedFiles: Set<string>;
  onFileSelect: (file: ProjectFileResponse, selected: boolean) => void;
  onRowClick: (file: ProjectFileResponse) => void;
  activeFileId?: string;
}

export const MarkTable = ({
  projectId,
  selectedFiles,
  onFileSelect,
  onRowClick,
  activeFileId,
}: MarkTableProps) => {
  // Use predefined filters for mark statuses
  const markFilters = {
    status: ["Marking", "Marked"] as ProjectFileStatus[],
  };

  // Fetch project data for header info
  const { data: projectData, isLoading: isProjectLoading } = useProjectData(projectId);

  // Fetch files with mark filters
  const { files, isLoading, error, hasNextPage, isFetchingNextPage, refetch, handleScrollEnd } =
    useFilteredFiles({
      projectId,
      limit: 20,
      filters: markFilters,
      sort: undefined,
    });

  const columns = markTableConfig();

  const handleRowSelect = (file: ProjectFileResponse, selected: boolean) => {
    onFileSelect(file, selected);
  };

  if (isLoading || isProjectLoading) {
    return <MarkTableSkeleton />;
  }

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center text-gray-500">
        <div className="text-center">
          <p className="text-lg font-medium">Error loading files</p>
          <p className="text-sm">Please try again</p>
          <button onClick={() => refetch()} className="text-primary mt-2 hover:underline">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-auto">
      {files.length === 0 ? (
        <NoMarkData />
      ) : (
        <div className="space-y-4">
          {/* Project Info Header */}
          <div className="flex justify-between">
            <div>
              <h2 className="text-lg font-semibold capitalize text-gray-900">
                {projectData?.vessel || "Loading..."}
              </h2>
              <p className="text-sm text-gray-500">
                {extractGlossaryItemValue("Client", projectData?.categories || [])} |{" "}
                {projectData?.code} | {projectData?.assignee?.name || "Not assigned"} • Total Files:{" "}
                {(projectData?.fileCountMap?.Marking || 0) +
                  (projectData?.fileCountMap?.Marked || 0) || files.length}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="rounded-2xl text-gray-500">
                Export
                <Icons.download className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" className="rounded-2xl text-gray-500">
                {" "}
                Filter
                <Icons.filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Table */}
          <AdvancedTable<ProjectFileResponse>
            data={files}
            columns={columns}
            onLoadMore={handleScrollEnd}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
            onRowSelect={handleRowSelect}
            selectedRows={selectedFiles}
            getRowId={(file) => file.id}
            isRowDisabled={(file) => file.status === "Marking"}
            isSelectionDisabled={(file) => file.status === "Marking" || file.subStatus === "Failed"}
            onRowClick={onRowClick}
            activeRowId={activeFileId}
            className="mt-4"
          />
        </div>
      )}
    </div>
  );
};
