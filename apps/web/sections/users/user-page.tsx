"use client";

import React, { useLayoutEffect } from "react";
import UserSearchHeader from "./components/user-search-header";
import UserList from "./components/user-list";
import { SubNavBar } from "@/components/sub-nav-bar";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import AddUserDialog from "./components/dialogs/add-user-dialog";
import { type User } from "@/types";

import EditUserDialog from "./components/dialogs/edit-user-dialog";
import ToggleUserStatusDialog from "./components/dialogs/toggle-user-status-dialog";
import ResetPasswordUserDialog from "./components/dialogs/reset-password-dialog";
import EmailVerificationUserDialog from "./components/dialogs/send-verification-mail";
import UserManagementRestriction from "./components/dialogs/user-management-restriction-dialog";
import { useTenant } from "@/providers/tenant-selector";

const UserPage: React.FC = () => {
  const [open, setOpen] = React.useState(false);
  const [openEdit, setOpenEdit] = React.useState(false);
  const [openToggleStatus, setOpenToggleStatus] = React.useState(false);
  const [openResetPassword, setOpenResetPassword] = React.useState(false);
  const [openEmailVerification, setOpenEmailVerification] = React.useState(false);
  const [openUserManagementRestriction, setOpenUserManagementRestriction] = React.useState(false);
  const [selectedUser, setSelectedUser] = React.useState<User | undefined>();

  const { currentTenantData } = useTenant();

  const handleEditUser = React.useCallback((user: User) => {
    setSelectedUser(user);
    setOpenEdit(true);
  }, []);
  const handleStatusChange = React.useCallback((user: User) => {
    setSelectedUser(user);
    setOpenToggleStatus(true);
  }, []);
  const handleResetPassword = React.useCallback((user: User) => {
    setSelectedUser(user);
    setOpenResetPassword(true);
  }, []);
  const handleEmailVerification = React.useCallback((user: User) => {
    setSelectedUser(user);
    setOpenEmailVerification(true);
  }, []);

  useLayoutEffect(() => {
    if (currentTenantData?.status === "Inactive") {
      setOpenUserManagementRestriction(true);
    }
  }, [currentTenantData?.status]);

  return (
    <div className="flex h-full flex-col space-y-2">
      <UserSearchHeader />
      <SubNavBar>
        <Button onClick={() => setOpen(true)} className="bg-primary text-white">
          Add User
          <Icons.plus />
        </Button>
      </SubNavBar>
      <UserList
        onEditUser={handleEditUser}
        onStatusChange={handleStatusChange}
        onResetPassword={handleResetPassword}
        onEmailVerification={handleEmailVerification}
      />
      <AddUserDialog open={open} setOpen={setOpen} />
      <EditUserDialog open={openEdit} onOpenChange={setOpenEdit} selectedUser={selectedUser} />
      <ToggleUserStatusDialog
        open={openToggleStatus}
        onOpenChange={setOpenToggleStatus}
        selectedUser={selectedUser}
      />
      <ResetPasswordUserDialog
        open={openResetPassword}
        onOpenChange={setOpenResetPassword}
        selectedUser={selectedUser}
      />
      <EmailVerificationUserDialog
        open={openEmailVerification}
        onOpenChange={setOpenEmailVerification}
        selectedUser={selectedUser}
      />
      <UserManagementRestriction
        open={openUserManagementRestriction}
        onOpenChange={setOpenUserManagementRestriction}
      />
    </div>
  );
};

export default UserPage;
