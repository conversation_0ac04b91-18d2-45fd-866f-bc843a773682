import { useCallback, useMemo } from "react";
import { useInfiniteQuery } from "@tanstack/react-query";
import { useTenantQueryKey, useHasTenant } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";
import type { BaseResponse, UserListResponse } from "@/types";
import type { UserFilters, UserSort } from "@/types";

interface UseFilteredUsersProps {
  limit?: number;
  filters?: UserFilters;
  sort?: UserSort;
}

export function useFilteredUsers({ limit = 10, filters = {}, sort }: UseFilteredUsersProps = {}) {
  // Create a stable query key that includes filters
  const baseQueryKey = useMemo(() => {
    const filterKey = JSON.stringify(filters);
    const sortKey = sort ? JSON.stringify(sort) : null;
    return ["users", "list", limit, filterKey, sortKey];
  }, [limit, filters, sort]);

  const queryKey = useTenantQueryKey(baseQueryKey);
  const hasTenant = useHasTenant();

  // Build the request payload
  const buildRequestPayload = useCallback(
    (pageParam?: string) => {
      const payload: any = {
        limit,
        cursor: pageParam,
      };

      // Add filters if they exist
      const activeFilters: any = {};

      if (filters.name && filters.name.trim()) {
        activeFilters.name = filters.name.trim();
      }

      if (filters.email && filters.email.trim()) {
        activeFilters.email = filters.email.trim();
      }

      if (filters.role && filters.role.length > 0) {
        activeFilters.role = filters.role;
      }

      if (filters.status && filters.status.length > 0) {
        activeFilters.status = filters.status;
      }

      if (Object.keys(activeFilters).length > 0) {
        payload.filters = activeFilters;
      }

      // Add sort if provided
      if (sort) {
        payload.sort = sort;
      }

      return payload;
    },
    [limit, filters, sort]
  );

  const query = useInfiniteQuery({
    queryKey,
    initialPageParam: undefined as string | undefined,
    queryFn: async ({ pageParam }: { pageParam?: string | undefined }) => {
      const requestPayload = buildRequestPayload(pageParam);

      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.post<BaseResponse<UserListResponse>>(
        `/users/find`,
        requestPayload
      );

      return response?.data?.data;
    },
    getNextPageParam: (lastPage) => lastPage?.nextCursor ?? undefined,
    enabled: hasTenant,
  });

  // Flatten pages into one list
  const users = useMemo(() => {
    return query.data?.pages?.flatMap((page) => page?.users || []) ?? [];
  }, [query.data]);

  // Scroll handler for infinite loading
  const handleScrollEnd = useCallback(() => {
    if (query.hasNextPage && !query.isFetchingNextPage) {
      query.fetchNextPage();
    }
  }, [query]);

  return {
    users,
    isLoading: query.isLoading,
    error: query.error,
    hasNextPage: query.hasNextPage,
    isFetchingNextPage: query.isFetchingNextPage,
    fetchNextPage: query.fetchNextPage,
    refetch: query.refetch,
    handleScrollEnd,
  };
}
