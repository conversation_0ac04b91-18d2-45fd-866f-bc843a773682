import { UserUpdateRequest } from "@/types";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useInvalidateResourceQueries } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";

export function useEditUser() {
  const queryClient = useQueryClient();
  const invalidateUserQueries = useInvalidateResourceQueries("users");

  return useMutation({
    mutationFn: async (data: UserUpdateRequest) => {
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.patch(`/user`, data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all user-related queries for the current tenant
      invalidateUserQueries(queryClient);
    },
  });
}
