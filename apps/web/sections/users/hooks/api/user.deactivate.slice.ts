import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useInvalidateResourceQueries } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";

// DEACTIVATE user
export function useDeactivateUser() {
  const queryClient = useQueryClient();
  const invalidateUserQueries = useInvalidateResourceQueries("users");

  return useMutation({
    mutationFn: async (userId: string) => {
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.patch(`/user/${userId}/deactivate`);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all user-related queries for the current tenant
      invalidateUserQueries(queryClient);
    },
  });
}
