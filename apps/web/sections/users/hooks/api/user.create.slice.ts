import { UserSignupRequest } from "@/types";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useInvalidateResourceQueries } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";

// POST request with useMutation
export function useCreateUser() {
  const queryClient = useQueryClient();
  const invalidateUserQueries = useInvalidateResourceQueries("users");

  return useMutation({
    mutationFn: async (userData: UserSignupRequest) => {
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.post("/user", userData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all user-related queries for the current tenant
      invalidateUserQueries(queryClient);
    },
  });
}
