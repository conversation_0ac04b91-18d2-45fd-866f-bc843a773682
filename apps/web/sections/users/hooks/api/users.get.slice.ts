import { useQuery } from "@tanstack/react-query";
import { useTenantQuery<PERSON>ey, useHasTenant } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";

// GET request with useQuery
export function useUserData(userId: string) {
  const queryKey = useTenantQueryKey(["users", userId]);
  const hasTenant = useHasTenant();

  return useQuery({
    queryKey,
    queryFn: async () => {
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.get(`/users/${userId}`);
      return response.data;
    },
    enabled: hasTenant && !!userId, // Only run when we have a tenant and userId
  });
}
