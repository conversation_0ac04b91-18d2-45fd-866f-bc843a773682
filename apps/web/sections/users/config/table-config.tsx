import { TableColumnHeader } from "@/components/custom-virtual-list";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { ColumnDef } from "@tanstack/react-table";
import { User, Action, Resource } from "@/types";
import { userRole, userStatus, UserProfileResponse } from "@/types";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { CanAccess } from "@/providers/access-control";

interface UserTableConfigProps {
  onEditUser: (user: User) => void;
  onStatusChange: (user: User) => void;
  onResetPassword: (user: User) => void;
  onEmailVerification: (user: User) => void;
  currentUser: UserProfileResponse | null;
}

export const getColumns = ({
  onEditUser,
  onStatusChange,
  onResetPassword,
  onEmailVerification,
  currentUser,
}: UserTableConfigProps) => {
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => <TableColumnHeader column={column} title="Name" />,
      cell: ({ row }) => (
        <p className="truncate text-lg font-semibold">
          {row?.original?.name || "-"}
          {currentUser?.email === row?.original?.emailId && (
            <span className="ml-1 text-red-500">(You)</span>
          )}
        </p>
      ),
      enableSorting: false,
      minSize: 200,
      meta: {
        isGrow: true,
      },
    },

    {
      accessorKey: "displayName",
      header: ({ column }) => <TableColumnHeader column={column} title="Display Name" />,
      cell: ({ row }) => <span className="truncate">{row?.original?.displayName || "-"}</span>,
      enableSorting: false,
      minSize: 200,
      meta: {
        isGrow: true,
      },
    },
    {
      accessorKey: "emailId",
      header: ({ column }) => <TableColumnHeader column={column} title="Email ID" />,
      cell: ({ row }) => <span className="truncate">{row?.original?.emailId || "-"} </span>,
      enableSorting: false,
      minSize: 200,
      meta: {
        isGrow: true,
      },
    },
    {
      accessorKey: "role",
      minSize: 150,
      meta: {
        isGrow: true,
      },
      header: ({ column }) => <TableColumnHeader column={column} title="Role" />,
      cell: ({ row }) => userRole[row?.original?.role] || "-",
      enableSorting: false,
    },
    {
      accessorKey: "status",
      minSize: 200,
      header: ({ column }) => <TableColumnHeader column={column} title="Status" />,
      meta: {
        isGrow: true,
      },
      cell: ({ row }) => {
        const status = row.original?.status;

        // Determine color based on status
        const bgColor =
          status === "PendingVerification"
            ? "bg-gray-200"
            : status === "Active"
              ? "bg-green-100"
              : "bg-red-100"; // inactive or any other fallback
        const color =
          status === "PendingVerification"
            ? "text-gray-800"
            : status === "Active"
              ? "text-green-800"
              : "text-red-800";
        return (
          <Badge className={bgColor}>
            <span className={`truncate ${color}`}>{userStatus[status]}</span>
          </Badge>
        );
      },
      enableSorting: false,
    },

    {
      id: "actions",
      header: "",
      size: 50,
      cell: ({ row }) => {
        const user = row.original;
        return (
          <CanAccess privilege={"update" as Action.Update} resource={"user" as Resource.User}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild disabled={user?.role === "SuperAdmin"}>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <Icons.moreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {user?.status !== "PendingVerification" && user.status !== "Inactive" && (
                  <DropdownMenuItem
                    onClick={() => {
                      onEditUser(user);
                    }}
                  >
                    Edit
                  </DropdownMenuItem>
                )}
                {user?.status === "PendingVerification" && (
                  <DropdownMenuItem
                    onClick={() => {
                      onEmailVerification(user);
                    }}
                  >
                    Send verification mail
                  </DropdownMenuItem>
                )}
                {currentUser?.email !== user?.emailId && user?.status !== "PendingVerification" && (
                  <DropdownMenuItem
                    onClick={() => {
                      onResetPassword(user);
                    }}
                  >
                    Reset password
                  </DropdownMenuItem>
                )}
                {/* Toggle Activate/Deactivate */}
                {currentUser?.email !== user?.emailId && (
                  <DropdownMenuItem
                    onClick={() => {
                      onStatusChange(user);
                    }}
                  >
                    {user?.status === "Active" || user?.status === "PendingVerification"
                      ? "Deactivate User"
                      : "Activate User"}
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </CanAccess>
        );
      },
    },
  ];
  return columns;
};
