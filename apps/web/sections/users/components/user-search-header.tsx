"use client";

import React, { useState } from "react";
import { SearchBar } from "@/components/search-bar";
import { SubNavBar } from "@/components/sub-nav-bar";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@repo/ui/components/dialog";
import AddUserForm from "./forms/add-user-form";
import { Icons } from "@repo/ui/components/icons";
import { Button } from "@repo/ui/components/button";
import { useUserFilterStore, useUserFilterSelectors } from "@/stores/user-filter-store";

const UserSearchHeader: React.FC = () => {
  const setSearch = useUserFilterStore((state) => state.setSearch);
  const { searchValue } = useUserFilterSelectors();

  return (
    <>
      <SearchBar value={searchValue} onChange={setSearch} placeholder="Search users by name..." />
    </>
  );
};

export default UserSearchHeader;
