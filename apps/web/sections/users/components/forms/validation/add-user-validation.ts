import type { UserSignupRequest } from "@/types";
import { z } from "zod";

export const addUserValidation = z.object({
  name: z.string().min(1, "Name is required").max(50, "Name must be at most 50 characters"),
  displayName: z
    .string()
    .min(1, "Display name is required")
    .max(100, "Display name must be at most 100 characters"),
  emailId: z.string().min(1, "Email is required").email("Invalid email format"),
  role: z.enum(["TenantAdmin", "ProjectHandler"], {
    errorMap: () => ({ message: "Role is required" }),
  }),
});

export type CreateUserPayload = z.infer<typeof addUserValidation>;
