"use client";

import React from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Button } from "@repo/ui/components/button";
import { userRole, userStatus } from "@/types";
import type { UserFilters, UserRole, UserStatus } from "@/types";

interface UserFilterFormProps {
  defaultValues: UserFilters;
  onApply: (filters: UserFilters) => void;
  onCancel: () => void;
  onClearAll: () => void;
}

const UserFilterForm: React.FC<UserFilterFormProps> = ({
  defaultValues,
  onApply,
  onCancel,
  onClearAll,
}) => {
  const { control, handleSubmit, reset, register } = useForm<UserFilters>({
    defaultValues,
  });

  const handleApply = (data: UserFilters) => onApply(data);

  const handleClearAllClick = () => {
    reset({});
    onClearAll();
  };

  return (
    <form onSubmit={handleSubmit(handleApply)} className="space-y-4">
      <div className="space-y-2">
        <h4 className="font-medium leading-none">Filter Users</h4>
        <p className="text-muted-foreground text-sm">Apply filters to narrow down the user list.</p>
      </div>

      {/* Name Filter */}

      <div className="space-y-2">
        <Label htmlFor="name">Name</Label>
        <Input id="name" {...register("name")} placeholder="Search by name..." />
      </div>

      {/* Email Filter */}

      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input id="email" {...register("email")} placeholder="Search by email..." />
      </div>

      {/* Role Filter */}
      <div className="space-y-2">
        <Label>Role</Label>
        <Controller
          name="role"
          control={control}
          render={({ field }) => (
            <div className="space-y-2">
              {Object.entries(userRole).map(([key, label]) => {
                const checked = field.value?.includes(key as UserRole) || false;
                return (
                  <div key={key} className="flex items-center space-x-2">
                    <Checkbox
                      id={`role-${key}`}
                      checked={checked}
                      onCheckedChange={(checked) => {
                        const current = field.value || [];
                        field.onChange(
                          checked ? [...current, key] : current.filter((r) => r !== key)
                        );
                      }}
                    />
                    <Label htmlFor={`role-${key}`} className="cursor-pointer text-sm font-normal">
                      {label}
                    </Label>
                  </div>
                );
              })}
            </div>
          )}
        />
      </div>

      {/* Status Filter */}
      <div className="space-y-2">
        <Label>Status</Label>
        <Controller
          name="status"
          control={control}
          render={({ field }) => (
            <div className="space-y-2">
              {Object.entries(userStatus).map(([key, label]) => {
                const checked = field.value?.includes(key as UserStatus) || false;
                return (
                  <div key={key} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${key}`}
                      checked={checked}
                      onCheckedChange={(checked) => {
                        const current = field.value || [];
                        field.onChange(
                          checked ? [...current, key] : current.filter((s) => s !== key)
                        );
                      }}
                    />
                    <Label htmlFor={`status-${key}`} className="cursor-pointer text-sm font-normal">
                      {label}
                    </Label>
                  </div>
                );
              })}
            </div>
          )}
        />
      </div>

      {/* Actions */}
      <div className="flex justify-between pt-4">
        <Button type="button" variant="outline" size="sm" onClick={handleClearAllClick}>
          Clear All
        </Button>
        <div className="space-x-2">
          <Button type="button" variant="outline" size="sm" onClick={() => reset(defaultValues)}>
            Cancel
          </Button>
          <Button type="submit" size="sm">
            Apply
          </Button>
        </div>
      </div>
    </form>
  );
};

export default UserFilterForm;
