"use client";
import React, { useEffect } from "react";
import { use<PERSON><PERSON>, SubmitH<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import { useCreateUser } from "@/sections/users/hooks/api";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@repo/ui/components/select";
import { toast } from "sonner";
import { addUserValidation, CreateUserPayload } from "./validation/add-user-validation";
import { userRole } from "@/types";
import { twMerge } from "tailwind-merge";

type AddUserFormProps = {
  setOpen: (open: boolean) => void;
};

const AddUserForm: React.FC<AddUserFormProps> = ({ setOpen }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    control,
  } = useForm<CreateUserPayload>({
    resolver: zodResolver(addUserValidation),
    defaultValues: {
      name: "",
      displayName: "",
      emailId: "",
      role: undefined,
    },
  });

  const createUser = useCreateUser();
  const { isPending } = createUser;

  const onSubmit: SubmitHandler<CreateUserPayload> = (data) => {
    createUser.mutate(data, {
      onSuccess: () => {
        setOpen(false);
        toast.success("User created successfully");
      },
      onError: (err) => {
        toast.error(err.message);
      },
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl p-2">
      <div className="grid grid-cols-1 gap-x-6 gap-y-1 md:grid-cols-2">
        {/* First Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("name")}
            placeholder="First Name"
            className={twMerge("h-10", errors.name && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.name?.message || "\u00A0"}</p>
        </div>

        {/* Display Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Display Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("displayName")}
            placeholder="Display Name"
            className={twMerge("h-10", errors.displayName && "border-red-500")}
          />

          <p className="text-xs text-red-500">{errors?.displayName?.message || "\u00A0"}</p>
        </div>

        {/* Email ID */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Email ID <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("emailId")}
            placeholder="Email ID"
            className={twMerge("h-10", errors.emailId && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.emailId?.message || "\u00A0"}</p>
        </div>

        {/* User Role */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            User Role <span className="ml-1 text-red-500">*</span>
          </label>
          <Controller
            name="role"
            control={control}
            render={({ field, fieldState }) => (
              <div className="w-full">
                <Select onValueChange={(val) => field.onChange(val)} value={field.value || ""}>
                  <SelectTrigger
                    className={twMerge("h-10 w-full", errors.role && "border-red-500")}
                  >
                    <SelectValue placeholder="Select User Role" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(userRole)
                      .filter(([key]) => key !== "SuperAdmin") // remove SuperAdmin
                      .map(([key, value]) => (
                        <SelectItem key={key} value={key}>
                          {value}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>

                <p className="mt-1 text-xs text-red-500">
                  {fieldState?.error?.message || "\u00A0"}
                </p>
              </div>
            )}
          />
        </div>
      </div>

      <div className="flex justify-center">
        <Button type="submit" className="px-8" disabled={isPending}>
          {isPending ? "Registering..." : "Register User"}
        </Button>
      </div>
    </form>
  );
};

export default AddUserForm;
