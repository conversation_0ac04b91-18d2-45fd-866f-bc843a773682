"use client";

import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@repo/ui/components/select";
import { useEditUser } from "@/sections/users/hooks/api";
import { toast } from "sonner";
import { UpdateUserPayload, editUserValidation } from "./validation/edit-user-validation";

import { userRole, type User } from "@/types";
import { twMerge } from "tailwind-merge";
import { useMetaData } from "@/providers/meta-data";
import { getDirtyFields } from "@/lib/form-utils";

type EditUserFormProps = {
  setOpen: (open: boolean) => void;
  selectedUser: User;
};

const EditUserForm: React.FC<EditUserFormProps> = ({ setOpen, selectedUser }) => {
  const {
    register,
    handleSubmit,
    formState: { errors, dirtyFields },
    control,
  } = useForm<UpdateUserPayload>({
    resolver: zodResolver(editUserValidation), // ✅ use Zod schema
    defaultValues: {
      name: selectedUser.name,
      displayName: selectedUser.displayName,
      emailId: selectedUser.emailId,
      role:
        selectedUser.role === "TenantAdmin" || selectedUser.role === "ProjectHandler"
          ? selectedUser.role
          : undefined,
    },
  });

  console.log(selectedUser.role, "selectedUser.role");

  const editUser = useEditUser();
  const { isPending } = editUser;
  const { metaData } = useMetaData();

  const onSubmit = (data: UpdateUserPayload) => {
    // Only send fields that have been modified (dirty fields)
    const dirtyData = getDirtyFields(data, dirtyFields);
    const payload = { id: selectedUser.id, ...dirtyData };

    console.log("Dirty fields:", dirtyFields);
    console.log("Sending only dirty data:", payload);

    editUser.mutate(payload, {
      onSuccess: () => {
        setOpen(false);
        toast.success("User updated successfully");
      },
      onError: (err) => {
        toast.error(err.message);
      },
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl p-2">
      <div className="grid grid-cols-1 gap-x-6 gap-y-1 md:grid-cols-2">
        {/* First Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("name")}
            placeholder="First Name"
            className={twMerge("h-10", errors.name && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.name?.message || "\u00A0"}</p>
        </div>

        {/* Display Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Display Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("displayName")}
            placeholder="Display Name"
            className={twMerge("h-10", errors.displayName && "border-red-500")}
          />

          <p className="text-xs text-red-500">{errors?.displayName?.message || "\u00A0"}</p>
        </div>

        {/* Email ID */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Email ID <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("emailId")}
            placeholder="Email ID"
            disabled
            className={twMerge("h-10", errors.emailId && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.emailId?.message || "\u00A0"}</p>
        </div>

        {/* User Role */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            User Role <span className="ml-1 text-red-500">*</span>
          </label>
          <Controller
            name="role"
            control={control}
            render={({ field, fieldState }) => (
              <div className="w-full">
                <Select
                  onValueChange={(val) => field.onChange(val)}
                  value={field.value || ""}
                  disabled={metaData?.email === selectedUser.emailId}
                >
                  <SelectTrigger
                    className={twMerge("h-10 w-full", errors.role && "border-red-500")}
                  >
                    <SelectValue placeholder="Select User Role" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(userRole)
                      .filter(([key]) => key !== "SuperAdmin") // remove SuperAdmin
                      .map(([key, value]) => (
                        <SelectItem key={key} value={key}>
                          {value}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>

                <p className="mt-1 text-xs text-red-500">
                  {fieldState?.error?.message || "\u00A0"}
                </p>
              </div>
            )}
          />
        </div>
      </div>

      {/* Buttons */}
      <div className="flex w-full items-center justify-between">
        <Button
          type="button"
          variant="outline"
          className="px-8 md:w-auto"
          onClick={() => setOpen(false)}
        >
          Cancel
        </Button>
        <Button type="submit" className="px-8 md:w-auto">
          {isPending ? "Saving..." : "Save"}
        </Button>
      </div>
    </form>
  );
};

export default EditUserForm;
