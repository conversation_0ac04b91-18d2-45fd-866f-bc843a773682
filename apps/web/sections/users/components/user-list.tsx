"use client";

import React, { useCallback } from "react";
import UserTable from "./user-table";
import UserTableSkeleton from "./skeleton/user-table-skeleton";
import { ErrorBoundary } from "@/components/error-boundary";
import { useFilteredUsers } from "../hooks/api";
import { useUserFilterStore } from "@/stores/user-filter-store";
import { type User } from "@/types";

interface UserListProps {
  onEditUser: (user: User) => void;
  onStatusChange: (user: User) => void;
  onResetPassword: (user: User) => void;
  onEmailVerification: (user: User) => void;
}

const UserList: React.FC<UserListProps> = ({
  onEditUser,
  onStatusChange,
  onResetPassword,
  onEmailVerification,
}) => {
  const filters = useUserFilterStore((state) => state.filters);
  const sort = useUserFilterStore((state) => state.sort);

  const { users, isLoading, error, hasNextPage, isFetchingNextPage, refetch, handleScrollEnd } =
    useFilteredUsers({
      limit: 10,
      filters,
      sort,
    });

  if (isLoading) return <UserTableSkeleton />;

  if (error) return <ErrorBoundary error={error} onRetry={() => refetch()} />;

  return (
    <UserTable
      userData={users ?? []}
      onEndReached={handleScrollEnd}
      hasNextPage={hasNextPage}
      isFetchingNextPage={isFetchingNextPage}
      onEditUser={onEditUser}
      onStatusChange={onStatusChange}
      onResetPassword={onResetPassword}
      onEmailVerification={onEmailVerification}
    />
  );
};

export default UserList;
