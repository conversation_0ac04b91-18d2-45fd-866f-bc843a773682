"use client";

import { getCoreRowModel } from "@tanstack/react-table";

import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { getColumns } from "../config/table-config";

import { CustomVirtualList } from "@/components/custom-virtual-list";
import { useMetaData } from "@/providers/meta-data";
import { useTenant } from "@/providers/tenant-selector";
import { useUserFilterStore } from "@/stores/user-filter-store";
import { type User } from "@/types";
import { useRouter } from "next/navigation";
import React from "react";
import ActiveFilters from "./filter-sort/active-filters";
import ActiveSort from "./filter-sort/active-sort";
import UserFilterPopover from "./filter-sort/user-filter-popover";
import UserSortPopover from "./filter-sort/user-sort-popover";
import NoUserData from "./no-data/no-user-data";

interface UsersTableProps {
  userData: User[] | [];
  onEndReached?: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  selectedUser?: User;
  onEditUser: (user: User) => void;
  onStatusChange: (user: User) => void;
  onResetPassword: (user: User) => void;
  onEmailVerification: (user: User) => void;
}

const UsersTable: React.FC<UsersTableProps> = ({
  userData,
  onEndReached,
  hasNextPage,
  isFetchingNextPage,
  onEditUser,
  onStatusChange,
  onResetPassword,
  onEmailVerification,
}) => {
  const filters = useUserFilterStore((state) => state.filters);
  const router = useRouter();

  const { metaData } = useMetaData();
  const { currentTenantData } = useTenant();

  const currentUser = metaData;

  const columns = getColumns({
    onEditUser,
    onStatusChange,
    onResetPassword,
    onEmailVerification,
    currentUser,
  });

  return (
    <div className="bg-background rounded-xl p-6">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" className="p-2" onClick={() => router.back()}>
            <Icons.arrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex flex-col">
            <h1 className="text-foreground my-0 text-xl font-semibold">User Management</h1>
            <p className="text-xs italic">
              Maximum Allotted Users -{" "}
              {currentTenantData?.maxActiveUsers || metaData?.tenant?.maxActiveUsers}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="rounded-2xl text-gray-500">
            Export
            <Icons.download className="h-4 w-4" />
          </Button>
          <UserSortPopover />
          <UserFilterPopover />
        </div>
      </div>

      {/* Active Filters */}
      <ActiveFilters />

      {/* Active Sort */}
      <ActiveSort />

      {/* Table */}
      {userData.length === 0 ? (
        <NoUserData filters={filters} />
      ) : (
        <CustomVirtualList
          options={{
            data: userData,
            columns: columns,
            getCoreRowModel: getCoreRowModel(),
          }}
          onEndReached={onEndReached}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
        />
      )}
    </div>
  );
};

export default UsersTable;
