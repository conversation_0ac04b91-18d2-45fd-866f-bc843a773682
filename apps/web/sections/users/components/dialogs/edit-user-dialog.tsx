"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "@repo/ui/components/dialog";
import EditUserForm from "../forms/edit-user-form";

import { type User } from "@/types";

interface EditTenantDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUser: User | undefined;
}

const EditUserDialog: React.FC<EditTenantDialogProps> = ({ open, onOpenChange, selectedUser }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[700px]"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-center">Edit User</DialogTitle>
        </DialogHeader>
        {selectedUser ? (
          <EditUserForm selectedUser={selectedUser} setOpen={onO<PERSON>Chang<PERSON>} />
        ) : (
          <p className="text-muted-foreground">No user selected</p>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditUserDialog;
