"use client";

import React from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { useActivateUser, useDeactivateUser } from "@/sections/users/hooks/api";
import { toast } from "sonner";
import { type User } from "@/types";
import { APIError } from "@/axios-instance";
interface ToggleUserStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUser: User | undefined;
}

const ToggleUserStatusDialog: React.FC<ToggleUserStatusDialogProps> = ({
  open,
  onOpenChange,
  selectedUser,
}) => {
  const activateUser = useActivateUser();
  const deactivateUser = useDeactivateUser();

  const handleConfirm = () => {
    if (!selectedUser) return;

    if (selectedUser.status === "Active" || selectedUser.status === "PendingVerification") {
      deactivateUser.mutate(selectedUser.id, {
        onSuccess: () => {
          onOpenChange(false);
          toast.success("User de-activated successfully");
        },
        onError: (err: APIError) => {
          toast.error(err.message);
        },
      });
    } else {
      activateUser.mutate(selectedUser.id, {
        onSuccess: () => {
          onOpenChange(false);
          toast.success("User activated successfully");
        },
        onError: (err: APIError) => {
          toast.error(err.message);
        },
      });
    }
  };

  const isLoading = activateUser.isPending || deactivateUser.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="text-center text-lg font-semibold">
          {selectedUser?.status === "Active" || selectedUser?.status === "PendingVerification"
            ? "Deactivate Tenant"
            : "Activate Tenant"}
        </DialogTitle>
        <p className="mt-2 text-center text-sm text-gray-600">
          Notification will be sent to the registered tenant admin email.
        </p>
        <div className="flex justify-between gap-2">
          <Input value={selectedUser?.emailId || ""} disabled />

          <Button onClick={handleConfirm} disabled={isLoading}>
            {isLoading ? "Confirming..." : "Confirm"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ToggleUserStatusDialog;
