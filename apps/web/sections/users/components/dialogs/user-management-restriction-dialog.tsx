"use client";

import React from "react";
import { Dialog, DialogContent, DialogTitle } from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { useRouter } from "next/navigation";

interface UserManagementRestrictionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const UserManagementRestriction: React.FC<UserManagementRestrictionDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const router = useRouter();
  return (
    <Dialog open={open}>
      <DialogContent
        className="max-w-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
        showCloseButton={false}
      >
        <DialogTitle className="text-center text-lg font-semibold">Tenant Deactivated</DialogTitle>
        <p className="mt-2 text-center text-sm text-gray-600">
          You cannot manage users of an inactive tenant.
        </p>
        <div className="flex justify-center">
          <Button onClick={() => router.back()}>OK</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserManagementRestriction;
