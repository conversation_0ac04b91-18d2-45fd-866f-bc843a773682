"use client";

import React from "react";
import { Dialog, DialogContent } from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { useResendEmailVerification } from "@/sections/users/hooks/api";
import { toast } from "sonner";
import { type User } from "@/types";

interface EmailVerificationUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUser: User | undefined;
}

const EmailVerificationUserDialog: React.FC<EmailVerificationUserDialogProps> = ({
  open,
  onOpenChange,
  selectedUser,
}) => {
  const emailVerification = useResendEmailVerification();
  const { isPending } = emailVerification;

  const handleConfirm = async () => {
    if (!selectedUser) return;

    emailVerification.mutate(selectedUser.id, {
      onSuccess: () => {
        onOpenChange(false);
        toast.success("Verification mail sent successfully");
      },
      onError: (err) => {
        toast.error(err.message);
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <h2 className="text-center text-lg font-semibold">Email Verification</h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Email verification link will be sent to the below email address.
        </p>
        <div className="flex justify-between gap-2">
          <Input value={selectedUser?.emailId || ""} disabled />

          <Button onClick={handleConfirm} disabled={isPending}>
            {isPending ? "Confirming..." : "Confirm"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EmailVerificationUserDialog;
