"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogTitle } from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { useResetPasswordUser } from "@/sections/users/hooks/api";
import { toast } from "sonner";
import EmailPassword from "supertokens-auth-react/recipe/emailpassword";
import { User } from "@/types";

interface ResetPasswordUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUser: User | undefined;
}

const ResetPasswordUserDialog: React.FC<ResetPasswordUserDialogProps> = ({
  open,
  onOpenChange,
  selectedUser,
}) => {
  const [loading, setLoading] = React.useState(false);
  const handleConfirm = async () => {
    if (!selectedUser) return;
    setLoading(true);
    try {
      const response = await Email<PERSON>assword.sendPasswordResetEmail({
        formFields: [{ id: "email", value: selectedUser.emailId }],
      });

      switch (response.status) {
        case "OK": {
          onOpenChange(false);
          toast.success("Password reset email sent! Check your inbox.");
          break;
        }

        case "PASSWORD_RESET_NOT_ALLOWED": {
          toast.error("Please verify your email before resetting your password.");
          break;
        }

        default: {
          onOpenChange(false);
          toast.error("Something went wrong. Please try again.");
        }
      }
    } catch (err) {
      onOpenChange(false);
      toast.error("Something went wrong. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="text-center">Reset Password</DialogTitle>

        <p className="mt-2 text-center text-sm text-gray-600">
          Reset password link will be sent to the below email address.
        </p>
        <div className="flex justify-between gap-2">
          <Input value={selectedUser?.emailId || ""} disabled />

          <Button onClick={handleConfirm} disabled={loading}>
            {loading ? "Confirming..." : "Confirm"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ResetPasswordUserDialog;
