import React from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@repo/ui/components/dialog";
import AddUserForm from "../forms/add-user-form";

interface AddUserDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export default function AddUserDialog({ open, setOpen }: AddUserDialogProps) {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="sm:max-w-[700px]"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-center">User Registry</DialogTitle>
        </DialogHeader>
        <AddUserForm setOpen={setOpen} />
      </DialogContent>
    </Dialog>
  );
}
