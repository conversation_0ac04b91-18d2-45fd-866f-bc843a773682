"use client";

import React, { useState, useCallback } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Badge } from "@repo/ui/components/badge";
import { useUserFilterStore, useUserFilterSelectors } from "@/stores/user-filter-store";
import type { UserFilters } from "@/types";
import UserFilterForm from "../forms/user-filter-form";

const UserFilterPopover: React.FC = () => {
  const filters = useUserFilterStore((state) => state.filters);
  const setFilters = useUserFilterStore((state) => state.setFilters);
  const clearAllFilters = useUserFilterStore((state) => state.clearAllFilters);
  const { hasActiveFilters, activeFilterCount } = useUserFilterSelectors();

  const [isOpen, setIsOpen] = useState(false);

  const handleApply = useCallback(
    (filters: UserFilters) => {
      setFilters(filters);
      setIsOpen(false);
    },
    [setFilters]
  );

  const handleCancel = useCallback(() => {
    setIsOpen(false);
  }, []);

  const handleClearAll = useCallback(() => {
    clearAllFilters();
    setIsOpen(false);
  }, [clearAllFilters]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="relative rounded-2xl text-gray-500">
          Filter
          <Icons.filter className="ml-1 h-4 w-4" />
          {hasActiveFilters && (
            <Badge className="bg-primary absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs text-white">
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>

      <PopoverContent className="h-[500px] w-80 overflow-auto p-4" align="end">
        <UserFilterForm
          defaultValues={filters}
          onApply={handleApply}
          onCancel={handleCancel}
          onClearAll={handleClearAll}
        />
      </PopoverContent>
    </Popover>
  );
};

export default UserFilterPopover;
