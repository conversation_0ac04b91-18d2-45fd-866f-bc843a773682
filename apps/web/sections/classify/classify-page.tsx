"use client";
import React from "react";
import { SearchBar } from "@/components/search-bar";
import { SubNavBar } from "@/components/sub-nav-bar";
import { ClassifyContainer } from "./components/classify-container";
import { BreadCrumbs } from "@/components/breadcrumbs";

interface ClassifyPageProps {
  projectId: string;
}

export const ClassifyPage = ({ projectId }: ClassifyPageProps) => {
  return (
    <div className="flex h-full flex-col space-y-2">
      <SearchBar />

      <SubNavBar />

      <div className="min-w-0 flex-1">
        <ClassifyContainer projectId={projectId} />
      </div>
    </div>
  );
};
