import React from "react";
import { Skeleton } from "@repo/ui/components/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";

const ClassifyTableSkeleton: React.FC = () => {
  return (
    <div className="flex gap-4">
      {/* Main Table Skeleton */}
      <div className="bg-background flex-1 space-y-4 rounded-xl">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="mt-1 h-4 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-16" />
            <Skeleton className="w-18 h-8" />
          </div>
        </div>

        {/* Table Skeleton */}
        <div className="relative max-h-[600px] overflow-auto rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-12 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-4" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-32" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-24 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-20" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-20 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-16" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-24 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-20" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-40" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-24 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-20" />
                </TableHead>
                <TableHead className="bg-background/95 supports-[backdrop-filter]:bg-background/60 text-muted-foreground sticky top-0 z-10 w-12 border-b font-medium backdrop-blur">
                  <Skeleton className="h-4 w-4" />
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 8 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Skeleton className="h-4 w-4" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-32" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-12" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-40" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-20 rounded-full" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-4" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default ClassifyTableSkeleton;
