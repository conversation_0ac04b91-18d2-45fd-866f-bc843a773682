"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import type { ProjectFileResponse } from "@/types";
import { PdfViewerDialog } from "@/components/pdf-viewer-dialog";

interface ClassifyFormData {
  category: string;
  assets: string;
  sparePages: string;
  jobPages: string;
}

interface ClassifyFormProps {
  file: ProjectFileResponse;
  projectId: string;
  onClose: () => void;
  onSave: (data: ClassifyFormData) => void;
}

export const ClassifyForm = ({ file, projectId, onClose, onSave }: ClassifyFormProps) => {
  const [formData, setFormData] = useState<ClassifyFormData>({
    category: "PMS", // Default category since file doesn't have this property yet
    assets: "Machine", // Default assets since file doesn't have this property yet
    sparePages: "3, 5, 8, 23, 29, 53, 87, 129, 198",
    jobPages: "3, 21, 52, 84, 126, 158",
  });
  const [isPdfDialogOpen, setIsPdfDialogOpen] = useState(false);

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  const handleViewManual = () => {
    setIsPdfDialogOpen(true);
  };

  return (
    <div className="bg-background h-full space-y-4 rounded-xl p-2">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{file.name}</h3>
          <p className="text-sm text-gray-500">Total Files: 168</p>
        </div>
        <Button variant="outline" size="sm" onClick={handleViewManual}>
          View Manual
        </Button>
      </div>

      {/* Form Fields */}
      <div className="space-y-4">
        {/* Category */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Category</label>
          <Select
            value={formData.category}
            onValueChange={(value) => setFormData((prev) => ({ ...prev, category: value }))}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="PMS">PMS</SelectItem>
              <SelectItem value="Non PMS">Non PMS</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Assets */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Assets</label>
          <Select
            value={formData.assets}
            onValueChange={(value) => setFormData((prev) => ({ ...prev, assets: value }))}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Asset" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Machine">Machine</SelectItem>
              <SelectItem value="System">System</SelectItem>
              <SelectItem value="Boiler">Boiler</SelectItem>
              <SelectItem value="Incinerator">Incinerator</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Spare Pages */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Spare Pages</label>
          <Input
            className="w-full"
            value={formData.sparePages}
            onChange={(e) => setFormData((prev) => ({ ...prev, sparePages: e.target.value }))}
            placeholder="Enter page numbers separated by commas"
            disabled
          />
        </div>

        {/* Job Pages */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Job Pages</label>
          <Input
            className="w-full"
            value={formData.jobPages}
            onChange={(e) => setFormData((prev) => ({ ...prev, jobPages: e.target.value }))}
            placeholder="Enter page numbers separated by commas"
            disabled
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-2 pt-4">
        <Button variant="outline" onClick={handleCancel}>
          Cancel
        </Button>
        <Button onClick={handleSave}>Save</Button>
      </div>

      {/* PDF Viewer Dialog */}
      <PdfViewerDialog
        open={isPdfDialogOpen}
        onOpenChange={setIsPdfDialogOpen}
        projectId={projectId}
        fileId={file.id}
        fileName={file.name}
      />
    </div>
  );
};
