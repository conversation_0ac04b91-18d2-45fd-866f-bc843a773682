import { GlossaryItemCreateRequest } from "@/types";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useInvalidateResourceQueries } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";

// POST request with useMutation
export function useCreateGlossaryItem() {
  const queryClient = useQueryClient();
  const invalidateGlossaryItemQueries = useInvalidateResourceQueries("glossary-items");

  return useMutation({
    mutationFn: async (data: GlossaryItemCreateRequest & { glossaryId: string }) => {
      // x-tenant-id header is automatically added by axios interceptor
      const { glossaryId, ...glossaryItemData } = data;
      console.log("glossaryItemData,glossaryId", glossaryItemData, glossaryId);
      const response = await instance.post(`/glossary/${glossaryId}`, glossaryItemData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all glossary-item-related queries for the current tenant
      invalidateGlossaryItemQueries(queryClient);
    },
  });
}
