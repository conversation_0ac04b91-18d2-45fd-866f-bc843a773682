"use client";

import React from "react";
import { SearchBar } from "@/components/search-bar";
import {
  useGlossaryItemFilterStore,
  useGlossaryItemFilterSelectors,
} from "@/stores/glossary-item-filter-store";

const GlossarySearchHeader: React.FC = () => {
  const setSearch = useGlossaryItemFilterStore((state) => state.setSearch);
  const { searchValue } = useGlossaryItemFilterSelectors();

  return (
    <SearchBar
      value={searchValue}
      onChange={setSearch}
      placeholder="Search Glossary Items by name..."
    />
  );
};

export default GlossarySearchHeader;
