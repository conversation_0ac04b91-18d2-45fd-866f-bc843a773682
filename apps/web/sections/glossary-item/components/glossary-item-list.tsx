"use client";

import React, { useCallback } from "react";
import GlossaryItemTable from "./glossary-item-table";
import GlossaryItemTableSkeleton from "./skeleton/glossary-item-table-skeleton";
import { ErrorBoundary } from "@/components/error-boundary";
import { useFilteredGlossaryItems } from "../hooks/api/glossary-item.list-with-filter.slice";
import { useGlossaryItemFilterStore } from "@/stores/glossary-item-filter-store";
import { GlossaryItemResponse } from "@/types";

interface GlossaryItemListProps {
  glossaryId: string;
  onRowClick: (glossaryItem: GlossaryItemResponse) => void;
  selectedGlossaryItem: GlossaryItemResponse | undefined;
  onStatusChange: (glossaryItem: GlossaryItemResponse) => void;
}

const GlossaryItemList: React.FC<GlossaryItemListProps> = ({
  glossaryId,
  onRowClick,
  selectedGlossaryItem,
  onStatusChange,
}) => {
  const filters = useGlossaryItemFilterStore((state) => state.filters);
  const sort = useGlossaryItemFilterStore((state) => state.sort);
  const {
    glossaryItems,
    glossaryName,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    fetchNextPage,
  } = useFilteredGlossaryItems({
    glossaryId,
    limit: 10,
    filters,
    sort,
  });

  const handleScrollEnd = useCallback(() => {
    fetchNextPage?.();
  }, [fetchNextPage]);

  if (isLoading) return <GlossaryItemTableSkeleton />;

  if (error) return <ErrorBoundary error={error} onRetry={() => refetch()} />;

  return (
    <GlossaryItemTable
      glossaryData={glossaryItems ?? []}
      glossaryName={glossaryName}
      onEndReached={handleScrollEnd}
      hasNextPage={hasNextPage}
      isFetchingNextPage={isFetchingNextPage}
      selectedGlossaryItem={selectedGlossaryItem}
      onRowClick={onRowClick}
      onStatusChange={onStatusChange}
    />
  );
};

export default GlossaryItemList;
