"use client";

import { getCoreRowModel } from "@tanstack/react-table";

import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { getColumns } from "../config/table-config";

import React from "react";

import { CustomVirtualList } from "@/components/custom-virtual-list";
import { useRouter } from "next/navigation";
import ActiveFilters from "./filter-sort/active-filters";
import ActiveSort from "./filter-sort/active-sort";
import GlossaryItemFilterPopover from "./filter-sort/glossary-item-filter-popover";
import GlossaryItemSortPopover from "./filter-sort/glossary-item-sort-popover";

import { BreadCrumbs } from "@/components/breadcrumbs";
import { useMetaData } from "@/providers/meta-data";
import { useTenant } from "@/providers/tenant-selector";
import { useGlossaryItemFilterStore } from "@/stores/glossary-item-filter-store";
import { GlossaryItemResponse } from "@/types";
import NoGlossaryItemData from "./no-data/no-glossary-item-data";

interface GlossaryItemTableProps {
  glossaryData: GlossaryItemResponse[];
  glossaryName: string;
  onEndReached?: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  selectedGlossaryItem?: GlossaryItemResponse;
  onRowClick?: (glossaryItem: GlossaryItemResponse) => void;
  onStatusChange: (glossaryItem: GlossaryItemResponse) => void;
}

const GlossaryItemTable: React.FC<GlossaryItemTableProps> = ({
  glossaryName,
  glossaryData,
  onEndReached,
  hasNextPage,
  isFetchingNextPage,
  selectedGlossaryItem,
  onRowClick,
  onStatusChange,
}) => {
  const filters = useGlossaryItemFilterStore((state) => state.filters);

  const router = useRouter();

  const { metaData } = useMetaData();
  const { currentTenantData } = useTenant();

  const setSelectedGlossaryItem = (glossaryItem: GlossaryItemResponse) => {
    if (onRowClick) {
      onRowClick(glossaryItem);
    }
  };

  const columns = getColumns(
    glossaryName,
    metaData,
    currentTenantData,
    setSelectedGlossaryItem,
    onStatusChange
  );

  return (
    <div className="bg-background min-h-[400px] rounded-xl p-6">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <BreadCrumbs path={["Glossary", glossaryName]} />
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="rounded-2xl text-gray-500">
            Export
            <Icons.download className="h-4 w-4" />
          </Button>
          <GlossaryItemSortPopover />
          <GlossaryItemFilterPopover />
        </div>
      </div>

      {/* Active Filters */}
      <ActiveFilters />

      {/* Active Sort */}
      <ActiveSort />

      {/* Table */}
      {glossaryData.length === 0 ? (
        <NoGlossaryItemData filters={filters} />
      ) : (
        <CustomVirtualList
          options={{
            data: glossaryData,
            columns: columns,
            getCoreRowModel: getCoreRowModel(),
          }}
          onEndReached={onEndReached}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
        />
      )}
    </div>
  );
};

export default GlossaryItemTable;
