"use client";

import React from "react";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";

import type { GlossaryItemFilters } from "@/types";
import {
  useGlossaryItemFilterStore,
  useGlossaryItemFilterSelectors,
} from "@/stores/glossary-item-filter-store";

const ActiveFilters: React.FC = () => {
  const filters = useGlossaryItemFilterStore((state) => state.filters);
  const removeFilter = useGlossaryItemFilterStore((state) => state.removeFilter);
  const clearAllFilters = useGlossaryItemFilterStore((state) => state.clearAllFilters);
  const { hasActiveFilters } = useGlossaryItemFilterSelectors();

  if (!hasActiveFilters) return null;

  const filterConfig = {
    name: { label: "Name", getValue: (v: string) => v },
    code: { label: "Code", getValue: (v: string) => v },
    referenceCode: { label: "Reference Code", getValue: (v: string) => v },
    status: { label: "Status", getValue: (v: string) => v },
  };

  return (
    <div className="flex flex-wrap items-center gap-2 rounded-lg bg-gray-50 p-4">
      {/* <span className="text-sm font-medium text-gray-700">Active filters:</span> */}

      {Object.entries(filters).map(([key, value]) => {
        const filterKey = key as keyof GlossaryItemFilters;
        const config = filterConfig[filterKey];

        if (!config) return null;

        // Handle string filters (name)
        if (typeof value === "string" && value.trim()) {
          return (
            <Badge key={filterKey} className="flex items-center gap-1">
              {config.label}: {config.getValue(value)}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => removeFilter(filterKey)}
              >
                <Icons.x className="h-3 w-3" />
              </Button>
            </Badge>
          );
        }

        // Handle array filters (type, status)
        if (Array.isArray(value) && value.length > 0) {
          return value.map((item) => (
            <Badge key={`${filterKey}-${item}`} className="flex items-center gap-1">
              {config.label}: {config.getValue(item)}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => removeFilter(filterKey, item)}
              >
                <Icons.x className="h-3 w-3" />
              </Button>
            </Badge>
          ));
        }

        return null;
      })}

      {/* Clear all button */}
      <Button variant="outline" size="sm" onClick={clearAllFilters} className="ml-2 text-xs">
        Clear All
      </Button>
    </div>
  );
};

export default ActiveFilters;
