"use client";

import React, { useState, useMemo } from "react";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { Badge } from "@repo/ui/components/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { GlossaryItemFilters } from "@/types";
import { GlossaryFilterForm } from "../forms/glossary-item-filter-form";
import {
  useGlossaryItemFilterStore,
  useGlossaryItemFilterSelectors,
} from "@/stores/glossary-item-filter-store";

const GlossaryFilterPopover: React.FC = () => {
  const filters = useGlossaryItemFilterStore((state) => state.filters);
  const setFilters = useGlossaryItemFilterStore((state) => state.setFilters);
  const clearAllFilters = useGlossaryItemFilterStore((state) => state.clearAllFilters);
  const { hasActiveFilters, activeFilterCount } = useGlossaryItemFilterSelectors();

  const [isOpen, setIsOpen] = useState(false);

  const handleApply = (values: GlossaryItemFilters) => {
    setFilters(values);
    setIsOpen(false);
  };

  const handleClear = () => {
    clearAllFilters();
    setIsOpen(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="relative rounded-2xl text-gray-500">
          Filter
          <Icons.filter className="ml-1 h-4 w-4" />
          {activeFilterCount > 0 && (
            <Badge className="bg-primary absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs text-white">
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="max-h-[500px] w-80 overflow-y-auto p-4" align="end">
        <GlossaryFilterForm
          defaultValues={filters}
          onSubmit={handleApply}
          onClear={handleClear}
          onCancel={() => setIsOpen(false)}
        />
      </PopoverContent>
    </Popover>
  );
};

export default GlossaryFilterPopover;
