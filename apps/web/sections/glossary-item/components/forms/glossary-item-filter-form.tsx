"use client";

import React from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Button } from "@repo/ui/components/button";
import { GlossaryStatus, glossaryStatus, GlossaryItemFilters } from "@/types";

interface GlossaryFilterFormProps {
  defaultValues: GlossaryItemFilters;
  onSubmit: (values: GlossaryItemFilters) => void;
  onClear: () => void;
  onCancel: () => void;
}

export const GlossaryFilterForm: React.FC<GlossaryFilterFormProps> = ({
  defaultValues,
  onSubmit,
  onClear,
  onCancel,
}) => {
  const { control, handleSubmit, reset, watch, register } = useForm<GlossaryItemFilters>({
    defaultValues,
  });

  const selectedStatuses = watch("status") || [];

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <h4 className="font-medium leading-none">Filter Glossary Items</h4>
        <p className="text-muted-foreground text-sm">
          Apply filters to narrow down the glossary item list.
        </p>
      </div>

      {/* Name */}
      <div className="space-y-2">
        <Label htmlFor="name-filter" className="text-sm font-medium">
          Name
        </Label>
        <Input id="name-filter" placeholder="Search by name..." {...register("name")} />
      </div>

      {/* Code */}
      <div className="space-y-2">
        <Label htmlFor="code-filter" className="text-sm font-medium">
          Code
        </Label>
        <Input id="code-filter" placeholder="Search by code..." {...register("code")} />
      </div>

      {/* Reference Code */}
      <div className="space-y-2">
        <Label htmlFor="reference-code-filter" className="text-sm font-medium">
          Reference Code
        </Label>
        <Input
          id="reference-code-filter"
          placeholder="Search by reference code..."
          {...register("referenceCode")}
        />
      </div>

      {/* Status */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Status</Label>
        <div className="space-y-2">
          {Object.entries(glossaryStatus).map(([key, value]) => (
            <Controller
              key={key}
              control={control}
              name="status"
              render={({ field: { value: fieldValue = [], onChange } }) => {
                const checked = fieldValue.includes(value as GlossaryStatus);
                return (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${key}`}
                      checked={checked}
                      onCheckedChange={(checked) => {
                        if (checked) onChange([...fieldValue, value]);
                        else onChange(fieldValue.filter((s: GlossaryStatus) => s !== value));
                      }}
                    />
                    <Label htmlFor={`status-${key}`} className="cursor-pointer text-sm font-normal">
                      {key}
                    </Label>
                  </div>
                );
              }}
            />
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between pt-4">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => {
            reset({});
            onClear();
          }}
        >
          Clear All
        </Button>
        <div className="space-x-2">
          <Button type="button" variant="outline" size="sm" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" size="sm">
            Apply
          </Button>
        </div>
      </div>
    </form>
  );
};
