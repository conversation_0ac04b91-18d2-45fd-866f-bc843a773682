"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import { toast } from "sonner";
import {
  AddGlossaryItemPayload,
  addGlossaryItemValidation,
} from "./validation/add-glossary-item-validation";

import { twMerge } from "tailwind-merge";

import { useParams, useSearchParams } from "next/navigation";
import { useCreateGlossaryItem } from "../../hooks/api/glossary-item.create.slice";

type AddGlossaryItemFormProps = {
  open?: boolean;
  setOpen: (open: boolean) => void;
};

const AddGlossaryItemForm: React.FC<AddGlossaryItemFormProps> = ({ open, setOpen }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<AddGlossaryItemPayload>({
    resolver: zodResolver(addGlossaryItemValidation), // ✅ use Zod schema
  });

  const params = useParams();
  const glossaryCategoryName = useSearchParams().get("name") || "Item";

  console.log("params", params.id);

  const addGlossaryItem = useCreateGlossaryItem();
  const { isPending } = addGlossaryItem;

  const onSubmit = (data: AddGlossaryItemPayload) => {
    addGlossaryItem.mutate(
      {
        ...data,
        glossaryId: params.id! as string,
      },
      {
        onSuccess: () => {
          setOpen(false);
          toast.success("Glossary updated successfully");
          console.log("sssssss");
        },
        onError: (err) => {
          toast.error(err.message);
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl p-2">
      <div className="grid grid-cols-1 gap-x-6 gap-y-1 md:grid-cols-2">
        {/* First Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            {glossaryCategoryName} <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("name")}
            placeholder="Item Name"
            className={twMerge("h-10", errors.name && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.name?.message || "\u00A0"}</p>
        </div>

        {/* Remarks */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Reference Code <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("referenceCode")}
            placeholder="Reference Code"
            className={twMerge("h-10", errors.referenceCode && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.referenceCode?.message || "\u00A0"}</p>
        </div>
      </div>

      {/* Buttons */}
      <div className="flex justify-center">
        <Button type="submit" className="w-full px-8 md:w-auto" disabled={isPending}>
          {isPending ? "Adding Item..." : "Add Item"}
        </Button>
      </div>
    </form>
  );
};

export default AddGlossaryItemForm;
