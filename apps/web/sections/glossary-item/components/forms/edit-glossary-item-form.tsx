"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import { toast } from "sonner";

import { twMerge } from "tailwind-merge";

import { useParams, useSearchParams } from "next/navigation";
import { useEditGlossaryItem } from "../../hooks/api/glossary-item.update.slice";
import { GlossaryItemResponse } from "@/types";
import {
  EditGlossaryItemPayload,
  editGlossaryItemValidation,
} from "./validation/edit-glossary-item-validation";

type EditGlossaryItemFormProps = {
  open?: boolean;
  setOpen: (open: boolean) => void;
  selectedGlossaryItem: GlossaryItemResponse;
};

const EditGlossaryItemForm: React.FC<EditGlossaryItemFormProps> = ({
  open,
  setOpen,
  selectedGlossaryItem,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<EditGlossaryItemPayload>({
    resolver: zodResolver(editGlossaryItemValidation), // ✅ use Zod schema
    defaultValues: {
      name: selectedGlossaryItem.name,
      referenceCode: selectedGlossaryItem.referenceCode,
    },
  });

  const editGlossaryItem = useEditGlossaryItem();
  const { isPending } = editGlossaryItem;
  const glossaryCategoryName = useSearchParams().get("name") || "Item";

  const onSubmit = (data: EditGlossaryItemPayload) => {
    editGlossaryItem.mutate(
      {
        ...data,
        id: selectedGlossaryItem.id,
      },
      {
        onSuccess: () => {
          setOpen(false);
          toast.success("Glossary updated successfully");
          console.log("sssssss");
        },
        onError: (err) => {
          toast.error(err.message);
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl p-2">
      <div className="grid grid-cols-1 gap-x-6 gap-y-1 md:grid-cols-2">
        {/* First Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            {glossaryCategoryName} <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("name")}
            placeholder="Item Name"
            className={twMerge("h-10", errors.name && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.name?.message || "\u00A0"}</p>
        </div>

        {/* Remarks */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Reference Code <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("referenceCode")}
            placeholder="Reference Code"
            className={twMerge("h-10", errors.referenceCode && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.referenceCode?.message || "\u00A0"}</p>
        </div>
      </div>

      {/* Buttons */}
      <div className="flex w-full items-center justify-between">
        <Button
          type="button"
          variant="outline"
          className="px-8 md:w-auto"
          onClick={() => setOpen(false)}
          disabled={isPending}
        >
          Cancel
        </Button>
        <Button type="submit" className="px-8 md:w-auto">
          {isPending ? "Saving..." : "Save"}
        </Button>
      </div>
    </form>
  );
};

export default EditGlossaryItemForm;
