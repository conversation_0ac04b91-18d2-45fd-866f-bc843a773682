import { z } from "zod";

export const editGlossaryItemValidation = z.object({
  name: z
    .string()
    .min(1, "Glossary Name is required")
    .max(50, "Name must be at most 50 characters"),
  referenceCode: z
    .string()
    .min(1, "Display name is required")
    .max(100, "Display name must be at most 100 characters"),
});

export type EditGlossaryItemPayload = z.infer<typeof editGlossaryItemValidation>;
