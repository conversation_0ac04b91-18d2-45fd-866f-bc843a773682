import { z } from "zod";

export const addGlossaryItemValidation = z.object({
  name: z
    .string()
    .min(1, "Glossary Name is required")
    .max(50, "Name must be at most 50 characters"),
  referenceCode: z
    .string()
    .min(1, "Reference code is required")
    .max(100, "Reference code must be at most 100 characters"),
});

export type AddGlossaryItemPayload = z.infer<typeof addGlossaryItemValidation>;
