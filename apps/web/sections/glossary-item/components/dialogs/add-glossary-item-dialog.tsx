import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@repo/ui/components/dialog";
import React from "react";

import AddGlossaryItemForm from "../forms/add-glossary-item-form";
import { useSearchParams } from "next/navigation";

interface AddGlossaryItemDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export default function AddGlossaryItemDialog({ open, setOpen }: AddGlossaryItemDialogProps) {
  const glossaryCategoryName = useSearchParams().get("name") || "Item";

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="sm:max-w-[700px]"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-center">{glossaryCategoryName} Registry</DialogTitle>
        </DialogHeader>
        <AddGlossaryItemForm setOpen={setOpen} />
      </DialogContent>
    </Dialog>
  );
}
