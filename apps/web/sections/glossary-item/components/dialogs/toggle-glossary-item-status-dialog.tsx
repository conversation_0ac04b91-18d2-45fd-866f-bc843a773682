"use client";

import { useActivateGlossaryItem, useDeactivateGlossaryItem } from "@/sections/glossary-item/hooks";
import { Button } from "@repo/ui/components/button";
import { Dialog, DialogContent, DialogTitle } from "@repo/ui/components/dialog";
import React from "react";
import { GlossaryItemResponse } from "@/types";
import { toast } from "sonner";

interface ToggleGlossaryItemStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedGlossaryItem: GlossaryItemResponse | undefined;
}

const ToggleGlossaryItemStatusDialog: React.FC<ToggleGlossaryItemStatusDialogProps> = ({
  open,
  onOpenChange,
  selectedGlossaryItem,
}) => {
  const activateGlossaryItem = useActivateGlossaryItem();
  const deactivateGlossaryItem = useDeactivateGlossaryItem();

  const handleConfirm = () => {
    if (!selectedGlossaryItem) return;

    if (selectedGlossaryItem.status === "Active") {
      deactivateGlossaryItem.mutate(selectedGlossaryItem.id, {
        onSuccess: () => {
          onOpenChange(false);
          toast.success("Glossary item deactivated successfully");
        },
        onError: (err: any) => {
          toast.error(err.message || "Failed to deactivate glossary item");
        },
      });
    } else {
      activateGlossaryItem.mutate(selectedGlossaryItem.id, {
        onSuccess: () => {
          onOpenChange(false);
          toast.success("Glossary item activated successfully");
        },
        onError: (err: any) => {
          toast.error(err.message || "Failed to activate glossary item");
        },
      });
    }
  };

  const isLoading = activateGlossaryItem.isPending || deactivateGlossaryItem.isPending;
  const isDeactivating = selectedGlossaryItem?.status === "Active";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="text-center">
          {isDeactivating ? "Deactivate Glossary Item" : "Activate Glossary Item"}
        </DialogTitle>
        <div className="space-y-4">
          <p className="text-center text-sm text-gray-600">
            Are you sure you want to {isDeactivating ? "deactivate" : "activate"} the glossary item
            "<span className="font-semibold">{selectedGlossaryItem?.name}</span>" (
            <span className="font-mono text-xs">{selectedGlossaryItem?.code}</span>)?
          </p>
          {isDeactivating && (
            <p className="text-center text-xs text-red-600">
              Deactivating this glossary item will make it unavailable for use.
            </p>
          )}
        </div>
        <div className="flex justify-between gap-2 pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={isLoading} variant="default">
            {isLoading ? "Processing..." : isDeactivating ? "Deactivate" : "Activate"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ToggleGlossaryItemStatusDialog;
