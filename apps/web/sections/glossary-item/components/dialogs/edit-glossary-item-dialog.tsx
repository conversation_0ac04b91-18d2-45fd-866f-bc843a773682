"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@repo/ui/components/dialog";
import EditGlossaryItemForm from "../forms/edit-glossary-item-form";
import { GlossaryItemResponse } from "@/types";
import { useSearchParams } from "next/navigation";

// import { type Glossary } from "@/types";

interface EditGlossaryItemDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedGlossaryItem: GlossaryItemResponse | undefined;
}

export const EditGlossaryItemDialog: React.FC<EditGlossaryItemDialogProps> = ({
  open,
  setOpen,
  selectedGlossaryItem,
}) => {
  const glossaryCategoryName = useSearchParams().get("name") || "Item";
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="sm:max-w-[700px]"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-center">Edit {glossaryCategoryName}</DialogTitle>
        </DialogHeader>
        {selectedGlossaryItem ? (
          <EditGlossaryItemForm selectedGlossaryItem={selectedGlossaryItem} setOpen={setOpen} />
        ) : (
          <p className="text-muted-foreground">No glossary selected</p>
        )}
      </DialogContent>
    </Dialog>
  );
};
