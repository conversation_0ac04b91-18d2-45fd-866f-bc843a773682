"use client";

import React, { useCallback, useState } from "react";
import GlossarySearchHeader from "./components/glossary-item-search-header";
import GlossaryItemList from "./components/glossary-item-list";

import { useParams, useSearchParams } from "next/navigation";
import type { Action, Resource } from "@/types";
import { CanAccess } from "@/providers/access-control";
import { SubNavBar } from "@/components/sub-nav-bar";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import AddGlossaryItemDialog from "./components/dialogs/add-glossary-item-dialog";
import { GlossaryItemResponse } from "@/types";
import { EditGlossaryItemDialog } from "./components/dialogs/edit-glossary-item-dialog";
import ToggleGlossaryItemStatusDialog from "./components/dialogs/toggle-glossary-item-status-dialog";

const GlossaryItemPage: React.FC = () => {
  const [openAdd, setOpenAdd] = useState(false);
  const params = useParams();
  const glossaryCategoryName = useSearchParams().get("name") || "Item";

  const [openEdit, setOpenEdit] = useState(false);
  const [openStatusToggle, setOpenStatusToggle] = useState(false);
  const [selectedGlossaryItem, setSelectedGlossaryItem] = useState<
    GlossaryItemResponse | undefined
  >();

  const handleRowClick = useCallback((glossaryItem: GlossaryItemResponse) => {
    setSelectedGlossaryItem(glossaryItem);
    setOpenEdit(true);
  }, []);

  const handleStatusChange = useCallback((glossaryItem: GlossaryItemResponse) => {
    setSelectedGlossaryItem(glossaryItem);
    setOpenStatusToggle(true);
  }, []);

  return (
    <div className="flex h-full flex-col space-y-2">
      <GlossarySearchHeader />

      <CanAccess
        privilege={"create" as Action.Create}
        resource={"glossary-item" as Resource.GlossaryItem}
      >
        <SubNavBar>
          <Button onClick={() => setOpenAdd(true)} className="bg-primary text-white">
            Add {glossaryCategoryName}
            <Icons.plus />
          </Button>
        </SubNavBar>
      </CanAccess>

      <GlossaryItemList
        glossaryId={params.id as string}
        onRowClick={handleRowClick}
        selectedGlossaryItem={selectedGlossaryItem}
        onStatusChange={handleStatusChange}
      />

      <AddGlossaryItemDialog open={openAdd} setOpen={setOpenAdd} />

      <EditGlossaryItemDialog
        open={openEdit}
        setOpen={setOpenEdit}
        selectedGlossaryItem={selectedGlossaryItem}
      />

      <ToggleGlossaryItemStatusDialog
        open={openStatusToggle}
        onOpenChange={setOpenStatusToggle}
        selectedGlossaryItem={selectedGlossaryItem}
      />
    </div>
  );
};

export default GlossaryItemPage;
