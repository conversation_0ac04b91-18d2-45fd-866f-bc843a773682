import { TableColumnHeader } from "@/components/custom-virtual-list";
import { GlossaryItemResponse, UserProfileResponse } from "@/types";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { Badge } from "@repo/ui/components/badge";
import { ColumnDef } from "@tanstack/react-table";

import { CurrentTenantData } from "@/providers/tenant-selector";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { CanAccess } from "@/providers/access-control";
import type { Action, Resource } from "@/types";

const colors = {
  Active: {
    bgColor: "bg-green-100",
    color: "text-green-800",
  },
  Inactive: {
    bgColor: "bg-red-100",
    color: "text-red-800",
  },
};

export const getColumns = (
  glossaryName: string,
  metaData: UserProfileResponse | null,
  currentTenantData: CurrentTenantData | null,
  setSelectedGlossaryItem: (glossary: GlossaryItemResponse) => void,
  onStatusChange: (glossaryItem: GlossaryItemResponse) => void
) => {
  const columns: ColumnDef<GlossaryItemResponse>[] = [
    {
      accessorKey: "code",
      header: ({ column }) => <TableColumnHeader column={column} title="Code" />,
      cell: ({ row }) => (
        <p className="truncate text-lg font-semibold">{row?.original?.code || "-"}</p>
      ),
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "name",
      header: ({ column }) => <TableColumnHeader column={column} title={glossaryName} />,
      cell: ({ row }) => row?.original?.name || "-",
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "referenceCode",
      header: ({ column }) => <TableColumnHeader column={column} title="Reference Code" />,
      cell: ({ row }) => row?.original?.referenceCode || "-",
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "type",
      header: ({ column }) => <TableColumnHeader column={column} title="Type" />,
      cell: ({ row }) => row?.original?.type || "-",
      enableSorting: false,
      size: 200,
    },
    // {
    //   accessorKey: "createdAt",
    //   header: ({ column }) => <TableColumnHeader column={column} title="Created At" />,
    //   cell: ({ row }) => dayjs(row?.original?.createdAt).format("DD-MM-YYYY") || "-",

    //   enableSorting: false,
    //   size: 200,
    // },
    // {
    //   accessorKey: "updatedAt",
    //   header: ({ column }) => <TableColumnHeader column={column} title="Last Updated" />,
    //   cell: ({ row }) => dayjs(row?.original?.updatedAt).format("DD-MM-YYYY") || "-",
    //   enableSorting: false,
    //   size: 200,
    // },
    {
      accessorKey: "status",
      header: ({ column }) => <TableColumnHeader column={column} title="Status" />,
      cell: ({ row }) => {
        const status = row.original?.status;
        const colorConfig = colors[status as keyof typeof colors] || colors.Inactive;
        return <Badge className={`${colorConfig.bgColor} ${colorConfig.color}`}>{status}</Badge>;
      },
      enableSorting: false,
      size: 100,
      meta: {
        isGrow: true,
      },
    },
    {
      id: "actions",
      header: "",
      size: 50,
      cell: ({ row }) => {
        const glossary = row.original;
        return (
          <CanAccess
            privilege={"update" as Action.Update}
            resource={"glossary-item" as Resource.GlossaryItem}
          >
            <DropdownMenu>
              <DropdownMenuTrigger
                asChild
                disabled={
                  !(
                    row?.original?.type === "Self" ||
                    (metaData?.role === "SuperAdmin" &&
                      row?.original?.type === "Global" &&
                      currentTenantData?.id === metaData?.tenant?.id)
                  )
                }
              >
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <Icons.moreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    setSelectedGlossaryItem(glossary);
                  }}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    onStatusChange(glossary);
                  }}
                >
                  {glossary?.status === "Active" ? "Deactivate" : "Activate"}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CanAccess>
        );
      },
    },
  ];

  return columns;
};
