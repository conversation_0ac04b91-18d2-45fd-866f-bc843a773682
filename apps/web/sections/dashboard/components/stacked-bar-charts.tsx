"use client";

import { useEffect, useRef } from "react";
import * as d3 from "d3";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@repo/ui/components/card";

interface DataPoint {
  client: string;
  New: number;
  Ongoing: number;
  Completed: number;
}

const data: DataPoint[] = [
  { client: "Client 1", New: 20, Ongoing: 15, Completed: 15 },
  { client: "Client 2", New: 18, Ongoing: 12, Completed: 13 },
  { client: "Client 3", New: 12, Ongoing: 10, Completed: 8 },
];

export default function StackedBarChart() {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current) return;

    const margin = { top: 10, right: 20, bottom: 30, left: 40 };
    const width = 320 - margin.left - margin.right;
    const height = 220 - margin.top - margin.bottom;

    d3.select(svgRef.current).selectAll("*").remove();

    const svg = d3
      .select(svgRef.current)
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const colorScale = d3
      .scaleOrdinal<string>()
      .domain(["New", "Ongoing", "Completed"])
      .range(["#4b9d83", "#b56d6d", "#5b7bb4"]);

    const stackedData = d3.stack<DataPoint>().keys(["New", "Ongoing", "Completed"])(data);

    const xScale = d3
      .scaleBand()
      .domain(data.map((d) => d.client))
      .range([0, width])
      .padding(0.4);

    const yScale = d3
      .scaleLinear()
      .domain([0, d3.max(stackedData[stackedData.length - 1], (d) => d[1]) || 0])
      .nice()
      .range([height, 0]);

    // --- Grid lines with fade-in ---
    const grid = svg
      .append("g")
      .attr("class", "grid")
      .style("opacity", 0)
      .call(
        d3
          .axisLeft(yScale)
          .ticks(4)
          .tickSize(-width)
          .tickFormat(() => "")
      );

    grid.selectAll("line").attr("stroke", "#ccc").attr("stroke-dasharray", "3,3");

    grid.transition().duration(600).ease(d3.easeCubicOut).style("opacity", 1);

    svg.select(".grid path").remove();

    // --- Bars with staggered animation ---
    const layers = svg
      .selectAll(".layer")
      .data(stackedData)
      .join("g")
      .attr("fill", (d) => colorScale(d.key));

    layers
      .selectAll("rect")
      .data((d) => d)
      .join("rect")
      .attr("x", (d) => xScale(d.data.client)!)
      .attr("y", height)
      .attr("width", xScale.bandwidth())
      .attr("height", 0)
      .attr("opacity", 0)
      .transition()
      .delay((_, i) => i * 150) // Stagger animation per client
      .duration(800)
      .ease(d3.easeCubicOut)
      .attr("y", (d) => yScale(d[1]))
      .attr("height", (d) => yScale(d[0]) - yScale(d[1]))
      .attr("opacity", 1)
      .on("end", function () {
        d3.select(this)
          .transition()
          .duration(600)
          .ease(d3.easeSinInOut)
          .attr("transform", "scale(1.03,1)")
          .transition()
          .duration(400)
          .attr("transform", "scale(1,1)");
      });

    // --- Tooltip setup ---
    const tooltip = d3
      .select("body")
      .append("div")
      .attr("class", "d3-tooltip")
      .style("position", "absolute")
      .style("padding", "6px 10px")
      .style("background", "rgba(0,0,0,0.7)")
      .style("color", "#fff")
      .style("border-radius", "6px")
      .style("font-size", "12px")
      .style("pointer-events", "none")
      .style("opacity", 0);

    layers
      .selectAll("rect")
      .on("mouseover", function (event, d) {
        const [x, y] = d3.pointer(event);
        tooltip
          .style("opacity", 1)
          .html(`<strong>${d.data.client}</strong><br/>Total: ${d[1] - d[0]}`)
          .style("left", `${event.pageX + 8}px`)
          .style("top", `${event.pageY - 20}px`);
        d3.select(this).attr("opacity", 0.8);
      })
      .on("mousemove", function (event) {
        tooltip.style("left", `${event.pageX + 8}px`).style("top", `${event.pageY - 20}px`);
      })
      .on("mouseout", function () {
        tooltip.style("opacity", 0);
        d3.select(this).attr("opacity", 1);
      });

    // --- X-axis fade-in ---
    svg
      .append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale))
      .call((g) => g.select(".domain").remove())
      .call((g) => g.selectAll("line").remove())
      .call((g) =>
        g
          .selectAll("text")
          .attr("font-size", "12px")
          .attr("fill", "#555")
          .style("opacity", 0)
          .transition()
          .duration(600)
          .delay(300)
          .style("opacity", 1)
      );

    // --- Y-axis fade-in ---
    const yAxis = svg
      .append("g")
      .call(d3.axisLeft(yScale).ticks(4))
      .call((g) => g.select(".domain").remove())
      .call((g) => g.selectAll("line").remove());

    yAxis
      .selectAll("text")
      .attr("font-size", "11px")
      .attr("fill", "#777")
      .style("opacity", 0)
      .transition()
      .duration(600)
      .delay(300)
      .style("opacity", 1);

    return () => {
      tooltip.remove();
    };
  }, []);

  return (
    <Card className="h-full rounded-2xl">
      <CardHeader className="pb-2">
        <CardTitle className="text-[15px] font-semibold text-gray-800">
          Projects by Client
        </CardTitle>
        <CardDescription className="text-[13px] text-gray-500">
          Client-wise project count
        </CardDescription>
      </CardHeader>

      <CardContent>
        <svg ref={svgRef}></svg>

        {/* Legend */}
        <div className="mt-4 space-y-2 text-sm text-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full" style={{ backgroundColor: "#4b9d83" }}></div>
              <span>New</span>
            </div>
            <span className="font-medium text-gray-800">40%</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full" style={{ backgroundColor: "#b56d6d" }}></div>
              <span>Ongoing</span>
            </div>
            <span className="font-medium text-gray-800">20%</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full" style={{ backgroundColor: "#5b7bb4" }}></div>
              <span>Completed</span>
            </div>
            <span className="font-medium text-gray-800">30%</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
