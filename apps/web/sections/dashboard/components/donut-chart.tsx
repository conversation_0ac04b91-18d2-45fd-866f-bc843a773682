"use client";

import { useEffect, useRef } from "react";
import * as d3 from "d3";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@repo/ui/components/card";

interface DataPoint {
  name: string;
  value: number;
  percentage: number;
}

const data: DataPoint[] = [
  { name: "Project Handler 1", value: 48, percentage: 48 },
  { name: "Project Handler 2", value: 17, percentage: 17 },
  { name: "Project Handler 3", value: 35, percentage: 35 },
];

export default function DonutChart() {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current) return;

    const width = 280;
    const height = 300;
    const radius = Math.min(width, height) / 2 - 20;

    // Clear previous content
    d3.select(svgRef.current).selectAll("*").remove();

    const svg = d3
      .select(svgRef.current)
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`)
      .attr("opacity", 0)
      .transition()
      .duration(800)
      .attr("opacity", 1);

    const colorScale = d3
      .scaleOrdinal<string>()
      .domain(data.map((d) => d.name))
      .range(["#7b68aa", "#5b7bb4", "#8b7e4f"]);

    const pie = d3
      .pie<DataPoint>()
      .sort(null)
      .value((d) => d.value);

    const arc = d3
      .arc<d3.PieArcDatum<DataPoint>>()
      .innerRadius(radius * 0.6)
      .outerRadius(radius);

    const arcs = d3
      .select(svgRef.current)
      .select("g")
      .selectAll(".arc")
      .data(pie(data))
      .join("g")
      .attr("class", "arc");

    // Animate drawing the arcs
    arcs
      .append("path")
      .attr("fill", (d) => colorScale(d.data.name))
      .attr("stroke", "#fff")
      .attr("stroke-width", 2)
      .transition()
      .duration(1000)
      .attrTween("d", function (d) {
        const i = d3.interpolate(d.startAngle + 0.1, d.endAngle);
        return function (t) {
          d.endAngle = i(t);
          return arc(d)!;
        };
      });

    // Add hover zoom + tooltip
    const tooltip = d3
      .select("body")
      .append("div")
      .style("position", "absolute")
      .style("background", "rgba(0,0,0,0.7)")
      .style("color", "#fff")
      .style("padding", "4px 8px")
      .style("border-radius", "4px")
      .style("font-size", "12px")
      .style("visibility", "hidden");

    arcs
      .select("path")
      .on("mouseover", function (event, d) {
        tooltip.style("visibility", "visible").text(`${d.data.name}: ${d.data.percentage}%`);

        d3.select(this)
          .transition()
          .duration(200)
          .attr(
            "d",
            d3
              .arc<d3.PieArcDatum<DataPoint>>()
              .innerRadius(radius * 0.6)
              .outerRadius(radius + 10)
          );
      })
      .on("mousemove", function (event) {
        tooltip.style("top", event.pageY - 28 + "px").style("left", event.pageX + 10 + "px");
      })
      .on("mouseout", function () {
        tooltip.style("visibility", "hidden");
        d3.select(this).transition().duration(200).attr("d", arc);
      });

    // Add center text (optional)
    const total = d3.sum(data, (d) => d.value);
    d3.select(svgRef.current)
      .select("g")
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dy", "0.3em")
      .attr("font-size", "16px")
      .attr("font-weight", "bold")
      .attr("fill", "#333")
      .text(`${total}`);

    return () => {
      tooltip.remove();
      d3.select(svgRef.current).selectAll("*").remove();
    };
  }, []);

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Projects under project handlers</CardTitle>
        <CardDescription>Projects categorized by project handler</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center">
          <svg ref={svgRef}></svg>
          <div className="mt-4 w-full space-y-2 text-sm">
            {data.map((item) => (
              <div key={item.name} className="flex items-center justify-between px-2">
                <div className="flex items-center gap-2">
                  <div
                    className="h-3 w-3 rounded-full"
                    style={{
                      backgroundColor:
                        item.name === "Project Handler 1"
                          ? "#7b68aa"
                          : item.name === "Project Handler 2"
                            ? "#5b7bb4"
                            : "#8b7e4f",
                    }}
                  ></div>
                  <span>{item.name}</span>
                </div>
                <span className="font-semibold">{item.percentage}%</span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
