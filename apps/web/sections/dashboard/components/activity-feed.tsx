import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@repo/ui/components/card";
import { Avatar, AvatarFallback } from "@repo/ui/components/avatar";

interface Activity {
  id: string;
  user: string;
  action: string;
  time: string;
  initials: string;
}

const activities: Activity[] = [
  {
    id: "1",
    user: "<PERSON>",
    action: "updated status of project MV Atlas to Processing",
    time: "1 hour ago",
    initials: "<PERSON><PERSON>",
  },
  {
    id: "2",
    user: "<PERSON><PERSON>",
    action: "uploaded 8 files to ZX Shipping",
    time: "1 hour ago",
    initials: "AR",
  },
  {
    id: "3",
    user: "<PERSON><PERSON><PERSON>",
    action: "created new export template JobType-A",
    time: "3 days ago",
    initials: "<PERSON>",
  },
  {
    id: "4",
    user: "<PERSON><PERSON><PERSON> <PERSON>",
    action: "created new export template JobType-A",
    time: "3 days ago",
    initials: "<PERSON>",
  },
  {
    id: "5",
    user: "<PERSON><PERSON><PERSON>",
    action: "created new export template JobType-A",
    time: "3 days ago",
    initials: "MC",
  },
];

export default function ActivityFeed() {
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Recent Activity Feed</CardTitle>
        <CardDescription>
          Shows user-level actions across projects, templates, and files.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.map((activity) => (
          <div key={activity.id} className="flex gap-3">
            <Avatar className="h-8 w-8 flex-shrink-0">
              <AvatarFallback className="text-xs">{activity.initials}</AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <p className="text-foreground text-sm font-medium">{activity.user}</p>
              <p className="text-muted-foreground line-clamp-2 text-xs">{activity.action}</p>
              <p className="text-muted-foreground mt-1 text-xs">{activity.time}</p>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
