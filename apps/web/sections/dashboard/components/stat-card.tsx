import { ArrowUpRight } from "lucide-react";

interface StatCardProps {
  title: string;
  value: string;
  icon?: string;
  className?: string;
}

export default function StatCard({
  title,
  value,
  icon,
  className = "bg-background border border-border",
}: StatCardProps) {
  return (
    <div className={`flex items-start justify-between rounded-lg p-6 ${className}`}>
      <div className="flex-1">
        <p className={className.includes("bg-teal") ? "text-white/80" : "text-muted-foreground"}>
          {title}
        </p>
        <h3
          className={`mt-2 text-3xl font-bold ${className.includes("bg-teal") ? "text-white" : "text-foreground"}`}
        >
          {value}
        </h3>
      </div>
      <div
        className={`flex h-10 w-10 items-center justify-center rounded-full ${className.includes("bg-teal") ? "bg-white/20" : "bg-muted"}`}
      >
        <ArrowUpRight
          className={`h-5 w-5 ${className.includes("bg-teal") ? "text-white" : "text-foreground"}`}
        />
      </div>
    </div>
  );
}
