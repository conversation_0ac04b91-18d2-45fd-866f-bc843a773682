"use client";
import React from "react";
import StatCard from "./components/stat-card";
import StackedBarChart from "./components/stacked-bar-charts";
import DonutChart from "./components/donut-chart";
import ActivityFeed from "./components/activity-feed";
import { SubNavBar } from "@/components/sub-nav-bar";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { useRouter } from "next/navigation";
import { CanAccess } from "@/providers/access-control";
import { Action, Resource } from "@/types";

const DashboardPage: React.FC = () => {
  const router = useRouter();
  return (
    <div className="flex h-full flex-col space-y-2">
      <SubNavBar>
        <CanAccess privilege={"create" as Action.Create} resource={"project" as Resource.Project}>
          <Button
            onClick={() => router.push("/project?add-project=true")}
            className="bg-primary text-white"
          >
            Add Project
            <Icons.plus />
          </Button>
        </CanAccess>
      </SubNavBar>
      <main className="bg-background min-h-screen rounded-xl p-6">
        <div className="mx-auto">
          {/* Header Stats */}
          <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-4">
            <StatCard
              title="Total Projects"
              value="24"
              icon="📊"
              className="bg-teal-600 text-white"
            />
            <StatCard title="In Progress" value="12" />
            <StatCard title="Completed" value="3" />
            <StatCard title="Average Duration" value="7 Days" />
          </div>

          {/* Charts and Activity Feed */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            <div className="lg:col-span-1">
              <StackedBarChart />
            </div>
            <div className="lg:col-span-1">
              <DonutChart />
            </div>
            <div className="lg:col-span-1">
              <ActivityFeed />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default DashboardPage;
