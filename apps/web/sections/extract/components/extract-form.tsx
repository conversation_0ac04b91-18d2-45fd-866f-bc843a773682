"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import type { ProjectFileResponse } from "@/types";

interface ExtractFormData {
  extractionType: string;
  dataFields: string;
  outputFormat: string;
  quality: string;
  notes: string;
}

interface ExtractFormProps {
  file: ProjectFileResponse;
  onClose: () => void;
  onSave: (data: ExtractFormData) => void;
}

export const ExtractForm = ({ file, onClose, onSave }: ExtractFormProps) => {
  const [formData, setFormData] = useState<ExtractFormData>({
    extractionType: "Full", // Default extraction type
    dataFields: "All Fields", // Default data fields
    outputFormat: "JSON", // Default output format
    quality: "High", // Default quality
    notes: "",
  });

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <div className="bg-background h-full space-y-4 rounded-xl p-2">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{file.name}</h3>
          <p className="text-sm text-gray-500">Total Files: 168</p>
        </div>
        <Button variant="outline" size="sm">
          View Manual
        </Button>
      </div>

      {/* Form Fields */}
      <div className="space-y-4">
        {/* Extraction Type */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Extraction Type</label>
          <Select
            value={formData.extractionType}
            onValueChange={(value) => setFormData((prev) => ({ ...prev, extractionType: value }))}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Extraction Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Full">Full Extraction</SelectItem>
              <SelectItem value="Partial">Partial Extraction</SelectItem>
              <SelectItem value="Selective">Selective Extraction</SelectItem>
              <SelectItem value="Metadata">Metadata Only</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Data Fields */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Data Fields</label>
          <Select
            value={formData.dataFields}
            onValueChange={(value) => setFormData((prev) => ({ ...prev, dataFields: value }))}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Data Fields" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All Fields">All Fields</SelectItem>
              <SelectItem value="Text Only">Text Only</SelectItem>
              <SelectItem value="Images Only">Images Only</SelectItem>
              <SelectItem value="Tables Only">Tables Only</SelectItem>
              <SelectItem value="Custom">Custom Selection</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Output Format */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Output Format</label>
          <Select
            value={formData.outputFormat}
            onValueChange={(value) => setFormData((prev) => ({ ...prev, outputFormat: value }))}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Output Format" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="JSON">JSON</SelectItem>
              <SelectItem value="XML">XML</SelectItem>
              <SelectItem value="CSV">CSV</SelectItem>
              <SelectItem value="Excel">Excel</SelectItem>
              <SelectItem value="PDF">PDF</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Quality */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Quality</label>
          <Select
            value={formData.quality}
            onValueChange={(value) => setFormData((prev) => ({ ...prev, quality: value }))}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Quality" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="High">High Quality</SelectItem>
              <SelectItem value="Medium">Medium Quality</SelectItem>
              <SelectItem value="Low">Low Quality</SelectItem>
              <SelectItem value="Auto">Auto Detect</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Notes */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Notes</label>
          <Textarea
            className="w-full"
            value={formData.notes}
            onChange={(e) => setFormData((prev) => ({ ...prev, notes: e.target.value }))}
            placeholder="Add any additional notes or special instructions..."
            rows={4}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-2 pt-4">
        <Button variant="outline" onClick={handleCancel}>
          Cancel
        </Button>
        <Button onClick={handleSave}>Save</Button>
      </div>
    </div>
  );
};
