"use client";
import React from "react";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { useRouter } from "next/navigation";

interface BucketNavigationProps {
  projectId: string;
  currentBucket: "classify" | "mark" | "extract";
}

export const BucketNavigation = ({ projectId, currentBucket }: BucketNavigationProps) => {
  const router = useRouter();

  const buckets = [
    {
      id: "classify",
      name: "Classify",
      icon: Icons.filter,
      path: `/project/${projectId}/classify`,
      description: "Classify documents by category and assets",
    },
    {
      id: "mark",
      name: "Mark",
      icon: Icons.boxes,
      path: `/project/${projectId}/mark`,
      description: "Mark and annotate documents",
    },
    {
      id: "extract",
      name: "Extract",
      icon: Icons.lock,
      path: `/project/${projectId}/extract`,
      description: "Extract data from documents",
    },
  ];

  return (
    <div className="mx-auto flex items-center gap-4">
      {/* Stepper Navigation */}
      <div className="flex items-center gap-4">
        {buckets.map((bucket, index) => {
          const Icon = bucket.icon;
          const isActive = currentBucket === bucket.id;
          const isCompleted = buckets.findIndex((b) => b.id === currentBucket) > index;

          return (
            <React.Fragment key={bucket.id}>
              <div className="flex flex-col items-center">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    router.push(bucket.path, {
                      scroll: false,
                    })
                  }
                  className={`flex h-12 w-12 items-center justify-center rounded-full p-0 ${
                    isActive
                      ? "bg-primary text-white"
                      : isCompleted
                        ? "bg-green-100 text-green-600"
                        : "bg-gray-100 text-gray-500"
                  }`}
                >
                  <Icon className="h-6 w-6" />
                </Button>
                <p
                  className={`mt-2 text-sm font-medium ${
                    isActive ? "text-primary" : isCompleted ? "text-green-600" : "text-gray-500"
                  }`}
                >
                  {bucket.name}
                </p>
              </div>

              {index < buckets.length - 1 && (
                <div className="mb-5 h-[2px] w-6 border-t border-dashed border-gray-400"></div>
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};
