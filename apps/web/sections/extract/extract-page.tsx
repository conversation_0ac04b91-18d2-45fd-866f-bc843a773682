"use client";
import React from "react";
import { SearchBar } from "@/components/search-bar";
import { SubNavBar } from "@/components/sub-nav-bar";
import { ExtractContainer } from "./components/extract-container";
import { BreadCrumbs } from "@/components/breadcrumbs";

interface ExtractPageProps {
  projectId: string;
}

export const ExtractPage = ({ projectId }: ExtractPageProps) => {
  return (
    <div className="flex h-full flex-col space-y-2">
      <SearchBar />

      <SubNavBar />

      <div className="min-w-0 flex-1">
        <ExtractContainer projectId={projectId} />
      </div>
    </div>
  );
};
