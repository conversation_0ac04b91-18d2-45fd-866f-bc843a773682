import { useInfiniteQuery } from "@tanstack/react-query";
import { useTenantQuery<PERSON>ey, useHasTenant } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";
import type { BaseResponse, ProjectListResponse } from "@/types";

export function useGetProjectsList(limit = 10) {
  const queryKey = useTenantQueryKey(["projects", "list", limit]);
  const hasTenant = useHasTenant();

  return useInfiniteQuery({
    queryKey,
    initialPageParam: undefined as string | undefined,
    queryFn: async ({ pageParam }: { pageParam?: string | undefined }) => {
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.post<BaseResponse<ProjectListResponse>>(`/projects/find`, {
        limit,
        cursor: pageParam,
      });
      return response?.data?.data;
    },
    getNextPageParam: (lastPage) => lastPage?.nextCursor ?? undefined,
    enabled: hasTenant,
  });
}
