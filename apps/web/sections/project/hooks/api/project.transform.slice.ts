import { ProjectResponse } from "@/types";
import { EditProjectPayload } from "../../components/forms/validation/edit-project-validation";

/**
 * Transform project data from API format to form format
 * This function converts the nested categories structure back to individual form fields
 */
export function transformProjectToFormData(project: ProjectResponse): EditProjectPayload {
  // Helper function to find glossary item by category name
  const findGlossaryItem = (categoryName: string) => {
    const category = project.categories.find((cat) => cat.glossaryCategoryName === categoryName);
    return category?.items[0] || null;
  };

  // Helper function to find all glossary items by category name (for arrays like Tags)
  const findGlossaryItems = (categoryName: string) => {
    const category = project.categories.find((cat) => cat.glossaryCategoryName === categoryName);
    return category?.items || [];
  };

  return {
    assignee: {
      id: project.assignee.id,
      name: project.assignee.name || "",
      role: project.assignee.role as "<PERSON><PERSON><PERSON><PERSON>",
    },
    code: project.code,
    vessel: project.vessel,
    Client: findGlossaryItem("Client") || {
      glossaryItemId: "",
      glossaryItemName: "",
    },
    Category: findGlossaryItem("Category") || {
      glossaryItemId: "",
      glossaryItemName: "",
    },
    Tags: findGlossaryItems("Tags").map((item) => ({
      glossaryItemId: item.glossaryItemId,
      glossaryItemName: item.glossaryItemName || "",
    })),
    Shipyard: findGlossaryItem("Shipyard") || {
      glossaryItemId: "",
      glossaryItemName: "",
    },
    "Vessel Type": findGlossaryItem("Vessel Type") || {
      glossaryItemId: "",
      glossaryItemName: "",
    },
    hullNumber: project.hullNumber,
    imoNumber: project.imoNumber,
    vesselDeliveryDate: project.vesselDeliveryDate
      ? new Date(project.vesselDeliveryDate).toISOString()
      : "",
  };
}

/**
 * Transform form data to API update format
 * Reuses the existing transform logic from create but for update payload
 */
export function transformEditProjectRequest(
  formData: EditProjectPayload,
  getGlossaryByName: (name: string) => any | undefined
) {
  const categories: any[] = [];

  const addToCategory = (items: any | any[], glossaryName: string) => {
    if (!items) return;

    const glossary = getGlossaryByName(glossaryName);
    if (!glossary) {
      console.warn(`Glossary "${glossaryName}" not found in context`);
      return;
    }

    const categoryId = glossary.id;
    const categoryName = glossary.name;

    // Find existing category or create new one
    let existingCategory = categories.find((cat) => cat.glossaryCategoryId === categoryId);

    if (!existingCategory) {
      existingCategory = {
        glossaryCategoryId: categoryId,
        glossaryCategoryName: categoryName,
        items: [],
      };
      categories.push(existingCategory);
    }

    // Add items to category
    const itemsArray = Array.isArray(items) ? items : [items];
    itemsArray.forEach((item) => {
      // Avoid duplicates
      const exists = existingCategory.items.some(
        (existingItem: any) => existingItem.glossaryItemId === item.glossaryItemId
      );
      if (!exists) {
        existingCategory.items.push({
          glossaryItemId: item.glossaryItemId,
          glossaryItemName: item.glossaryItemName,
        });
      }
    });
  };

  // Map form fields to their corresponding glossary categories
  const glossaryMapping = {
    Client: "Client",
    Category: "Category",
    Tags: "Tags",
    Shipyard: "Shipyard",
    "Vessel Type": "Vessel Type",
  };

  // Process each glossary field
  addToCategory(formData.Client, glossaryMapping.Client);
  addToCategory(formData.Category, glossaryMapping.Category);
  addToCategory(formData.Tags, glossaryMapping.Tags);
  addToCategory(formData.Shipyard, glossaryMapping.Shipyard);
  addToCategory(formData["Vessel Type"], glossaryMapping["Vessel Type"]);

  return {
    assignee: formData.assignee,
    code: formData.code,
    vessel: formData.vessel,
    categories,
    hullNumber: formData.hullNumber,
    imoNumber: formData.imoNumber,
    vesselDeliveryDate: new Date(formData.vesselDeliveryDate),
  };
}
