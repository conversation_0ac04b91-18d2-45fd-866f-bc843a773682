import { useQuery } from "@tanstack/react-query";
import { useTenantQuery<PERSON>ey, useHasTenant } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";
import { BaseResponse, ProjectGetResponse } from "@/types";

// GET request with useQuery
export function useProjectData(projectId: string) {
  const queryKey = useTenantQueryKey(["projects", projectId]);
  const hasTenant = useHasTenant();

  return useQuery({
    queryKey,
    queryFn: async () => {
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.get<BaseResponse<ProjectGetResponse>>(
        `/projects/${projectId}`
      );
      return response.data?.data;
    },
    enabled: hasTenant && !!projectId,
  });
}
