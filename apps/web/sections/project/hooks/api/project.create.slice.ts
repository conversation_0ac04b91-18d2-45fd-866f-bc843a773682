import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useInvalidateResourceQueries } from "@/hooks/use-tenant-aware-query";
import { ProjectCreateRequest, GlossaryResponse } from "@/types";
import { instance } from "@/axios-instance";
import { CreateProjectPayload } from "../../components/forms/validation/add-project-validation";

/**
 * Transform form data to API format
 *
 * This function takes the flat form structure with individual glossary fields
 * and transforms it into the grouped categories format expected by the API.
 *
 * @param formData - The form data from the project form
 * @param getGlossaryByName - Function to get glossary by name from context
 */
export function transformProjectRequest(
  formData: CreateProjectPayload,
  getGlossaryByName: (name: string) => GlossaryResponse | undefined
): ProjectCreateRequest {
  const categories: ProjectCreateRequest["categories"] = [];

  // Helper function to add items to categories using glossary context
  const addToCategory = (items: any | any[], glossaryName: string) => {
    if (!items) return;

    // Get glossary info from context
    const glossary = getGlossaryByName(glossaryName);
    if (!glossary) {
      console.warn(`Glossary "${glossaryName}" not found in context`);
      return;
    }

    // Handle both single item and array
    const itemsArray = Array.isArray(items) ? items : [items];

    itemsArray.forEach((item) => {
      if (item && item.glossaryItemId && item.glossaryItemName) {
        // Use glossary info from context
        const categoryId = glossary.id;
        const categoryName = glossary.name;

        // Find existing category or create new one
        let category = categories.find((cat) => cat.glossaryCategoryId === categoryId);
        if (!category) {
          category = {
            glossaryCategoryId: categoryId,
            glossaryCategoryName: categoryName,
            items: [],
          };
          categories.push(category);
        }

        // Add the item to the category (avoid duplicates)
        const existingItem = category.items.find(
          (existingItem) => existingItem.glossaryItemId === item.glossaryItemId
        );
        if (!existingItem) {
          category.items.push({
            glossaryItemId: item.glossaryItemId,
            glossaryItemName: item.glossaryItemName,
          });
        }
      }
    });
  };

  // Define glossary name mapping for each field
  // You can customize these mappings based on your actual glossary structure
  const glossaryMapping = {
    Client: "Manuals Category",
    Category: "Manuals Category",
    Tags: "Manuals Category",
    Shipyard: "Manuals Category",
    "Vessel Type": "Manuals Category",
  };

  // Process each glossary field from the form with their respective glossary names
  addToCategory(formData.Client, glossaryMapping.Client);
  addToCategory(formData.Category, glossaryMapping.Category);
  addToCategory(formData.Tags, glossaryMapping.Tags);
  addToCategory(formData.Shipyard, glossaryMapping.Shipyard);
  addToCategory(formData["Vessel Type"], glossaryMapping["Vessel Type"]);

  return {
    assignee: formData.assignee,
    code: formData.code,
    vessel: formData.vessel,
    categories,
    hullNumber: formData.hullNumber,
    imoNumber: formData.imoNumber,
    vesselDeliveryDate: formData.vesselDeliveryDate
      ? new Date(formData.vesselDeliveryDate)
      : new Date(),
  };
}

// POST request with useMutation that uses glossary context
export function useCreateProject() {
  const queryClient = useQueryClient();
  const invalidateProjectQueries = useInvalidateResourceQueries("projects");

  return useMutation({
    mutationFn: async (data: {
      formData: CreateProjectPayload;
      getGlossaryByName: (name: string) => GlossaryResponse | undefined;
    }) => {
      // Transform form data to API format using glossary context
      const projectData = transformProjectRequest(data.formData, data.getGlossaryByName);

      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.post("/project", projectData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all project-related queries for the current tenant
      invalidateProjectQueries(queryClient);
    },
  });
}

// Simplified hook for use with glossary context
export function useCreateProjectWithContext() {
  const queryClient = useQueryClient();
  const invalidateProjectQueries = useInvalidateResourceQueries("projects");

  return useMutation({
    mutationFn: async (formData: CreateProjectPayload) => {
      // This version expects the transform to be done externally with context
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.post("/project", formData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all project-related queries for the current tenant
      invalidateProjectQueries(queryClient);
    },
  });
}
