"use client";

import { useCallback, useMemo } from "react";
import { useInfiniteQuery } from "@tanstack/react-query";
import { useTenantQuery<PERSON>ey, useHasTenant } from "@/hooks/use-tenant-aware-query";
import { instance } from "@/axios-instance";
import type {
  BaseResponse,
  ProjectListResponse,
  ProjectFilters as ApiProjectFilters,
  ProjectSort as ApiProjectSort,
} from "@/types";
// Remove these imports since we now use types from @/types

interface UseFilteredProjectsProps {
  limit?: number;
  filters?: ApiProjectFilters;
  sort?: ApiProjectSort;
}

export function useFilteredProjects({ limit = 20, filters, sort }: UseFilteredProjectsProps = {}) {
  const hasTenant = useHasTenant();

  // Convert component filters to API filters
  const apiFilters = useMemo((): ApiProjectFilters => {
    const cleanFilters: ApiProjectFilters = {};

    if (filters?.code) {
      cleanFilters.code = filters.code;
    }
    if (filters?.vessel) {
      cleanFilters.vessel = filters.vessel;
    }
    if (filters?.status && filters.status.length > 0) {
      cleanFilters.status = filters.status as any[];
    }
    if (filters?.projectHandler) {
      cleanFilters.projectHandler = filters.projectHandler;
    }

    return cleanFilters;
  }, [filters]);

  // Convert component sort to API sort
  const apiSort = useMemo((): ApiProjectSort | undefined => {
    if (!sort) return undefined;
    return {
      field: sort.field,
      direction: sort.direction,
    };
  }, [sort]);

  // Create a stable query key that includes filters and sort
  const queryKey = useTenantQueryKey([
    "projects",
    "filtered",
    { limit, filters: apiFilters, sort: apiSort },
  ]);

  const queryFn = useCallback(
    async ({ pageParam }: { pageParam?: string }) => {
      const payload = {
        limit,
        cursor: pageParam,
        filters: apiFilters,
        sort: apiSort,
      };

      const response = await instance.post<BaseResponse<ProjectListResponse>>(
        "/projects/find",
        payload
      );
      return response.data.data;
    },
    [limit, apiFilters, apiSort]
  );

  const query = useInfiniteQuery({
    queryKey,
    queryFn,
    initialPageParam: undefined as string | undefined,
    getNextPageParam: (lastPage) => lastPage?.nextCursor ?? undefined,
    enabled: hasTenant,
  });

  // Flatten all pages into a single array
  const projects = useMemo(() => {
    return query.data?.pages.flatMap((page) => page?.projects ?? []) ?? [];
  }, [query.data]);

  // Handle scroll end for infinite loading
  const handleScrollEnd = useCallback(() => {
    if (query.hasNextPage && !query.isFetchingNextPage) {
      query.fetchNextPage();
    }
  }, [query.hasNextPage, query.isFetchingNextPage, query.fetchNextPage]);

  return {
    ...query,
    projects,
    handleScrollEnd,
  };
}
