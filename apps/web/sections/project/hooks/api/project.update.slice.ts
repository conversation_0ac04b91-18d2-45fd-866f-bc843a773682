import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useInvalidateResourceQueries } from "@/hooks/use-tenant-aware-query";
import { ProjectUpdateRequest } from "@/types";
import { instance } from "@/axios-instance";

export function useEditProject() {
  const queryClient = useQueryClient();
  const invalidateProjectQueries = useInvalidateResourceQueries("projects");

  return useMutation({
    mutationFn: async (data: { projectId: string; projectData: ProjectUpdateRequest }) => {
      // x-tenant-id header is automatically added by axios interceptor
      const response = await instance.patch(`/project/${data.projectId}`, data.projectData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all project-related queries for the current tenant
      invalidateProjectQueries(queryClient);
    },
  });
}
