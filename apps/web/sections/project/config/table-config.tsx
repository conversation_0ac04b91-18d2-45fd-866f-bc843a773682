import { TableColumnHeader } from "@/components/custom-virtual-list";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { ColumnDef } from "@tanstack/react-table";
import { ProjectResponse, UserProfileResponse, Action, Resource } from "@/types";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { CurrentTenantData } from "@/providers/tenant-selector";
import { CanAccess } from "@/providers/access-control";

export const getColumns = (
  onEditProject: ((project: ProjectResponse) => void) | undefined,
  metaData: UserProfileResponse | null,
  currentTenantData: CurrentTenantData | null
) => {
  const columns: ColumnDef<ProjectResponse>[] = [
    {
      accessorKey: "vessel",
      header: ({ column }) => <TableColumnHeader column={column} title="Vessel Name" />,
      enableSorting: false,
      cell: ({ row }) => (
        <div>
          <p className="text-lg font-semibold">{row?.original?.vessel}</p>
          <p className="text-muted-foreground text-sm">{row?.original?.code}</p>
          <p className="text-muted-foreground text-sm">
            {row?.original?.categories.find(
              (category) => category.glossaryCategoryName === "Client"
            )?.items[0]?.glossaryItemName || "-"}
          </p>
        </div>
      ),
      meta: {
        isGrow: true,
      },
    },
    {
      accessorKey: "vesselType",
      header: "Vessel Type",
      enableSorting: false,
      cell: ({ row }) =>
        row?.original?.categories?.find(
          (category) => category.glossaryCategoryName === "Vessel Type"
        )?.items[0]?.glossaryItemName || "-",
      meta: {
        isGrow: true,
      },
    },
    {
      accessorKey: "totalFiles",
      header: "Total Files",
      enableSorting: false,
      cell: ({ row }) => row?.original?.fileCount || 0,
      meta: {
        isGrow: true,
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      enableSorting: false,
      meta: {
        isGrow: true,
      },
      cell: ({ row }) => {
        const status = row.original?.status;

        const bgColor = "bg-green-100"; // inactive or any other fallback
        const color = "text-green-800";
        return (
          <Badge className={bgColor}>
            <span className={color}>{status}</span>
          </Badge>
        );
      },
    },
    {
      accessorKey: "projectHandler",
      header: "Project Handler",
      meta: {
        isGrow: true,
      },
      enableSorting: false,
      cell: ({ row }) => {
        const projectHandler = row?.original?.assignee;
        return projectHandler?.name || "Not assigned";
      },
    },

    {
      id: "actions",
      header: "",
      size: 50,
      enableSorting: false,
      cell: ({ row }) => {
        const project = row.original;
        return (
          <CanAccess privilege={"update" as Action.Update} resource={"project" as Resource.Project}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <Icons.moreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {/* <DropdownMenuItem onClick={() => router.push(`/projects/${project.id}`)}>
                <Icons.eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem> */}
                <DropdownMenuItem onClick={() => onEditProject?.(project)}>
                  <Icons.edit className="mr-2 h-4 w-4" />
                  Edit Project
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CanAccess>
        );
      },
    },
  ];

  return columns;
};
