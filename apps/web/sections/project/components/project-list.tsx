"use client";

import React from "react";
import ProjectTable from "./project-table";
import ProjectTableSkeleton from "./skeleton/project-table-skeleton";
import { ErrorBoundary } from "@/components/error-boundary";
import { useFilteredProjects } from "../hooks/api";
import { useProjectFilterStore } from "@/stores/project-filter-store";

interface ProjectListProps {
  onRowClick?: (project: import("@api-types").ProjectResponse) => void;
  selectedProject?: import("@api-types").ProjectResponse;
  onEditProject?: (project: import("@api-types").ProjectResponse) => void;
}

const ProjectList: React.FC<ProjectListProps> = ({
  onRowClick,
  selectedProject,
  onEditProject,
}) => {
  const filters = useProjectFilterStore((state) => state.filters);
  const sort = useProjectFilterStore((state) => state.sort);

  const { projects, isLoading, error, hasNextPage, isFetchingNextPage, refetch, handleScrollEnd } =
    useFilteredProjects({
      limit: 5,
      filters,
      sort,
    });

  if (isLoading) return <ProjectTableSkeleton />;

  if (error) return <ErrorBoundary error={error} onRetry={() => refetch()} />;

  return (
    <ProjectTable
      projectData={projects ?? []}
      onEndReached={handleScrollEnd}
      hasNextPage={hasNextPage}
      isFetchingNextPage={isFetchingNextPage}
      onRowClick={onRowClick}
      selectedProject={selectedProject}
      onEditProject={onEditProject}
    />
  );
};

export default ProjectList;
