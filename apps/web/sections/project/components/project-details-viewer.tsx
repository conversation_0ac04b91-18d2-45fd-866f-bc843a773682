"use client";

import { Badge } from "@repo/ui/components/badge";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { Separator } from "@repo/ui/components/separator";
import { Skeleton } from "@repo/ui/components/skeleton";
import React from "react";

import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import { useProjectData } from "../hooks/api";

interface ProjectDetailsViewerProps {
  projectId?: string;
}

const ProjectDetailsViewer: React.FC<ProjectDetailsViewerProps> = ({ projectId }) => {
  const router = useRouter();
  const { data: project, isLoading, error } = useProjectData(projectId || "");

  const formatDate = (date: Date | string) => {
    return dayjs(date).format("DD-MM-YYYY");
  };

  // Show loading skeleton while fetching data
  if (isLoading) {
    return <ProjectDetailsViewerSkeleton />;
  }

  // Show error state
  if (error) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Icons.alertCircle className="text-muted-foreground mx-auto h-12 w-12" />
          <h3 className="mt-4 text-lg font-semibold">Error Loading Project</h3>
          <p className="text-muted-foreground">Failed to load project details. Please try again.</p>
        </div>
      </div>
    );
  }

  // Show empty state when no project ID is provided
  if (!projectId) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Icons.folder className="text-muted-foreground mx-auto h-12 w-12" />
          <h3 className="mt-4 text-lg font-semibold">No Project Selected</h3>
          <p className="text-muted-foreground">
            Select a project from the table to view its details
          </p>
        </div>
      </div>
    );
  }

  // Show empty state when project data is not available
  if (!project) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Icons.folder className="text-muted-foreground mx-auto h-12 w-12" />
          <h3 className="mt-4 text-lg font-semibold">Project Not Found</h3>
          <p className="text-muted-foreground">The selected project could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="h-[50px] w-full rounded-xl bg-[#C7ECEB]"></div>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <p className="text-muted-foreground text-sm">
            {project.categories.find((category) => category.glossaryCategoryName === "Client")
              ?.items[0]?.glossaryItemName || "-"}{" "}
            | {project.code}
          </p>
          <h2 className="mt-1 text-xl font-semibold capitalize">{project.vessel}</h2>
        </div>
        {/* {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            <Icons.x className="h-4 w-4" />
          </Button>
        )} */}
      </div>

      <Separator />

      {/* Project Info Card */}

      <h1 className="text-base font-semibold text-gray-800">Project Info</h1>

      <div className="flex flex-row space-x-2">
        <p className="text-sm font-medium text-gray-600">Start Date:</p>
        <p className="text-sm text-gray-600">{formatDate(project.createdAt)}</p>
      </div>
      <div className="flex flex-row space-x-2">
        <p className="text-sm font-medium text-gray-600">Created By:</p>
        <p className="text-sm text-gray-600">{project.assignee?.name || "Not assigned"}</p>
      </div>

      <div className="flex flex-row space-x-2">
        <p className="text-sm font-medium text-gray-600">Last Modified:</p>
        <p className="text-sm text-gray-600">{formatDate(project.updatedAt)}</p>
      </div>

      <Separator />

      <h1 className="text-base font-semibold text-gray-800">Status & Progress</h1>

      <div className="flex flex-row items-center space-x-2">
        <p className="text-sm font-medium text-gray-600">Status:</p>

        <Badge className="mt-1">{project.status}</Badge>
      </div>
      {/* Progress bar placeholder - you can implement actual progress calculation */}
      {/* <Progress value={80} className="w-full" /> */}
      <Separator />
      {/* File Breakdown Card - Placeholder */}

      <h1 className="text-base font-semibold text-gray-800">File Breakdown</h1>

      <div className="flex flex-row space-x-2">
        <p className="text-sm font-medium text-gray-600">Total Files:</p>
        <p className="text-sm text-gray-600">{project.fileCount || 0}</p>
      </div>
      {/* <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-blue-600">Classified:</span>
          <span>43</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-orange-600">Marked:</span>
          <span>50</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-green-600">Extracted:</span>
          <span>70</span>
        </div>
      </div> */}

      {/* Action Button */}
      <Button
        className="w-full"
        size="lg"
        onClick={() =>
          router.push(`/project/${project.id}`, {
            scroll: false,
          })
        }
      >
        View Project Detail
      </Button>
    </div>
  );
};

// Skeleton component for loading state
const ProjectDetailsViewerSkeleton: React.FC = () => {
  return (
    <div className="space-y-3">
      {/* Header Banner Skeleton */}
      <Skeleton className="h-[50px] w-full rounded-xl" />

      {/* Header Info Skeleton */}
      <div className="flex items-center justify-between">
        <div>
          <Skeleton className="h-4 w-32" />
          <Skeleton className="mt-1 h-6 w-48" />
        </div>
      </div>

      <Separator />

      {/* Project Info Section */}
      <Skeleton className="h-5 w-24" />

      <div className="space-y-2">
        <div className="flex flex-row space-x-2">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-24" />
        </div>
        <div className="flex flex-row space-x-2">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-32" />
        </div>
        <div className="flex flex-row space-x-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-24" />
        </div>
      </div>

      <Separator />

      {/* Status & Progress Section */}
      <Skeleton className="h-5 w-32" />

      <div className="flex flex-row items-center space-x-2">
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-6 w-16 rounded" />
      </div>

      <Separator />

      {/* File Breakdown Section */}
      <Skeleton className="h-5 w-28" />

      <div className="flex flex-row space-x-2">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-8" />
      </div>

      {/* Action Button Skeleton */}
      <Skeleton className="h-10 w-full" />
    </div>
  );
};

export default ProjectDetailsViewer;
export { ProjectDetailsViewerSkeleton };
