"use client";

import { SearchBar } from "@/components/search-bar";
import React from "react";
import { useProjectFilterStore, useProjectFilterSelectors } from "@/stores/project-filter-store";

const ProjectSearchHeader: React.FC = () => {
  const setSearch = useProjectFilterStore((state) => state.setSearch);
  const { searchValue } = useProjectFilterSelectors();

  return (
    <SearchBar
      value={searchValue}
      onChange={setSearch}
      placeholder="Search projects by project code..."
    />
  );
};

export default ProjectSearchHeader;
