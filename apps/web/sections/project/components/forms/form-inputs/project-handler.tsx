"use client";

import React, { useState, useMemo } from "react";
import { Control, Controller } from "react-hook-form";
import { But<PERSON> } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@repo/ui/components/command";
import { Badge } from "@repo/ui/components/badge";
import { useFilteredUsers } from "@/sections/users/hooks/api";
import { UserResponse } from "@/types";
import { twMerge } from "tailwind-merge";

interface ProjectHandlerValue {
  id: string;
  name: string;
  role: "ProjectHandler";
}

interface ProjectHandlerProps {
  control: Control<any>;
  name: string;
  error?: any; // Allow any error structure for nested fields
  defaultValue?: ProjectHandlerValue;
  placeholder?: string;
  className?: string;
}

const ProjectHandler: React.FC<ProjectHandlerProps> = ({
  control,
  name,
  error,
  defaultValue,
  placeholder = "Select project handler...",
  className,
}) => {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");

  // Filter users to only show ProjectHandler role
  const userFilters = useMemo(
    () => ({
      role: ["ProjectHandler"],
      name: searchValue.trim() || undefined,
      status: ["Active"],
    }),
    [searchValue]
  );

  const { users, isLoading, hasNextPage, isFetchingNextPage, handleScrollEnd } = useFilteredUsers({
    limit: 20,
    filters: userFilters,
  });

  // Filter users to only ProjectHandler role (additional safety check)
  const projectHandlers = useMemo(
    () => users.filter((user: UserResponse) => user.role === "ProjectHandler"),
    [users]
  );

  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => (
        <div className="flex flex-col space-y-1">
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={open}
                className={twMerge(
                  "h-10 w-full justify-between text-left font-normal",
                  !field.value && "text-muted-foreground",
                  error && "border-red-500",
                  className
                )}
              >
                {field.value ? (
                  <div className="flex items-center gap-2">
                    <span className="truncate">{field.value.name}</span>
                    {/* <Badge variant="secondary" className="text-xs">
                      Project Handler
                    </Badge> */}
                  </div>
                ) : (
                  placeholder
                )}
                <Icons.chevronUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[400px] p-0" align="start">
              <Command shouldFilter={false}>
                <CommandInput
                  placeholder="Search project handlers..."
                  value={searchValue}
                  onValueChange={setSearchValue}
                />
                <CommandList>
                  {isLoading && (
                    <div className="flex items-center justify-center py-6">
                      <Icons.spinner className="h-4 w-4 animate-spin" />
                      <span className="text-muted-foreground ml-2 text-sm">Loading...</span>
                    </div>
                  )}

                  {!isLoading && projectHandlers.length === 0 && (
                    <CommandEmpty>
                      {searchValue
                        ? "No project handlers found."
                        : "No project handlers available."}
                    </CommandEmpty>
                  )}

                  {projectHandlers.length > 0 && (
                    <>
                      {/* Clear selection button - only show if there's a selection */}
                      {field.value && (
                        <div className="border-b">
                          <CommandItem
                            onSelect={() => {
                              field.onChange(undefined);
                              setSearchValue("");
                              setOpen(false);
                            }}
                            className="text-muted-foreground hover:text-foreground"
                          >
                            <Icons.x className="mr-2 h-4 w-4" />
                            Clear selection
                          </CommandItem>
                        </div>
                      )}
                      <CommandGroup>
                        {projectHandlers.map((user: UserResponse) => (
                          <CommandItem
                            key={user.id}
                            value={user.id}
                            onSelect={() => {
                              const selectedValue: ProjectHandlerValue = {
                                id: user.id,
                                name: user.name,
                                role: "ProjectHandler",
                              };
                              field.onChange(selectedValue);
                              setOpen(false);
                              setSearchValue("");
                            }}
                          >
                            <div className="flex w-full items-center justify-between">
                              <div className="flex flex-col">
                                <span className="font-medium">{user.name}</span>
                                <span className="text-muted-foreground text-sm">
                                  {user.emailId}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="text-xs">
                                  {user.role}
                                </Badge>
                                {field.value?.id === user.id && <Icons.check className="h-4 w-4" />}
                              </div>
                            </div>
                          </CommandItem>
                        ))}

                        {hasNextPage && (
                          <div className="border-t">
                            <Button
                              variant="ghost"
                              className="w-full justify-center py-2"
                              onClick={handleScrollEnd}
                              disabled={isFetchingNextPage}
                            >
                              {isFetchingNextPage ? (
                                <>
                                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                                  Loading more...
                                </>
                              ) : (
                                "Load more"
                              )}
                            </Button>
                          </div>
                        )}
                      </CommandGroup>
                    </>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      )}
    />
  );
};

export default ProjectHandler;
