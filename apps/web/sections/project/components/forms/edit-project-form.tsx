"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";

import { toast } from "sonner";

import { ProjectResponse } from "@/types";
import { twMerge } from "tailwind-merge";

import { useGlossaryCategory } from "@/providers/glossary-provider/use-glossary-category";
import { DatePickerField } from "@repo/ui/components/date-picker";
import { Separator } from "@repo/ui/components/separator";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useEditProject } from "../../hooks/api";
import {
  transformEditProjectRequest,
  transformProjectToFormData,
} from "../../hooks/api/project.transform.slice";
import { ProjectHandler } from "./form-inputs";
import GlossaryHandler from "./form-inputs/glossary-handler";
import { EditProjectPayload, editProjectValidation } from "./validation/edit-project-validation";

type EditProjectFormProps = {
  setOpen: (open: boolean) => void;
  selectedProject: ProjectResponse;
};

const EditProjectForm: React.FC<EditProjectFormProps> = ({ setOpen, selectedProject }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    watch,
    reset,
  } = useForm<EditProjectPayload>({
    resolver: zodResolver(editProjectValidation),
    defaultValues: transformProjectToFormData(selectedProject),
  });

  const editProject = useEditProject();
  const { getGlossaryByName } = useGlossaryCategory();

  // Reset form when selectedProject changes
  useEffect(() => {
    console.log(selectedProject, "selectedProject");
    console.log(
      "transformProjectToFormData(selectedProject)",
      transformProjectToFormData(selectedProject)
    );
    reset(transformProjectToFormData(selectedProject));
  }, [selectedProject, reset]);

  console.log("watch", watch());

  const onSubmit = (data: EditProjectPayload) => {
    console.log("submitted", data);

    const transformedData = transformEditProjectRequest(data, getGlossaryByName);

    editProject.mutate(
      {
        projectId: selectedProject.id,
        projectData: transformedData,
      },
      {
        onSuccess: () => {
          setOpen(false);
          toast.success("Project updated successfully");
        },
        onError: (err: any) => {
          toast.error(err.message || "Failed to update project");
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="p-2">
      <h2 className="text-lg font-semibold text-gray-600">Edit Project Details</h2>
      <Separator />
      <div className="mt-4 grid grid-cols-1 gap-x-6 gap-y-1 md:grid-cols-3">
        {/* Project Handler */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Project Handler
            <span className="ml-1 text-red-500">*</span>
          </label>
          <ProjectHandler
            control={control}
            name="assignee"
            error={errors.assignee}
            placeholder="Select project handler..."
          />
          <p className="text-xs text-red-500">{errors?.assignee?.message || "\u00A0"}</p>
        </div>

        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Project Code
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("code")}
            placeholder="Code"
            className={twMerge("h-10", errors.code && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.code?.message || "\u00A0"}</p>
        </div>

        {/* Vessel Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Vessel Name
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("vessel")}
            placeholder="Vessel Name"
            className={twMerge("h-10", errors.vessel && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.vessel?.message || "\u00A0"}</p>
        </div>

        {/* Client */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Client
            <span className="ml-1 text-red-500">*</span>
          </label>
          <GlossaryHandler
            control={control}
            name="Client"
            glossaryName="Client"
            multiple={false}
            placeholder="Select client..."
          />
          <p className="text-xs text-red-500">{errors?.Client?.message || "\u00A0"}</p>
        </div>
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Category
            <span className="ml-1 text-red-500">*</span>
          </label>
          <GlossaryHandler
            control={control}
            name="Category"
            glossaryName="Category"
            multiple={false}
            placeholder="Select Category..."
          />
          <p className="text-xs text-red-500">{errors?.Category?.message || "\u00A0"}</p>
        </div>
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">Tags</label>
          <GlossaryHandler
            control={control}
            name="Tags"
            glossaryName="Tags"
            multiple={true}
            placeholder="Select Tags..."
          />
          <p className="text-xs text-red-500">{errors?.Tags?.message || "\u00A0"}</p>
        </div>
      </div>

      <div className="h-4" />

      <h2 className="text-lg font-semibold text-gray-600">Vessel Details</h2>
      <Separator />
      <div className="mt-4 grid grid-cols-1 gap-x-6 gap-y-1 md:grid-cols-3">
        {/* Shipyard */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Shipyard
            <span className="ml-1 text-red-500">*</span>
          </label>
          <GlossaryHandler
            control={control}
            name="Shipyard"
            glossaryName="Shipyard"
            multiple={false}
            placeholder="Select Shipyard..."
          />
          <p className="text-xs text-red-500">{errors?.Shipyard?.message || "\u00A0"}</p>
        </div>

        {/* Hull Number */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Hull Number
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("hullNumber")}
            placeholder="Hull Number"
            className={twMerge("h-10", errors.hullNumber && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.hullNumber?.message || "\u00A0"}</p>
        </div>

        {/* IMO Number */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            IMO Number
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("imoNumber")}
            placeholder="IMO Number"
            className={twMerge("h-10", errors.imoNumber && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.imoNumber?.message || "\u00A0"}</p>
        </div>

        {/* Vessel Type */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Vessel Type
            <span className="ml-1 text-red-500">*</span>
          </label>
          <GlossaryHandler
            control={control}
            name="Vessel Type"
            glossaryName="Vessel Type"
            multiple={false}
            placeholder="Select Vessel Type..."
          />
          <p className="text-xs text-red-500">{errors?.["Vessel Type"]?.message || "\u00A0"}</p>
        </div>
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Vessel Delivery Date
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Controller
            control={control}
            name="vesselDeliveryDate"
            render={({ field }) => (
              <DatePickerField
                value={field.value}
                onChange={field.onChange}
                placeholder="Select delivery date..."
                error={!!errors.vesselDeliveryDate}
              />
            )}
          />
          <p className="text-xs text-red-500">{errors?.vesselDeliveryDate?.message || "\u00A0"}</p>
        </div>
      </div>

      <div className="flex justify-center">
        <Button type="submit" className="w-full px-8 md:w-auto" disabled={editProject.isPending}>
          {editProject.isPending ? "Updating..." : "Update Project"}
        </Button>
      </div>
    </form>
  );
};

export default EditProjectForm;
