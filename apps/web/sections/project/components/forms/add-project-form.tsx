"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";

import { toast } from "sonner";

import { twMerge } from "tailwind-merge";

import { CreateProjectPayload, addProjectValidation } from "./validation/add-project-validation";
import { useForm, Controller } from "react-hook-form";
import { Separator } from "@repo/ui/components/separator";
import { DatePickerField } from "@repo/ui/components/date-picker";
import { ProjectHandler } from "./form-inputs";
import GlossaryHandler from "./form-inputs/glossary-handler";
import { useCreateProject } from "../../hooks/api";
import { useGlossaryCategory } from "@/providers/glossary-provider/use-glossary-category";

type AddProjectFormProps = {
  setOpen: (open: boolean) => void;
};

const AddProjectForm: React.FC<AddProjectFormProps> = ({ setOpen }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    watch,
  } = useForm<CreateProjectPayload>({
    resolver: zodResolver(addProjectValidation), // ✅ use Zod schema
    defaultValues: {},
  });

  const createProject = useCreateProject();
  const { getGlossaryByName } = useGlossaryCategory();

  console.log("watch", watch());

  const onSubmit = (data: CreateProjectPayload) => {
    console.log("submitted", data);

    createProject.mutate(
      { formData: data, getGlossaryByName },
      {
        onSuccess: () => {
          setOpen(false);
          toast.success("Project created successfully");
        },
        onError: (err: any) => {
          toast.error(err.message || "Failed to create project");
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="p-2">
      <h2 className="text-lg font-semibold text-gray-600">Project Details</h2>
      <Separator />
      <div className="mt-4 grid grid-cols-1 gap-x-6 gap-y-1 md:grid-cols-3">
        {/* Project Handler */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Project Handler
            <span className="ml-1 text-red-500">*</span>
          </label>
          <ProjectHandler
            control={control}
            name="assignee"
            error={errors.assignee}
            placeholder="Select project handler..."
          />
          <p className="text-xs text-red-500">{errors?.assignee?.message || "\u00A0"}</p>
        </div>

        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Project Code
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("code")}
            placeholder="Code"
            className={twMerge("h-10", errors.code && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.code?.message || "\u00A0"}</p>
        </div>

        {/* Remarks */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Vessel Name
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("vessel")}
            placeholder="Vessel Name"
            className={twMerge("h-10", errors.vessel && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.vessel?.message || "\u00A0"}</p>
        </div>

        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Client
            <span className="ml-1 text-red-500">*</span>
          </label>
          <GlossaryHandler
            control={control}
            name="Client"
            glossaryName="Client"
            error={errors.Client}
            multiple={false}
            placeholder="Select client..."
          />
          <p className="text-xs text-red-500">{errors?.Client?.message || "\u00A0"}</p>
        </div>
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Category
            <span className="ml-1 text-red-500">*</span>
          </label>
          <GlossaryHandler
            control={control}
            name="Category"
            glossaryName="Category"
            error={errors.Category}
            multiple={false}
            placeholder="Select Category..."
          />
          <p className="text-xs text-red-500">{errors?.Category?.message || "\u00A0"}</p>
        </div>
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Tags
            {/* <span className="ml-1 text-red-500">*</span> */}
          </label>
          <GlossaryHandler
            control={control}
            name="Tags"
            glossaryName="Tags"
            error={errors.Tags}
            multiple={true}
            placeholder="Select tags..."
          />
          <p className="text-xs text-red-500">{errors?.Tags?.message || "\u00A0"}</p>
        </div>
      </div>

      <div className="h-4" />

      <h2 className="text-lg font-semibold text-gray-600">Vessel Details</h2>
      <Separator />
      <div className="mt-4 grid grid-cols-1 gap-x-6 gap-y-1 md:grid-cols-3">
        {/* Project Handler */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Shipyard
            <span className="ml-1 text-red-500">*</span>
          </label>
          <GlossaryHandler
            control={control}
            name="Shipyard"
            error={errors.Shipyard}
            glossaryName="Shipyard"
            placeholder="Select shipyard..."
          />
          <p className="text-xs text-red-500">{errors?.Shipyard?.message || "\u00A0"}</p>
        </div>

        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Hull Number
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("hullNumber")}
            placeholder="Hull Number"
            className={twMerge("h-10", errors.hullNumber && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.hullNumber?.message || "\u00A0"}</p>
        </div>

        {/* Remarks */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            IMO number
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("imoNumber")}
            placeholder="Vessel Name"
            className={twMerge("h-10", errors.imoNumber && "border-red-500")}
          />
          <p className="text-xs text-red-500">{errors?.imoNumber?.message || "\u00A0"}</p>
        </div>

        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Vessel Type
            <span className="ml-1 text-red-500">*</span>
          </label>
          <GlossaryHandler
            control={control}
            name="Vessel Type"
            glossaryName="Vessel Type"
            error={errors["Vessel Type"]}
            multiple={false}
            placeholder="Select Vessel Type..."
          />
          <p className="text-xs text-red-500">{errors?.["Vessel Type"]?.message || "\u00A0"}</p>
        </div>
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Vessel Delivery Date
            <span className="ml-1 text-red-500">*</span>
          </label>
          <Controller
            control={control}
            name="vesselDeliveryDate"
            render={({ field }) => (
              <DatePickerField
                value={field.value}
                onChange={field.onChange}
                placeholder="Select delivery date..."
                error={!!errors.vesselDeliveryDate}
              />
            )}
          />
          <p className="text-xs text-red-500">{errors?.vesselDeliveryDate?.message || "\u00A0"}</p>
        </div>
      </div>

      {/* Buttons */}
      <div className="flex justify-center">
        <Button type="submit" className="w-full px-8 md:w-auto" disabled={createProject.isPending}>
          {createProject.isPending ? "Creating..." : "Create"}
        </Button>
      </div>
    </form>
  );
};

export default AddProjectForm;
