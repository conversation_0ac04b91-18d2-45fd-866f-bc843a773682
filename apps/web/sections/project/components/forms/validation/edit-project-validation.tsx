import { z } from "zod";

export const editProjectValidation = z.object({
  assignee: z.object(
    {
      id: z.string().uuid("Invalid user ID format"),
      name: z.string().min(1, "Name is required").max(50, "Name must be at most 50 characters"),
      role: z.enum(["ProjectHandler"], {
        errorMap: () => ({ message: "Role is required" }),
      }),
    },
    { required_error: "Project handler is required" }
  ),

  code: z.string().min(1, "Code is required").max(10, "Code must be at most 10 characters"),
  vessel: z
    .string()
    .min(1, "Vessel name is required")
    .max(100, "Vessel name must be at most 100 characters"),

  Client: z.object(
    {
      glossaryItemId: z.string().uuid("Invalid item ID format"),
      glossaryItemName: z.string().min(1, "Item name is required"),
    },
    { required_error: "Client is required" }
  ),
  Category: z.object(
    {
      glossaryItemId: z.string().uuid("Invalid item ID format"),
      glossaryItemName: z.string().min(1, "Item name is required"),
    },
    {
      required_error: "Category is required",
    }
  ),

  Tags: z.array(
    z.object(
      {
        glossaryItemId: z.string().uuid("Invalid item ID format"),
        glossaryItemName: z.string().min(1, "Item name is required"),
      },
      { required_error: "Tag is required" }
    )
  ),

  Shipyard: z.object(
    {
      glossaryItemId: z.string().uuid("Invalid item ID format"),
      glossaryItemName: z.string().min(1, "Item name is required"),
    },
    {
      required_error: "Shipyard is required",
    }
  ),
  "Vessel Type": z.object(
    {
      glossaryItemId: z.string().uuid("Invalid item ID format"),
      glossaryItemName: z.string().min(1, "Item name is required"),
    },
    {
      required_error: "Vessel type is required",
    }
  ),
  hullNumber: z
    .string()
    .min(1, "Hull number is required")
    .max(50, "Hull number must be at most 50 characters"),
  imoNumber: z
    .string()
    .min(7, "IMO number is required")
    .max(7, "IMO number must be at most 7 characters"),
  vesselDeliveryDate: z
    .string()
    .min(1, "Vessel delivery date is required")
    .refine((val) => !isNaN(Date.parse(val)), {
      message: "Please enter a valid date",
    }),
});

export type EditProjectPayload = z.infer<typeof editProjectValidation>;
