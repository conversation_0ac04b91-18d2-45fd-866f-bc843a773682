"use client";

import React from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Checkbox } from "@repo/ui/components/checkbox";
import { ProjectFilters, ProjectStatus } from "@/types";
import { ProjectHandler } from "./form-inputs";

interface ProjectFilterFormProps {
  defaultValues: ProjectFilters;
  onSubmit: (values: ProjectFilters) => void;
  onClear: () => void;
  onCancel: () => void;
}

const statusOptions = [
  { value: "New", label: "New" },
  { value: "OnGoing", label: "On Going" },
  { value: "Completed", label: "Completed" },
];

export const ProjectFilterForm: React.FC<ProjectFilterFormProps> = ({
  defaultValues,
  onSubmit,
  onClear,
  onCancel,
}) => {
  const { handleSubmit, control, reset, register } = useForm<ProjectFilters>({
    defaultValues,
  });

  const handleClearAll = () => {
    reset({});
    onClear();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <h4 className="font-medium leading-none">Filter Projects</h4>
        <p className="text-muted-foreground text-sm">
          Set filters to narrow down the project list.
        </p>
      </div>

      {/* Code Filter */}
      <div className="space-y-2">
        <Label htmlFor="code">Project Code</Label>
        <Input id="code" {...register("code")} placeholder="Search by project code..." />
      </div>

      {/* Vessel Filter */}
      <div className="space-y-2">
        <Label htmlFor="vessel">Vessel Name</Label>
        <Input id="vessel" {...register("vessel")} placeholder="Search by vessel name..." />
      </div>

      {/* Project Handler Filter */}
      {/* <div className="space-y-2">
        <Label htmlFor="projectHandler">Project Handler</Label>
        <Controller
          name="projectHandler"
          control={control}
          render={({ field }) => (
            <ProjectHandler control={control} placeholder="Select project handler..." {...field} />
          )}
        />
      </div> */}

      {/* Status Filter */}
      <div className="space-y-2">
        <Label>Status</Label>
        {statusOptions.map((option) => (
          <Controller
            key={option.value}
            name="status"
            control={control}
            render={({ field }) => {
              const selected = field.value || [];
              const isChecked = selected.includes(option.value as ProjectStatus);
              return (
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={`status-${option.value}`}
                    checked={isChecked}
                    onCheckedChange={(checked) => {
                      const newValue = checked
                        ? [...selected, option.value]
                        : selected.filter((v) => v !== option.value);
                      field.onChange(newValue);
                    }}
                  />
                  <Label htmlFor={`status-${option.value}`} className="text-sm font-normal">
                    {option.label}
                  </Label>
                </div>
              );
            }}
          />
        ))}
      </div>

      {/* Actions */}
      <div className="flex justify-between space-x-2">
        <Button type="button" variant="outline" size="sm" onClick={handleClearAll}>
          Clear All
        </Button>
        <div className="space-x-2">
          <Button type="button" variant="outline" size="sm" onClick={onCancel}>
            Cancel
          </Button>
          <Button size="sm" type="submit">
            Apply
          </Button>
        </div>
      </div>
    </form>
  );
};
