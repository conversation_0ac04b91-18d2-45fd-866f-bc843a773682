"use client";

import { CustomVirtualList } from "@/components/project-virtual-list";
import { useProjectFilterStore } from "@/stores/project-filter-store";
import { type ProjectResponse, ProjectFilters } from "@/types";
import { Icons } from "@repo/ui/components/icons";
import { getCoreRowModel } from "@tanstack/react-table";
import React from "react";
import { getColumns } from "../config/table-config";
import NoProjectData from "./no-data/no-project-data";

interface ProjectTableProps {
  projectData: ProjectResponse[] | [];
  onEndReached?: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  onRowClick?: (project: ProjectResponse) => void;
  selectedProject?: ProjectResponse; // ✅ new prop for highlighting
  onEditProject?: (project: ProjectResponse) => void; // ✅ new prop for edit action
}

const ProjectTable: React.FC<ProjectTableProps> = ({
  projectData,
  onEndReached,
  hasNextPage,
  isFetchingNextPage,
  onRowClick,
  selectedProject,
  onEditProject,
}) => {
  const filters = useProjectFilterStore((state) => state.filters);
  const columns = getColumns(onEditProject, null, null);

  return (
    <div className="bg-background h-full space-y-4 rounded-xl p-6">
      {/* Table */}
      {projectData.length === 0 ? (
        <NoProjectData filters={filters} />
      ) : (
        <CustomVirtualList
          options={{
            data: projectData,
            columns,
            getCoreRowModel: getCoreRowModel(),
          }}
          onEndReached={onEndReached}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
          onRowClick={onRowClick}
          selectedItem={selectedProject}
        />
      )}
    </div>
  );
};

export default ProjectTable;
