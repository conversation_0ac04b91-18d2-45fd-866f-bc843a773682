import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@repo/ui/components/dialog";
import React from "react";
import AddProjectForm from "../forms/add-project-form";

interface AddProjectDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}
export default function AddProjectDialog({ open, setOpen }: AddProjectDialogProps) {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="max-h-[700px] overflow-y-auto sm:max-w-[900px]"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-center">New Project</DialogTitle>
        </DialogHeader>
        {/* TODO: Add ProjectForm component when forms are created */}
        <AddProjectForm setOpen={setOpen} />
      </DialogContent>
    </Dialog>
  );
}
