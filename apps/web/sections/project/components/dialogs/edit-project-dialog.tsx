import React from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@repo/ui/components/dialog";

import { ProjectResponse } from "@/types";
import EditProjectForm from "../forms/edit-project-form";

interface EditProjectDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedProject: ProjectResponse | undefined;
}

export default function EditProjectDialog({
  open,
  setOpen,
  selectedProject,
}: EditProjectDialogProps) {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="max-h-[700px] overflow-y-auto sm:max-w-[900px]"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-center">Edit Project</DialogTitle>
        </DialogHeader>
        {selectedProject && <EditProjectForm selectedProject={selectedProject} setOpen={setO<PERSON>} />}
      </DialogContent>
    </Dialog>
  );
}
