import { Icons } from "@repo/ui/components/icons";
import React from "react";
import { ProjectFilters } from "@/types";

export default function NoProjectData({ filters }: { filters: ProjectFilters }) {
  return (
    <div className="flex min-h-[500px] flex-col items-center justify-center py-12 text-center">
      <Icons.folder className="mb-4 h-12 w-12 text-gray-400" />
      <h3 className="mb-2 text-lg font-medium text-gray-900">No projects found</h3>
      <p className="max-w-sm text-gray-500">
        {Object.keys(filters).some((key) => filters[key as keyof ProjectFilters])
          ? "No projects match your current filters. Try adjusting your search criteria."
          : "There are no projects to display at the moment."}
      </p>
    </div>
  );
}
