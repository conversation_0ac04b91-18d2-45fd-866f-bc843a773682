"use client";

import React, { useState } from "react";
import { Button } from "@repo/ui/components/button";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Badge } from "@repo/ui/components/badge";
import { Icons } from "@repo/ui/components/icons";
import { ProjectFilterForm } from "../forms/project-filter-form";
import { useProjectFilterStore, useProjectFilterSelectors } from "@/stores/project-filter-store";

export const ProjectFilterPopover: React.FC = () => {
  const filters = useProjectFilterStore((state) => state.filters);
  const setFilters = useProjectFilterStore((state) => state.setFilters);
  const clearAllFilters = useProjectFilterStore((state) => state.clearAllFilters);
  const { hasActiveFilters, activeFilterCount } = useProjectFilterSelectors();

  const [isOpen, setIsOpen] = useState(false);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="relative text-gray-500">
          <Icons.filter className="mr-2 h-4 w-4" />
          Filter
          {hasActiveFilters && (
            <Badge className="ml-2 h-5 w-5 rounded-full p-0 text-xs">{activeFilterCount}</Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="max-h-[500px] w-80 overflow-y-auto p-4" align="end">
        <ProjectFilterForm
          defaultValues={filters}
          onSubmit={(values) => {
            // values.projectHandler = values.projectHandler?.id;
            setFilters(values);
            setIsOpen(false);
          }}
          onClear={() => {
            clearAllFilters();
            setIsOpen(false);
          }}
          onCancel={() => setIsOpen(false)}
        />
      </PopoverContent>
    </Popover>
  );
};
