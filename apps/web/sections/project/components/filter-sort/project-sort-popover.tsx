"use client";

import React, { useState, useCallback } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Badge } from "@repo/ui/components/badge";
import { Icons } from "@repo/ui/components/icons";
import { ProjectSort } from "@/types";
import { useProjectFilterStore } from "@/stores/project-filter-store";

const sortFields = [
  { key: "vessel", label: "Vessel Name" },
  { key: "createdAt", label: "Created Date" },
  { key: "updatedAt", label: "Updated Date" },
];

const ProjectSortPopover: React.FC = () => {
  const sort = useProjectFilterStore((state) => state.sort);
  const pendingSort = useProjectFilterStore((state) => state.pendingSort);
  const setPendingSort = useProjectFilterStore((state) => state.setPendingSort);
  const applyPendingSort = useProjectFilterStore((state) => state.applyPendingSort);
  const setSort = useProjectFilterStore((state) => state.setSort);

  const [isOpen, setIsOpen] = useState(false);

  // Initialize pending sort when popover opens
  const handleOpenChange = useCallback(
    (open: boolean) => {
      if (open) {
        setPendingSort(sort);
      }
      setIsOpen(open);
    },
    [sort, setPendingSort]
  );

  const handleClearSort = useCallback(() => {
    setSort(undefined);
    setIsOpen(false);
  }, [setSort]);

  const handleFieldChange = useCallback(
    (field: string) => {
      setPendingSort({
        field,
        direction: pendingSort?.direction || "asc",
      });
    },
    [pendingSort, setPendingSort]
  );

  const handleDirectionChange = useCallback(
    (direction: "asc" | "desc") => {
      setPendingSort({
        field: pendingSort?.field || "vessel",
        direction,
      });
    },
    [pendingSort, setPendingSort]
  );

  const handleApplySort = useCallback(() => {
    applyPendingSort();
    setIsOpen(false);
  }, [applyPendingSort]);

  const getSortFieldLabel = (field: string) => {
    return sortFields.find((f) => f.key === field)?.label || field;
  };

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="relative text-gray-500">
          Sort
          <Icons.sortAsc className="h-4 w-4" />
          {sort && (
            <Badge
              variant="default"
              className="absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs"
            >
              1
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 overflow-auto p-4" align="end">
        <div className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">Sort Projects</h4>
            <p className="text-muted-foreground text-sm">Choose how to sort the project list.</p>
          </div>

          {/* Current Sort Display */}
          {sort && (
            <div className="bg-muted rounded-md p-3">
              <p className="text-sm font-medium">Current Sort:</p>
              <p className="text-muted-foreground text-sm">
                {getSortFieldLabel(sort.field!)} (
                {sort.direction === "asc" ? "Ascending" : "Descending"})
              </p>
            </div>
          )}

          {/* Sort Field Selection */}
          <div className="space-y-2">
            <h5 className="text-sm font-medium">Sort by Field</h5>
            <div className="grid grid-cols-2 gap-2">
              {sortFields.map((field) => (
                <Button
                  key={field.key}
                  variant={pendingSort?.field === field.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleFieldChange(field.key)}
                  className="justify-start"
                >
                  {field.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Sort Direction Selection */}
          <div className="space-y-2">
            <h5 className="text-sm font-medium">Sort Direction</h5>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant={pendingSort?.direction === "asc" ? "default" : "outline"}
                size="sm"
                onClick={() => handleDirectionChange("asc")}
                className="justify-start"
              >
                <Icons.arrowUpIcon className="mr-2 h-4 w-4" />
                Ascending
              </Button>
              <Button
                variant={pendingSort?.direction === "desc" ? "default" : "outline"}
                size="sm"
                onClick={() => handleDirectionChange("desc")}
                className="justify-start"
              >
                <Icons.arrowDownIcon className="mr-2 h-4 w-4" />
                Descending
              </Button>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-between pt-4">
            <Button variant="outline" size="sm" onClick={handleClearSort}>
              Clear Sort
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button size="sm" onClick={handleApplySort} disabled={!pendingSort}>
                Apply Sort
              </Button>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default ProjectSortPopover;
