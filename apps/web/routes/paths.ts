const ROOTS = {
  AUTH: "/auth",
  DASHBOARD: "/dashboard",
  USERS: "/users",
  GLOSSARY: "/glossary",
  CONFIGURATION: "/configuration",
};

export const paths = {
  // AUTH
  auth: {
    signIn: `${ROOTS.AUTH}`,
    verify_email: `${ROOTS.AUTH}/verify-email`,
    signUp: `${ROOTS.AUTH}/sign-up`,
    updatePassword: `${ROOTS.AUTH}/update-password`,
    resetPassword: `${ROOTS.AUTH}/reset-password`,
  },

  // DASHBOARD
  dashboard: {
    root: `${ROOTS.DASHBOARD}`,
  },

  // User Management
  users: {
    root: `${ROOTS.USERS}`,
    view: (id: string) => `${ROOTS.USERS}/users/${id}`,
  },
  glossary: {
    root: `${ROOTS.GLOSSARY}`,
    view: (id: string, name: string) => `${ROOTS.GLOSSARY}/${id}?name=${encodeURIComponent(name)}`,
  },
  configuration: {
    root: `${ROOTS.CONFIGURATION}`,
    view: (id: string) => `${ROOTS.CONFIGURATION}/${id}`,
  },
};
