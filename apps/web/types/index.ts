export type { TenantResponse, TenantStatus, TenantSubscriptionType } from "@api-types";
export type { TenantListResponse } from "@api-types";
export type { TenantResponse as Tenant } from "@api-types";
export type { UserResponse, UserRole, UserStatus } from "@api-types";
export type { UserResponse as User } from "@api-types";
export type { UserListResponse } from "@api-types";
export type { UserProfileResponse } from "@api-types";
export type { Action, Resource } from "@api-types";
export type { BaseResponse, RoleDefinition, ResourcePermissions } from "@api-types";
export type { UserSignupRequest } from "@api-types";
export type { TenantRegisterRequest } from "@api-types";
export type { TenantUpdateRequest } from "@api-types";
export type { UserUpdateRequest } from "@api-types";
export type { UserListRequest } from "@api-types";
export type { TenantListRequest } from "@api-types";
export type { ForgotPasswordRequest } from "@api-types";
export type {
  GlossaryListRequest,
  GlossaryListResponse,
  GlossaryResponse,
  GlossaryCreateRequest,
  GlossaryUpdateRequest,
  GlossarySort,
  GlossaryFilters,
} from "@api-types";

export type { GlossaryType, GlossaryStatus } from "@api-types";

export type {
  GlossaryItemResponse,
  GlossaryItemListRequest,
  GlossaryItemListResponse,
  GlossaryItemCreateRequest,
  GlossaryItemUpdateRequest,
} from "@api-types";

export type { GlossaryItemFilters, GlossaryItemSort } from "@api-types";

export type {
  ProjectListRequest,
  ProjectListResponse,
  ProjectResponse,
  ProjectGetResponse,
  ProjectCategory,
  ProjectUpdateRequest,
  ProjectCreateRequest,
  ProjectFilters,
  ProjectSort,
  ProjectStatus,
  ProjectUser,
  ProjectFileListRequest,
  ProjectFileListResponse,
  ProjectFileFilter,
  ProjectFileSort,
  ProjectFileResponse,
  ProjectFileStatus,
} from "@api-types";
export type { Project } from "@api-types";

export type { UserFilters, UserSort } from "@api-types";
export type { TenantFilters, TenantSort } from "@api-types";

export const userStatus = {
  PendingVerification: "Pending Verification",
  Active: "Active",
  Inactive: "Inactive",
};

export const userRole = {
  TenantAdmin: "Tenant Admin",
  ProjectHandler: "Project Handler",
  SuperAdmin: "Super Admin",
};

export const glossaryType = {
  Global: "Global",
  Self: "Self",
};

export const glossaryStatus = {
  Active: "Active",
  Inactive: "Inactive",
};
