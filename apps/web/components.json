{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": true, "tsx": true, "tailwind": {"config": "", "css": "../../packages/ui/src/styles/globals.css", "baseColor": "neutral", "cssVariables": true}, "iconLibrary": "lucide", "aliases": {"components": "@/components", "utils": "@repo/ui/lib/utils", "ui": "@repo/ui/components", "lib": "@/lib", "hooks": "@/hooks"}, "registries": {"@wds": "https://wds-shadcn-registry.netlify.app/r/{name}.json"}}