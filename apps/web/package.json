{"name": "web", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "next dev --port 3000 --turbopack", "build": "next build && cp -r .next/standalone/apps/web/. .next/standalone && rm -rf .next/standalone/apps && cp -r .next/static .next/standalone/.next/ && cp -r public .next/standalone/", "start:standalone": "node .next/standalone/server.js", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@repo/ui": "*", "@tanstack/react-query": "^5.89.0", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.12.2", "clsx": "^2.1.1", "d3": "^7.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.18", "fflate": "^0.8.2", "fs": "0.0.1-security", "handlebars": "^4.7.8", "jotai": "^2.14.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "lucide-react": "^0.475.0", "next": "^15.4.2", "next-themes": "^0.4.6", "p-limit": "^7.1.1", "pdfjs": "^2.5.4", "pdfjs-dist": "^5.4.296", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "sonner": "^2.0.7", "supertokens-auth-react": "^0.50.0", "tailwind-merge": "^3.3.1", "usehooks-ts": "^3.1.1", "zod": "^3.25.76", "zustand": "^5.0.8"}, "devDependencies": {"@next/eslint-plugin-next": "^15.4.6", "@repo/eslint-config": "*", "@repo/typescript-config": "*", "@tailwindcss/postcss": "^4.1.5", "@types/d3": "^7.4.3", "@types/node": "^22.15.30", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@types/react-dropzone": "^5.1.0", "autoprefixer": "^10.4.20", "critters": "^0.0.25", "eslint": "^9.33.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "5.9.2"}}