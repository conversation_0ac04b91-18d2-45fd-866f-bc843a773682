/**
 * Format file size in bytes to human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Get file extension from filename (including the dot)
 */
export function getFileExtension(fileName: string): string {
  const lastDotIndex = fileName.lastIndexOf(".");
  return lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : "";
}

/**
 * Get filename without extension
 */
export function getFileNameWithoutExtension(fileName: string): string {
  const lastDotIndex = fileName.lastIndexOf(".");
  return lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;
}

/**
 * Get filename from a path
 */
export function getFileName(path: string): string {
  return path.split("/").pop() || path.split("\\").pop() || path;
}

/**
 * Validate file type by extension
 */
export function isValidFileType(fileName: string, allowedExtensions: string[]): boolean {
  const extension = getFileExtension(fileName).toLowerCase();
  return allowedExtensions.includes(extension);
}

/**
 * Generate a unique file ID
 */
export function generateFileId(): string {
  return Math.random().toString(36).substring(2, 11) + Date.now().toString(36);
}

/**
 * Check if a file is a system/OS-dependent file that should be filtered out
 */
export function isSystemFile(fileName: string, filePath?: string): boolean {
  const normalizedPath = filePath?.replace(/\\/g, "/") || "";
  const name = fileName.toLowerCase();

  // macOS system files
  if (fileName.startsWith("._")) return true;
  if (name === ".ds_store") return true;
  if (normalizedPath.includes("__macosx/")) return true;
  if (fileName === "Icon\r") return true; // Custom folder icons

  // Windows system files
  if (name === "thumbs.db") return true;
  if (name === "desktop.ini") return true;

  // General hidden files (starting with dot)
  if (fileName.startsWith(".") && fileName !== ".pdf") return true;

  return false;
}
