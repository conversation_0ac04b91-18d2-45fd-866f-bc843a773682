/**
 * Utility function to extract only dirty (changed) fields from form data
 * @param data - The complete form data
 * @param dirtyFields - The dirty fields object from React Hook Form
 * @param additionalFields - Additional fields to always include (like id)
 * @returns Object containing only the dirty fields plus any additional fields
 */
export function getDirtyFields<T extends Record<string, unknown>>(
  data: T,
  dirtyFields: Partial<Record<keyof T, boolean>>,
  additionalFields: Partial<T> = {}
): Partial<T> {
  const result: Partial<T> = { ...additionalFields };

  // Add only the fields that have been changed
  Object.keys(dirtyFields).forEach((key) => {
    const fieldKey = key as keyof T;
    if (dirtyFields[fieldKey]) {
      result[fieldKey] = data[fieldKey];
    }
  });

  return result;
}
