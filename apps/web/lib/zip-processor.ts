import { unzipSync } from "fflate";

export interface ProcessedFile {
  name: string;
  path: string;
  file: File;
  size: number;
}

export interface ZipProcessorOptions {
  maxDepth?: number;
  maxFileSize?: number;
  allowedExtensions?: string[];
}

export class ZipProcessor {
  private readonly maxDepth: number;
  private readonly maxFileSize: number;
  private readonly allowedExtensions: string[];

  constructor(options: ZipProcessorOptions = {}) {
    this.maxDepth = options.maxDepth ?? 10; // Prevent infinite recursion
    this.maxFileSize = options.maxFileSize ?? 1024 * 1024 * 1024; // 1GB default
    this.allowedExtensions = options.allowedExtensions ?? [".pdf"];
  }

  /**
   * Process a ZIP file recursively and extract all PDF files with their full paths
   */
  async processZipFile(zipFile: File, basePath: string = ""): Promise<ProcessedFile[]> {
    try {
      console.log(`Processing ZIP file: ${zipFile.name} (${zipFile.size} bytes)`);

      // Check if file is too large for processing
      if (zipFile.size > this.maxFileSize) {
        throw new Error(`ZIP file ${zipFile.name} is too large (${zipFile.size} bytes)`);
      }

      const arrayBuffer = await zipFile.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      // Get the base name without extension for the path
      const zipBaseName = this.getFileNameWithoutExtension(zipFile.name);
      const currentPath = basePath ? `${basePath}/${zipBaseName}` : zipBaseName;

      console.log(`Starting extraction for ZIP: ${zipFile.name} at path: ${currentPath}`);
      const result = await this.extractFromZip(uint8Array, currentPath, 0);
      console.log(`Completed extraction for ZIP: ${zipFile.name}, found ${result.length} files`);

      return result;
    } catch (error) {
      console.error(`Error processing ZIP file ${zipFile.name}:`, error);
      throw new Error(
        `Failed to process ZIP file: ${zipFile.name} - ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Recursively extract files from ZIP data
   */
  private async extractFromZip(
    zipData: Uint8Array,
    currentPath: string,
    depth: number
  ): Promise<ProcessedFile[]> {
    if (depth >= this.maxDepth) {
      console.warn(`Maximum recursion depth (${this.maxDepth}) reached for path: ${currentPath}`);
      return [];
    }

    console.log(`Extracting ZIP data at depth ${depth}, path: ${currentPath}`);
    const processedFiles: ProcessedFile[] = [];

    try {
      // Add size check before attempting to unzip
      if (zipData.length > this.maxFileSize) {
        throw new Error(`ZIP data too large: ${zipData.length} bytes`);
      }

      console.log(`Attempting to unzip ${zipData.length} bytes of data...`);
      const unzipped = unzipSync(zipData);
      console.log(`Successfully unzipped, found ${Object.keys(unzipped).length} entries`);

      let processedCount = 0;
      const totalEntries = Object.keys(unzipped).length;

      for (const [entryPath, fileData] of Object.entries(unzipped)) {
        processedCount++;
        console.log(`Processing entry ${processedCount}/${totalEntries}: ${entryPath}`);

        // Skip directories (they end with /)
        if (entryPath.endsWith("/")) {
          continue;
        }

        // Check file size
        if (fileData.length > this.maxFileSize) {
          console.warn(`File ${entryPath} exceeds size limit (${fileData.length} bytes), skipping`);
          continue;
        }

        // Filter out OS-dependent files
        if (this.shouldFilterFile(entryPath)) {
          console.log(`Filtering out system file: ${entryPath}`);
          continue;
        }

        const fileName = this.getFileName(entryPath);
        const fileExtension = this.getFileExtension(fileName).toLowerCase();
        const fullPath = this.buildFullPath(currentPath, entryPath);

        // If it's a ZIP file, process it recursively
        if (fileExtension === ".zip") {
          try {
            console.log(`Found nested ZIP: ${entryPath}, processing recursively...`);
            const nestedFiles = await this.extractFromZip(fileData, fullPath, depth + 1);
            processedFiles.push(...nestedFiles);
            console.log(`Nested ZIP ${entryPath} processed, found ${nestedFiles.length} files`);
          } catch (error) {
            console.error(`Error processing nested ZIP ${entryPath}:`, error);
            // Continue processing other files even if one nested ZIP fails
          }
        }
        // If it's an allowed file type (e.g., PDF), add it to results
        else if (this.allowedExtensions.includes(fileExtension)) {
          try {
            console.log(`Found ${fileExtension} file: ${fileName} at ${fullPath}`);
            const file = new File([new Uint8Array(fileData)], fileName, {
              type: this.getMimeType(fileExtension),
              lastModified: Date.now(),
            });

            processedFiles.push({
              name: fileName,
              path: fullPath,
              file: file,
              size: fileData.length,
            });
          } catch (error) {
            console.error(`Error creating file object for ${entryPath}:`, error);
          }
        } else {
          console.log(`Skipping unsupported file type: ${fileName} (${fileExtension})`);
        }
      }

      console.log(
        `Completed processing ${totalEntries} entries, found ${processedFiles.length} valid files`
      );
    } catch (error) {
      console.error(`Error extracting ZIP data for path ${currentPath}:`, error);

      // Provide more specific error information
      if (error instanceof Error) {
        if (error.message.includes("invalid signature")) {
          throw new Error(`Invalid ZIP file format at ${currentPath}`);
        } else if (error.message.includes("unexpected end")) {
          throw new Error(`Corrupted ZIP file at ${currentPath}`);
        }
      }

      throw error;
    }

    return processedFiles;
  }

  /**
   * Check if a file should be filtered out (OS-dependent files)
   */
  private shouldFilterFile(entryPath: string): boolean {
    const normalizedPath = entryPath.replace(/\\/g, "/");
    const fileName = this.getFileName(normalizedPath);

    // Filter out macOS system files
    if (fileName.startsWith("._")) return true;
    if (fileName === ".DS_Store") return true;
    if (normalizedPath.includes("__MACOSX/")) return true;

    // Filter out Windows system files
    if (fileName === "Thumbs.db") return true;
    if (fileName === "desktop.ini") return true;
    if (fileName.toLowerCase() === "thumbs.db") return true;

    // Filter out other common system files
    if (fileName.startsWith(".")) return true; // Hidden files
    if (fileName === "Icon\r") return true; // macOS custom folder icons

    return false;
  }

  /**
   * Build the full path including folder structure
   */
  private buildFullPath(basePath: string, entryPath: string): string {
    // Remove leading slashes and normalize path separators
    const normalizedEntry = entryPath.replace(/^\/+/, "").replace(/\\/g, "/");

    // If there are folders in the entry path, include them
    const pathParts = normalizedEntry.split("/");

    if (pathParts.length > 1) {
      // Remove the filename to get just the folder path
      const folderPath = pathParts.slice(0, -1).join("/");
      return `${basePath}/${folderPath}`;
    }

    return basePath;
  }

  /**
   * Get filename from a path
   */
  private getFileName(path: string): string {
    return path.split("/").pop() || path.split("\\").pop() || path;
  }

  /**
   * Get file extension including the dot
   */
  private getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf(".");
    return lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : "";
  }

  /**
   * Get filename without extension
   */
  private getFileNameWithoutExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf(".");
    return lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;
  }

  /**
   * Get MIME type based on file extension
   */
  private getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      ".pdf": "application/pdf",
      ".zip": "application/zip",
    };

    return mimeTypes[extension.toLowerCase()] || "application/octet-stream";
  }

  /**
   * Validate if a file is a supported ZIP file
   */
  static isZipFile(file: File): boolean {
    return (
      file.name.toLowerCase().endsWith(".zip") ||
      file.type === "application/zip" ||
      file.type === "application/x-zip-compressed"
    );
  }

  /**
   * Validate if a file is a supported PDF file
   */
  static isPdfFile(file: File): boolean {
    return file.name.toLowerCase().endsWith(".pdf") || file.type === "application/pdf";
  }
}

/**
 * Convenience function to process a single ZIP file
 */
export async function processZipFile(
  zipFile: File,
  options?: ZipProcessorOptions
): Promise<ProcessedFile[]> {
  const processor = new ZipProcessor(options);
  return processor.processZipFile(zipFile);
}

/**
 * Convenience function to process multiple files (ZIP and PDF)
 */
export async function processFiles(
  files: File[],
  options?: ZipProcessorOptions
): Promise<ProcessedFile[]> {
  const processor = new ZipProcessor(options);
  const allProcessedFiles: ProcessedFile[] = [];

  for (const file of files) {
    try {
      if (ZipProcessor.isZipFile(file)) {
        const zipFiles = await processor.processZipFile(file);
        allProcessedFiles.push(...zipFiles);
      } else if (ZipProcessor.isPdfFile(file)) {
        // For direct PDF files, use the filename as the path
        allProcessedFiles.push({
          name: file.name,
          path: processor["getFileNameWithoutExtension"](file.name),
          file: file,
          size: file.size,
        });
      }
    } catch (error) {
      console.error(`Error processing file ${file.name}:`, error);
      // Continue processing other files even if one fails
    }
  }

  return allProcessedFiles;
}
