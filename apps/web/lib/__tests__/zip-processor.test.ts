import { ZipProcessor, processFiles } from '../zip-processor';

// Mock fflate
jest.mock('fflate', () => ({
  unzipSync: jest.fn(),
}));

describe('ZipProcessor', () => {
  let processor: ZipProcessor;

  beforeEach(() => {
    processor = new ZipProcessor({
      maxDepth: 3,
      maxFileSize: 1024 * 1024, // 1MB
      allowedExtensions: ['.pdf'],
    });
  });

  describe('static methods', () => {
    it('should identify ZIP files correctly', () => {
      const zipFile = new File([''], 'test.zip', { type: 'application/zip' });
      const pdfFile = new File([''], 'test.pdf', { type: 'application/pdf' });
      const txtFile = new File([''], 'test.txt', { type: 'text/plain' });

      expect(ZipProcessor.isZipFile(zipFile)).toBe(true);
      expect(ZipProcessor.isZipFile(pdfFile)).toBe(false);
      expect(ZipProcessor.isZipFile(txtFile)).toBe(false);
    });

    it('should identify PDF files correctly', () => {
      const zipFile = new File([''], 'test.zip', { type: 'application/zip' });
      const pdfFile = new File([''], 'test.pdf', { type: 'application/pdf' });
      const txtFile = new File([''], 'test.txt', { type: 'text/plain' });

      expect(ZipProcessor.isPdfFile(zipFile)).toBe(false);
      expect(ZipProcessor.isPdfFile(pdfFile)).toBe(true);
      expect(ZipProcessor.isPdfFile(txtFile)).toBe(false);
    });
  });

  describe('path building', () => {
    it('should build correct paths for nested structures', () => {
      // This would test the buildFullPath method if it were public
      // For now, we'll test the overall functionality through integration tests
    });
  });

  describe('file processing', () => {
    it('should handle direct PDF files', async () => {
      const pdfFile = new File(['pdf content'], 'document.pdf', { type: 'application/pdf' });
      
      const result = await processFiles([pdfFile]);
      
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('document.pdf');
      expect(result[0].path).toBe('document');
    });

    it('should handle mixed file types', async () => {
      const pdfFile = new File(['pdf content'], 'document.pdf', { type: 'application/pdf' });
      const txtFile = new File(['text content'], 'readme.txt', { type: 'text/plain' });
      
      const result = await processFiles([pdfFile, txtFile]);
      
      // Should only return PDF files
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('document.pdf');
    });
  });

  describe('error handling', () => {
    it('should handle file size limits', () => {
      const processor = new ZipProcessor({ maxFileSize: 100 }); // 100 bytes
      expect(processor).toBeDefined();
    });

    it('should handle recursion depth limits', () => {
      const processor = new ZipProcessor({ maxDepth: 1 });
      expect(processor).toBeDefined();
    });
  });
});

describe('processFiles convenience function', () => {
  it('should process multiple files', async () => {
    const pdfFile1 = new File(['pdf1'], 'doc1.pdf', { type: 'application/pdf' });
    const pdfFile2 = new File(['pdf2'], 'doc2.pdf', { type: 'application/pdf' });
    
    const result = await processFiles([pdfFile1, pdfFile2]);
    
    expect(result).toHaveLength(2);
    expect(result[0].name).toBe('doc1.pdf');
    expect(result[1].name).toBe('doc2.pdf');
  });

  it('should use default options when none provided', async () => {
    const pdfFile = new File(['pdf content'], 'test.pdf', { type: 'application/pdf' });
    
    const result = await processFiles([pdfFile]);
    
    expect(result).toHaveLength(1);
    expect(result[0].name).toBe('test.pdf');
  });
});
