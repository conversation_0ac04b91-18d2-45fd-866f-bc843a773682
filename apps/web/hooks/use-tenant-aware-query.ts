"use client";

import { useTenant } from "@/providers/tenant-selector";
import { QueryKey, QueryClient } from "@tanstack/react-query";

/**
 * Simple utility to create tenant-aware query keys.
 * This is all we really need - the axios interceptor handles the HTTP headers.
 */
export function useTenantQueryKey(baseKey: QueryKey): readonly unknown[] {
  const { currentTenantId } = useTenant();
  return currentTenantId ? [currentTenantId, ...baseKey] : baseKey;
}

/**
 * Check if we have a current tenant (useful for enabling/disabling queries)
 */
export function useHasTenant(): boolean {
  const { currentTenantId } = useTenant();
  return !!currentTenantId;
}

/**
 * Hook to invalidate all queries for the current tenant.
 */
export function useInvalidateTenantQueries(): (queryClient: QueryClient) => void {
  const { currentTenantId } = useTenant();

  return (queryClient: QueryClient) => {
    queryClient.invalidateQueries({
      predicate: (query) => {
        return query.queryKey?.[0] === currentTenantId;
      },
    });
  };
}

/**
 * Hook to create an invalidation function for specific resource types.
 * This is useful for mutations that should invalidate all queries for a specific resource.
 */
export function useInvalidateResourceQueries(
  resourceType: string
): (queryClient: QueryClient) => void {
  const { currentTenantId } = useTenant();

  return (queryClient: QueryClient) => {
    queryClient.invalidateQueries({
      predicate: (query) => {
        const queryKey = query.queryKey;
        return queryKey && queryKey[0] === currentTenantId && queryKey[1] === resourceType;
      },
    });
  };
}
