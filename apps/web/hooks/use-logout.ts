"use client";

import { useQueryClient } from "@tanstack/react-query";
import Session from "supertokens-auth-react/recipe/session";
import { clearAllStores } from "@/stores/store-manager";

export function useLogout() {
  const queryClient = useQueryClient();

  const logout = async () => {
    // 1. Clear Supertokens session (cookies)
    await Session.signOut();

    // 2. Reset React Query cache
    queryClient.clear();

    // 3. Clear all Zustand stores
    clearAllStores();

    // 4. Remove local storage
    localStorage.removeItem("currentTenantData");
    localStorage.removeItem("currentTenantId");

    // 5. Redirect
    window.location.reload();
    window.location.href = "/login";
  };

  return logout;
}
