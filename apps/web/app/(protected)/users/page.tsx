import React from "react";
import { UserPage } from "@/sections/users/index";
import { CanAccess } from "@/providers/access-control";
import type { Action, Resource } from "@/types";

const Page: React.FC = () => {
  return (
    <CanAccess
      privilege={"read" as Action.Read}
      resource={"user" as Resource.User}
      showForbiddenPage={true}
    >
      <UserPage />
    </CanAccess>
  );
};

export default Page;
