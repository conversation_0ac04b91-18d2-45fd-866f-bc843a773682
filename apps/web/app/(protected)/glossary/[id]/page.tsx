import React from "react";
import GlossaryItemPage from "@/sections/glossary-item";
import { CanAccess } from "@/providers/access-control";
import type { Action, Resource } from "@/types";

const Page: React.FC = () => {
  return (
    <CanAccess
      privilege={"read" as Action.Read}
      resource={"glossary-item" as Resource.GlossaryItem}
      showForbiddenPage={true}
    >
      <GlossaryItemPage />
    </CanAccess>
  );
};

export default Page;
