import React from "react";
import GlossaryPage from "@/sections/glossary";
import { CanAccess } from "@/providers/access-control";
import type { Action, Resource } from "@/types";

const Page: React.FC = () => {
  return (
    <CanAccess
      privilege={"read" as Action.Read}
      resource={"glossary" as Resource.Glossary}
      showForbiddenPage={true}
    >
      <GlossaryPage />
    </CanAccess>
  );
};

export default Page;
