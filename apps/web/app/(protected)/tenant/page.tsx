import React from "react";
import { TenantPage } from "@/sections/tenant";
import { CanAccess } from "@/providers/access-control";
import type { Action, Resource } from "@/types";

const Page: React.FC = () => {
  return (
    <CanAccess
      privilege={"read" as Action.Read}
      resource={"tenant" as Resource.Tenant}
      showForbiddenPage={true}
    >
      <TenantPage />
    </CanAccess>
  );
};

export default Page;
