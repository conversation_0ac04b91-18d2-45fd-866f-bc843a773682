import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { Navbar } from "@/components/nav-bar";

import { cn } from "@repo/ui/lib/utils";
import { MetaDataProvider } from "@/providers/meta-data";
import { AccessProvider } from "@/providers/access-control";
import { TenantProvider } from "@/providers/tenant-selector";
import { PerformanceMonitor } from "@/components/performance-test";
import { ProtectedPage } from "@/sections/auth/protected-page";
import { ErrorBoundaryWrapper } from "@/components/error-boundary-wrapper";
import { Suspense } from "react";
import { AppLoading } from "@/components/loading";
import { Impersonator } from "@/providers/tenant-selector";
import GlossaryProvider from "@/providers/glossary-provider/glossary-provider";

const geist = Geist({ subsets: ["latin"] });

export default function ProtectedLayout({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundaryWrapper name="ProtectedPage">
      <Suspense fallback={<AppLoading message="Verifying authentication..." />}>
        <ProtectedPage>
          <ErrorBoundaryWrapper name="MetaDataProvider">
            <Suspense fallback={<AppLoading message="Loading metadata..." />}>
              <MetaDataProvider>
                <ErrorBoundaryWrapper name="AccessProvider">
                  <AccessProvider>
                    <ErrorBoundaryWrapper name="TenantProvider">
                      <TenantProvider>
                        <ErrorBoundaryWrapper name="GlossaryProvider">
                          <GlossaryProvider>
                            <Impersonator />
                            <div className={cn(geist.className, "flex h-full flex-col")}>
                              <ErrorBoundaryWrapper name="Navbar">
                                <Navbar />
                              </ErrorBoundaryWrapper>
                              <main className="bg-secondary flex-1 p-8">
                                <ErrorBoundaryWrapper name="Page Content">
                                  {children}
                                </ErrorBoundaryWrapper>
                              </main>
                            </div>
                            <PerformanceMonitor />
                          </GlossaryProvider>
                        </ErrorBoundaryWrapper>
                      </TenantProvider>
                    </ErrorBoundaryWrapper>
                  </AccessProvider>
                </ErrorBoundaryWrapper>
              </MetaDataProvider>
            </Suspense>
          </ErrorBoundaryWrapper>
        </ProtectedPage>
      </Suspense>
    </ErrorBoundaryWrapper>
  );
}
