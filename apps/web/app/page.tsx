import { getSSRSessionHelper } from "@/sections/auth/lib/session-helper";
import { redirect } from "next/navigation";

export default async function HomePage() {
  // // Server-side redirect - no loading state needed
  // const { accessTokenPayload } = await getSSRSessionHelper();
  // if (accessTokenPayload) {
  //   redirect("/dashboard");
  // }
  // redirect("/login");
  redirect("/dashboard");
}
