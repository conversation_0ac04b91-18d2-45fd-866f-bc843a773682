import "@repo/ui/globals.css";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import type { Metadata } from "next";
import { Providers as NextThemeProvider } from "@/providers/theme-provider";

import { Toaster } from "sonner";
import { SuperTokensProvider } from "@/sections/auth/super-token-provider";
import { QueryProvider } from "@/providers/query-provider";
import { ErrorBoundaryWrapper } from "@/components/error-boundary-wrapper";
import { Suspense } from "react";
import { AppLoading } from "@/components/loading";

const fontSans = Geist({
  subsets: ["latin"],
  variable: "--font-sans",
});

const fontMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
});

export const metadata: Metadata = {
  title: "Cadetlabs",
  description: "Generated by Pingcubes",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className="h-full">
      <body className={`${fontSans.variable} ${fontMono.variable} h-full font-sans antialiased`}>
        <SuperTokensProvider>
          <NextThemeProvider>
            <ErrorBoundaryWrapper name="QueryProvider">
              <QueryProvider>{children}</QueryProvider>
            </ErrorBoundaryWrapper>
          </NextThemeProvider>
          <Toaster position="top-center" />
        </SuperTokensProvider>
      </body>
    </html>
  );
}
