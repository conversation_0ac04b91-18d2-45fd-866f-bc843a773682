import React from "react";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import logo from "../public/logo.png";
import GoBackButton from "@/components/not-found/GoBackButton";

export default function NotFound() {
  return (
    <div className="from-background via-background to-primary/5 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
      <div className="w-full max-w-2xl">
        <Card className="border-0 bg-white/80 text-center shadow-lg backdrop-blur-sm">
          <CardHeader className="pb-6">
            {/* Logo */}
            <div className="mb-6 flex justify-center">
              <div className="bg-primary/10 rounded-2xl p-4">
                <Image src={logo} alt="Cadetlabs" className="h-12 w-14 object-contain" />
              </div>
            </div>

            {/* 404 Illustration */}
            <div className="relative mb-6">
              <div className="flex items-center justify-center space-x-4">
                {/* Large 404 Text with decorative elements */}
                <div className="relative">
                  <div className="text-primary/20 select-none text-8xl font-bold">4</div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Icons.fileQuestion className="text-primary h-12 w-12" />
                  </div>
                </div>
                <div className="text-primary/20 select-none text-8xl font-bold">0</div>
                <div className="relative">
                  <div className="text-primary/20 select-none text-8xl font-bold">4</div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Icons.search className="text-primary h-12 w-12" />
                  </div>
                </div>
              </div>

              {/* Floating decorative elements */}
              <div className="bg-primary/10 absolute -left-4 -top-4 h-8 w-8 animate-pulse rounded-full"></div>
              <div className="bg-primary/20 absolute -right-6 -top-2 h-6 w-6 animate-pulse rounded-full delay-300"></div>
              <div className="bg-primary/15 absolute -bottom-2 left-8 h-4 w-4 animate-pulse rounded-full delay-700"></div>
            </div>

            {/* Error Message */}
            <div className="space-y-2">
              <h1 className="text-foreground text-3xl font-bold">Page Not Found</h1>
              <p className="text-muted-foreground mx-auto max-w-md text-lg">
                Oops! The page you're looking for seems to have sailed away. Let's get you back on
                course.
              </p>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Helpful suggestions */}
            <div className="bg-muted/50 rounded-lg p-4 text-left">
              <h3 className="text-foreground mb-3 flex items-center gap-2 font-semibold">
                <Icons.AlertTriangle className="text-primary h-4 w-4" />
                What you can do:
              </h3>
              <ul className="text-muted-foreground space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <div className="bg-primary h-1.5 w-1.5 rounded-full"></div>
                  Check the URL for any typos
                </li>
                <li className="flex items-center gap-2">
                  <div className="bg-primary h-1.5 w-1.5 rounded-full"></div>
                  Go back to the previous page
                </li>
                <li className="flex items-center gap-2">
                  <div className="bg-primary h-1.5 w-1.5 rounded-full"></div>
                  Visit our homepage to start fresh
                </li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col justify-center gap-3 sm:flex-row">
              <GoBackButton /> {/* optional client component */}
              <Link href="/dashboard">
                <Button className="flex w-full items-center gap-2 sm:w-auto">
                  <Icons.home className="h-4 w-4" />
                  Back to Dashboard
                </Button>
              </Link>
            </div>

            {/* Contact Support */}
            <div className="border-border/50 border-t pt-4">
              <p className="text-muted-foreground text-sm">
                Still having trouble?{" "}
                <Link href="/help" className="text-primary hover:text-primary/80 font-medium">
                  Contact Support
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
