"use client";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type { UserFilters, UserSort } from "@/types";

interface UserFilterState {
  filters: UserFilters;
  sort?: UserSort;
  pendingSort?: UserSort;

  // Actions
  setFilters: (filters: UserFilters) => void;
  updateFilters: (partialFilters: Partial<UserFilters>) => void;
  setSort: (sort?: UserSort) => void;
  setPendingSort: (sort?: UserSort) => void;
  applyPendingSort: () => void;
  setSearch: (searchValue: string) => void;
  removeFilter: (filterKey: keyof UserFilters, value?: string) => void;
  clearAllFilters: () => void;
  clearSort: () => void;
  reset: () => void;
}

const initialState = {
  filters: {} as UserFilters,
  sort: undefined as UserSort | undefined,
  pendingSort: undefined as UserSort | undefined,
};

export const useUserFilterStore = create<UserFilterState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      setFilters: (filters) => set({ filters }, false, "setFilters"),

      updateFilters: (partialFilters) =>
        set(
          (state) => ({
            filters: { ...state.filters, ...partialFilters },
          }),
          false,
          "updateFilters"
        ),

      setSort: (sort) => set({ sort }, false, "setSort"),

      setPendingSort: (pendingSort) => set({ pendingSort }, false, "setPendingSort"),

      applyPendingSort: () =>
        set(
          (state) => ({
            sort: state.pendingSort,
            pendingSort: undefined,
          }),
          false,
          "applyPendingSort"
        ),

      setSearch: (searchValue) =>
        set(
          (state) => ({
            filters: {
              ...state.filters,
              name: searchValue || undefined,
            },
          }),
          false,
          "setSearch"
        ),

      removeFilter: (filterKey, value) =>
        set(
          (state) => {
            const newFilters = { ...state.filters };
            const currentValue = newFilters[filterKey];

            if (Array.isArray(currentValue) && value) {
              // Remove specific value from array
              const newArray = currentValue.filter((item) => item !== value);
              if (newArray.length === 0) {
                delete newFilters[filterKey];
              } else {
                (newFilters as any)[filterKey] = newArray;
              }
            } else {
              // Remove entire filter
              delete newFilters[filterKey];
            }

            return { filters: newFilters };
          },
          false,
          "removeFilter"
        ),

      clearAllFilters: () => set({ filters: {} }, false, "clearAllFilters"),

      clearSort: () => set({ sort: undefined }, false, "clearSort"),

      reset: () => set(initialState, false, "reset"),
    }),
    {
      name: "user-filter-store",
    }
  )
);

// Computed selectors
export const useUserFilterSelectors = () => {
  const { filters, sort } = useUserFilterStore();

  return {
    hasActiveFilters: Object.values(filters).some((value) => {
      if (Array.isArray(value)) return value.length > 0;
      return value && value.toString().trim() !== "";
    }),
    activeFilterCount: Object.values(filters).filter((value) => {
      if (Array.isArray(value)) return value.length > 0;
      return value && value.toString().trim() !== "";
    }).length,
    hasActiveSort: sort !== undefined,
    searchValue: filters.name || "",
  };
};
