import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type { ProjectFileFilter, ProjectFileSort } from "@/types";

interface FileFilterState {
  // State
  filters: ProjectFileFilter;
  sort?: ProjectFileSort;
  pendingSort?: ProjectFileSort;

  // Actions
  setFilters: (filters: ProjectFileFilter) => void;
  updateFilters: (partialFilters: Partial<ProjectFileFilter>) => void;
  setSort: (sort?: ProjectFileSort) => void;
  setPendingSort: (sort?: ProjectFileSort) => void;
  applyPendingSort: () => void;
  setSearch: (searchValue: string) => void;
  removeFilter: (filterKey: keyof ProjectFileFilter, value?: string) => void;
  clearAllFilters: () => void;
  clearSort: () => void;
  reset: () => void;
}

const initialState = {
  filters: {} as ProjectFileFilter,
  sort: undefined as ProjectFileSort | undefined,
  pendingSort: undefined as ProjectFileSort | undefined,
};

export const useFileFilterStore = create<FileFilterState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Set filters
      setFilters: (filters: ProjectFileFilter) => set({ filters }, false, "setFilters"),

      // Update filters partially
      updateFilters: (partialFilters: Partial<ProjectFileFilter>) =>
        set(
          (state) => ({
            filters: { ...state.filters, ...partialFilters },
          }),
          false,
          "updateFilters"
        ),

      // Set sort
      setSort: (sort?: ProjectFileSort) => set({ sort }, false, "setSort"),

      // Set pending sort (for apply button pattern)
      setPendingSort: (pendingSort?: ProjectFileSort) =>
        set({ pendingSort }, false, "setPendingSort"),

      // Apply pending sort
      applyPendingSort: () => {
        const { pendingSort } = get();
        set({ sort: pendingSort, pendingSort: undefined }, false, "applyPendingSort");
      },

      // Set search value
      setSearch: (searchValue: string) =>
        set(
          (state) => ({
            filters: { ...state.filters, name: searchValue || undefined },
          }),
          false,
          "setSearch"
        ),

      // Remove specific filter
      removeFilter: (filterKey: keyof ProjectFileFilter, value?: string) =>
        set(
          (state) => {
            const newFilters = { ...state.filters };
            const currentValue = newFilters[filterKey];

            if (Array.isArray(currentValue) && value) {
              // Remove specific value from array
              const newArray = currentValue.filter((item) => item !== value);
              if (newArray.length === 0) {
                delete newFilters[filterKey];
              } else {
                (newFilters as any)[filterKey] = newArray;
              }
            } else {
              // Remove entire filter
              delete newFilters[filterKey];
            }

            return { filters: newFilters };
          },
          false,
          "removeFilter"
        ),

      // Clear all filters
      clearAllFilters: () => set({ filters: {} }, false, "clearAllFilters"),

      // Clear sort
      clearSort: () => set({ sort: undefined }, false, "clearSort"),

      // Reset to initial state
      reset: () => set(initialState, false, "reset"),
    }),
    {
      name: "file-filter-store",
    }
  )
);

// Selectors for computed values
export const useFileFilterSelectors = () => {
  const { filters, sort } = useFileFilterStore();

  return {
    hasActiveFilters: Object.values(filters).some((value) => {
      if (Array.isArray(value)) return value.length > 0;
      return value !== undefined && value !== "";
    }),

    activeFilterCount: Object.values(filters).reduce((count, value) => {
      if (Array.isArray(value)) return count + value.length;
      return value !== undefined && value !== "" ? count + 1 : count;
    }, 0),

    hasActiveSort: sort !== undefined,

    searchValue: filters.name || "",
  };
};
