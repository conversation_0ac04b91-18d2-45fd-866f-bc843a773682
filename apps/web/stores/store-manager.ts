"use client";

import { useFileFilterStore } from "./file-filter-store";
import { useTenantFilterStore } from "./tenant-filter-store";
import { useUserFilterStore } from "./user-filter-store";
import { useGlossaryFilterStore } from "./glossary-filter-store";
import { useGlossaryItemFilterStore } from "./glossary-item-filter-store";
import { useProjectFilterStore } from "./project-filter-store";

/**
 * Store Manager - Centralized utility for managing all Zustand stores
 * 
 * This utility provides functions to clear all stores when:
 * - User logs out
 * - Tenant changes (to prevent data leakage between tenants)
 */

/**
 * Clear all Zustand stores
 * This should be called when:
 * 1. User logs out
 * 2. Tenant changes (to prevent cross-tenant data contamination)
 */
export function clearAllStores() {
  // Clear all filter stores
  useFileFilterStore.getState().reset();
  useTenantFilterStore.getState().reset();
  useUserFilterStore.getState().reset();
  useGlossaryFilterStore.getState().reset();
  useGlossaryItemFilterStore.getState().reset();
  useProjectFilterStore.getState().reset();
}

/**
 * Clear only tenant-specific stores
 * This can be used when switching tenants but staying logged in
 */
export function clearTenantSpecificStores() {
  // Clear stores that contain tenant-specific data
  useFileFilterStore.getState().reset();
  useUserFilterStore.getState().reset();
  useGlossaryFilterStore.getState().reset();
  useGlossaryItemFilterStore.getState().reset();
  useProjectFilterStore.getState().reset();
  
  // Keep tenant filter store as it's used for tenant selection
  // useTenantFilterStore.getState().reset(); // Don't clear this one
}

/**
 * Get all store states for debugging
 */
export function getAllStoreStates() {
  return {
    fileFilter: useFileFilterStore.getState(),
    tenantFilter: useTenantFilterStore.getState(),
    userFilter: useUserFilterStore.getState(),
    glossaryFilter: useGlossaryFilterStore.getState(),
    glossaryItemFilter: useGlossaryItemFilterStore.getState(),
    projectFilter: useProjectFilterStore.getState(),
  };
}

/**
 * Hook to get store clearing functions
 * Use this in components that need to clear stores
 */
export function useStoreCleaner() {
  return {
    clearAllStores,
    clearTenantSpecificStores,
    getAllStoreStates,
  };
}
