"use client";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type { TenantFilters, TenantSort } from "@/types";

interface TenantFilterState {
  filters: TenantFilters;
  sort?: TenantSort;
  pendingSort?: TenantSort;

  // Actions
  setFilters: (filters: TenantFilters) => void;
  updateFilters: (partialFilters: Partial<TenantFilters>) => void;
  setSort: (sort?: TenantSort) => void;
  setPendingSort: (sort?: TenantSort) => void;
  applyPendingSort: () => void;
  setSearch: (searchValue: string) => void;
  removeFilter: (filterKey: keyof TenantFilters, value?: string) => void;
  clearAllFilters: () => void;
  clearSort: () => void;
  reset: () => void;
}

const initialState = {
  filters: {} as TenantFilters,
  sort: undefined as TenantSort | undefined,
  pendingSort: undefined as TenantSort | undefined,
};

export const useTenantFilterStore = create<TenantFilterState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      setFilters: (filters) => set({ filters }, false, "setFilters"),

      updateFilters: (partialFilters) =>
        set(
          (state) => ({
            filters: { ...state.filters, ...partialFilters },
          }),
          false,
          "updateFilters"
        ),

      setSort: (sort) => set({ sort }, false, "setSort"),

      setPendingSort: (pendingSort) => set({ pendingSort }, false, "setPendingSort"),

      applyPendingSort: () =>
        set(
          (state) => ({
            sort: state.pendingSort,
            pendingSort: undefined,
          }),
          false,
          "applyPendingSort"
        ),

      setSearch: (searchValue) =>
        set(
          (state) => ({
            filters: {
              ...state.filters,
              name: searchValue || undefined,
            },
          }),
          false,
          "setSearch"
        ),

      removeFilter: (filterKey, value) =>
        set(
          (state) => {
            const newFilters = { ...state.filters };
            const currentValue = newFilters[filterKey];

            if (Array.isArray(currentValue) && value) {
              // Remove specific value from array
              const newArray = currentValue.filter((item) => item !== value);
              if (newArray.length === 0) {
                delete newFilters[filterKey];
              } else {
                (newFilters as any)[filterKey] = newArray;
              }
            } else {
              // Remove entire filter
              delete newFilters[filterKey];
            }

            return { filters: newFilters };
          },
          false,
          "removeFilter"
        ),

      clearAllFilters: () => set({ filters: {} }, false, "clearAllFilters"),

      clearSort: () => set({ sort: undefined }, false, "clearSort"),

      reset: () => set(initialState, false, "reset"),
    }),
    {
      name: "tenant-filter-store",
    }
  )
);

// Computed selectors
export const useTenantFilterSelectors = () => {
  const { filters, sort } = useTenantFilterStore();

  return {
    hasActiveFilters: Object.values(filters).some((value) => {
      if (Array.isArray(value)) return value.length > 0;
      return value && value.toString().trim() !== "";
    }),
    activeFilterCount: Object.values(filters).filter((value) => {
      if (Array.isArray(value)) return value.length > 0;
      return value && value.toString().trim() !== "";
    }).length,
    hasActiveSort: sort !== undefined,
    searchValue: filters.name || "",
  };
};
