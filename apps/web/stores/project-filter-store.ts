"use client";

import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import type { ProjectFilters, ProjectSort, ProjectResponse } from "@/types";

interface ProjectFilterState {
  // State
  filters: ProjectFilters;
  sort?: ProjectSort;
  pendingSort?: ProjectSort;
  selectedProject?: ProjectResponse;

  // Actions
  setFilters: (filters: ProjectFilters) => void;
  updateFilters: (partialFilters: Partial<ProjectFilters>) => void;
  setSort: (sort?: ProjectSort) => void;
  setPendingSort: (sort?: ProjectSort) => void;
  applyPendingSort: () => void;
  setSearch: (searchValue: string) => void;
  removeFilter: (filterKey: keyof ProjectFilters, value?: string) => void;
  clearAllFilters: () => void;
  clearSort: () => void;
  setSelectedProject: (project?: ProjectResponse) => void;
  clearSelectedProject: () => void;
  reset: () => void;
}

const initialState = {
  filters: {} as ProjectFilters,
  sort: undefined as ProjectSort | undefined,
  pendingSort: undefined as ProjectSort | undefined,
  selectedProject: undefined as ProjectResponse | undefined,
};

export const useProjectFilterStore = create<ProjectFilterState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Set complete filters object
        setFilters: (filters: ProjectFilters) => set({ filters }, false, "setFilters"),

        // Update filters partially (merge with existing)
        updateFilters: (partialFilters: Partial<ProjectFilters>) =>
          set(
            (state) => ({
              filters: { ...state.filters, ...partialFilters },
            }),
            false,
            "updateFilters"
          ),

        // Set sort configuration
        setSort: (sort?: ProjectSort) => set({ sort }, false, "setSort"),

        setPendingSort: (pendingSort) => set({ pendingSort }, false, "setPendingSort"),

        applyPendingSort: () =>
          set(
            (state) => ({
              sort: state.pendingSort,
              pendingSort: undefined,
            }),
            false,
            "applyPendingSort"
          ),

        // Update search (code filter)
        setSearch: (searchValue: string) =>
          set(
            (state) => ({
              filters: {
                ...state.filters,
                code: searchValue || undefined,
              },
            }),
            false,
            "setSearch"
          ),

        // Remove specific filter
        removeFilter: (filterKey: keyof ProjectFilters, value?: string) =>
          set(
            (state) => {
              const newFilters = { ...state.filters };

              if (value && Array.isArray(newFilters[filterKey])) {
                // Remove specific value from array filter
                const currentArray = newFilters[filterKey] as string[];
                const updatedArray = currentArray.filter((item) => item !== value);
                if (updatedArray.length === 0) {
                  delete newFilters[filterKey];
                } else {
                  (newFilters[filterKey] as string[]) = updatedArray;
                }
              } else {
                // Remove entire filter
                delete newFilters[filterKey];
              }

              return { filters: newFilters };
            },
            false,
            "removeFilter"
          ),

        // Clear all filters
        clearAllFilters: () => set({ filters: {} }, false, "clearAllFilters"),

        // Clear sort
        clearSort: () => set({ sort: undefined }, false, "clearSort"),

        // Set selected project
        setSelectedProject: (selectedProject?: ProjectResponse) =>
          set({ selectedProject }, false, "setSelectedProject"),

        // Clear selected project
        clearSelectedProject: () =>
          set({ selectedProject: undefined }, false, "clearSelectedProject"),

        // Reset to initial state
        reset: () => set(initialState, false, "reset"),
      }),
      {
        name: "project-filter-store",
        // Only persist selectedProject, not filters/sort which should reset on page load
        partialize: (state) => ({ selectedProject: state.selectedProject }),
      }
    ),
    {
      name: "project-filter-store",
    }
  )
);

// Selectors for computed values
export const useProjectFilterSelectors = () => {
  const { filters, sort, selectedProject } = useProjectFilterStore();

  return {
    hasActiveFilters: Object.values(filters).some((value) => {
      if (Array.isArray(value)) return value.length > 0;
      return value !== undefined && value !== "";
    }),

    activeFilterCount: Object.values(filters).reduce((count, value) => {
      if (Array.isArray(value)) return count + value.length;
      return value !== undefined && value !== "" ? count + 1 : count;
    }, 0),

    hasActiveSort: sort !== undefined,

    searchValue: filters.code || "",

    hasSelectedProject: selectedProject !== undefined,
    selectedProjectId: selectedProject?.id,
    selectedProjectName: selectedProject?.code,
  };
};
