import { TenantContext } from "@/shared/model/tenant.context";
import { UserContext } from "@/shared/model/user.context";
import { AsyncLocalStorage } from "async_hooks";

export interface RequestContext {
  requestId: string;
  user?: UserContext;
  tenant?: TenantContext;
}

// Private - not exported, only accessible within this module
const requestContextStore = new AsyncLocalStorage<RequestContext>();

/**
 * Initializes a new request context and runs the provided callback within it.
 * This should only be called once per request, typically in the first middleware.
 */
export function runWithRequestContext<T>(requestId: string, callback: () => T): T {
  const context: RequestContext = {
    requestId,
  };
  return requestContextStore.run(context, callback);
}

export const RequestContextHelper = {
  getRequestId: (): string | undefined => {
    return requestContextStore.getStore()?.requestId;
  },

  getUser: (): Readonly<UserContext> | undefined => {
    return requestContextStore.getStore()?.user;
  },

  setUser: (user: UserContext): void => {
    const store = requestContextStore.getStore();
    if (!store) {
      throw new Error("Request context not initialized. Ensure middleware is properly configured.");
    }
    if (store.user) {
      throw new Error(
        "User context has already been set for this request. Multiple setUser calls are not allowed."
      );
    }
    const readOnlyUser: Readonly<UserContext> = Object.freeze({ ...user });

    // Create a frozen copy to prevent tampering
    store.user = readOnlyUser;
  },

  getTenant: (): Readonly<TenantContext> | undefined => {
    return requestContextStore.getStore()?.tenant;
  },

  setTenant: (tenant: TenantContext): void => {
    const store = requestContextStore.getStore();
    if (!store) {
      throw new Error("Request context not initialized. Ensure middleware is properly configured.");
    }
    if (store.tenant) {
      throw new Error(
        "Tenant context has already been set for this request. Multiple setTenant calls are not allowed."
      );
    }
    const readOnlyTenant: Readonly<TenantContext> = Object.freeze({ ...tenant });

    // Create a frozen copy to prevent tampering
    store.tenant = readOnlyTenant;
  },
};
