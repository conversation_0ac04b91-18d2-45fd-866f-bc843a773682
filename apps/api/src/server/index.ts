import { json, urlencoded } from "body-parser";
import cors from "cors";
import express from "express";
import "reflect-metadata";
import { useExpressServer } from "routing-controllers";

import { appConfig } from "@/config";
import {
  authProviderErrorHandler,
  authProviderMiddleware,
  initAuthProvider,
  statefullVerifySession,
} from "@/providers/auth.provider";
import { closeDB, initDB } from "@/providers/db.provider";
import { logger } from "@/providers/logger.provider";

import { initEmailProvider } from "@/providers/email.provider";

import { initPolicyProvider } from "@/providers/policy.provider";

import { addRequestIdToRequestContext } from "@/middleware/request-id.middleware";

import { globalErrorHandler } from "@/middleware/global.error-handler";
import { addTenantToRequestContext } from "@/middleware/tenant-context.middleware";
import { ProtectedRoutes, UnprotectedRoutes } from "@/server/routes";
import { setupApiSwagger } from "@/swagger.api";
import { requestLoggerMiddleware } from "@/middleware/request-logger.middleware";
import { addUserToRequestContext } from "@/middleware/user-context.middleware";
import SuperTokens from "supertokens-node";

export const bootstrapServer = async () => {
  logger.info("Bootstrapping server...");

  await initDB();
  await initAuthProvider();
  await initEmailProvider();
  await initPolicyProvider();

  const app = express();

  app.use(
    cors({
      origin: ["http://localhost:3000", "http://localhost:3567"],
      credentials: true,
      methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
      allowedHeaders: ["Content-Type", "x-tenant-id", ...SuperTokens.getAllCORSHeaders()],
    })
  );

  app.disable("x-powered-by");
  app.use(urlencoded({ extended: true }));
  app.use(json());

  app.use(addRequestIdToRequestContext);

  app.use(requestLoggerMiddleware);

  app.use(authProviderMiddleware);

  useExpressServer(app, {
    controllers: UnprotectedRoutes,
    // Enable validation using class-validator
    validation: {
      whitelist: false,
      forbidNonWhitelisted: false,
      validationError: { target: false, value: false },
    },
    classTransformer: true,
    defaultErrorHandler: false,
  });

  app.use(statefullVerifySession, addUserToRequestContext, addTenantToRequestContext);
  useExpressServer(app, {
    routePrefix: "/api/v1",
    controllers: ProtectedRoutes,
    // Enable validation using class-validator
    validation: {
      whitelist: false,
      forbidNonWhitelisted: false,
      validationError: { target: false, value: false },
    },
    classTransformer: true,
    defaultErrorHandler: false,
  });

  setupApiSwagger(app);

  app.use(authProviderErrorHandler);

  app.use(globalErrorHandler);

  // Start server
  const server = app.listen(appConfig.server.port, () => {
    logger.info(`🚀 Server running on port ${appConfig.server.port}`);
  });

  // Graceful shutdown
  const shutdown = () => {
    logger.info("⚡ Shutting down server...");
    server.close(async () => {
      await closeDB();
      process.exit(0);
    });
  };

  process.on("SIGINT", shutdown);
  process.on("SIGTERM", shutdown);
};
