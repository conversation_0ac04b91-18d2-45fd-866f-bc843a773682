import { GlossaryItemActivateController } from "@/features/glossary-item-management/controller/glossary-item.activate";
import { GlossaryItemCreateController } from "@/features/glossary-item-management/controller/glossary-item.create";
import { GlossaryItemDeActivateController } from "@/features/glossary-item-management/controller/glossary-item.deactivate";
import { GlossaryItemListController } from "@/features/glossary-item-management/controller/glossary-item.list";
import { GlossaryItemUpdateController } from "@/features/glossary-item-management/controller/glossary-item.update";
import { GlossaryActivateController } from "@/features/glossary-management/controller/glossary.activate";
import { GlossaryCreateController } from "@/features/glossary-management/controller/glossary.create";
import { GlossaryDeActivateController } from "@/features/glossary-management/controller/glossary.deactivate";
import { GlossaryListController } from "@/features/glossary-management/controller/glossary.list";
import { GlossaryUpdateController } from "@/features/glossary-management/controller/glossary.update";
import { ProjectCreateController } from "@/features/project-management/controller/project.create";
import { ProjectFileListController } from "@/features/project-management/controller/project-file.list";
import { ProjectFileUploadController } from "@/features/project-management/controller/project-file.upload";
import { ProjectListController } from "@/features/project-management/controller/project.list";
import { ProjectUpdateController } from "@/features/project-management/controller/project.update";
import { TenantActivateController } from "@/features/tenant-management/controller/tenant.activate";
import { TenantDeactivateController } from "@/features/tenant-management/controller/tenant.deactivate";
import { TenantListController } from "@/features/tenant-management/controller/tenant.list";
import { TenantRegisterController } from "@/features/tenant-management/controller/tenant.register";
import { TenantUpdateController } from "@/features/tenant-management/controller/tenant.update";
import { UserActivateController } from "@/features/user-management/controller/user.activate";
import { UserDeactivateController } from "@/features/user-management/controller/user.deactivate";
import { UserForgotPasswordController } from "@/features/user-management/controller/user.forgot-password";
import { UserListController } from "@/features/user-management/controller/user.list";
import { UserProfileController } from "@/features/user-management/controller/user.me";
import { UserSendVerificationEmailController } from "@/features/user-management/controller/user.send-verify-email";
import { UserSignupController } from "@/features/user-management/controller/user.signup";
import { UserUpdateController } from "@/features/user-management/controller/user.update";
import { UserVerifyEmailController } from "@/features/user-management/controller/user.verify-email";
import { ProjectFileGetController } from "@/features/project-management/controller/project-file.get";
import { ProjectFileClassifyController } from "@/features/project-management/controller/project-file.classify";
import { ProjectGetController } from "@/features/project-management/controller/project.get";
import { ProjectFileMarkController } from "@/features/project-management/controller/project-file.mark";
import { ProjectFileExtractController } from "@/features/project-management/controller/project-file.extract";

export const UnprotectedRoutes = [UserForgotPasswordController, UserVerifyEmailController];

export const ProtectedRoutes = [
  UserSignupController,
  UserSendVerificationEmailController,
  UserProfileController,
  UserActivateController,
  UserDeactivateController,
  UserUpdateController,
  UserListController,
  TenantRegisterController,
  TenantListController,
  TenantUpdateController,
  TenantDeactivateController,
  TenantActivateController,
  GlossaryCreateController,
  GlossaryListController,
  GlossaryUpdateController,
  GlossaryActivateController,
  GlossaryDeActivateController,
  GlossaryItemCreateController,
  GlossaryItemListController,
  GlossaryItemUpdateController,
  GlossaryItemActivateController,
  GlossaryItemDeActivateController,
  ProjectCreateController,
  ProjectFileListController,
  ProjectFileUploadController,
  ProjectUpdateController,
  ProjectListController,
  ProjectFileGetController,
  ProjectFileClassifyController,
  ProjectGetController,
  ProjectFileMarkController,
  ProjectFileExtractController,
];
