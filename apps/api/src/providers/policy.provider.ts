import {
  Action,
  policy_rules,
  Resource,
  RoleDefinition,
  TenantRoleConfig,
} from "@/config/policy/rules";
import { logger } from "@/providers/logger.provider";
import { UserRole } from "@repo/prisma/client";

export const initPolicyProvider = async () => {
  try {
    if (!policy_rules.default) {
      throw new Error("Policy rules must have a 'default' configuration");
    }

    if (!policy_rules.default.roles || typeof policy_rules.default.roles !== "object") {
      throw new Error("Default policy configuration must have 'roles' object");
    }
    logger.info("✅ Policy provider initialized successfully");
  } catch (error) {
    throw new Error(`❌ Failed to initialize policy provider: ${error}`);
  }
};

export const getPolicyForTenant = (tenantId: string): TenantRoleConfig => {
  return policy_rules[tenantId] || policy_rules.default;
};

export const getPolicyForRole = (tenantId: string, role: string): RoleDefinition => {
  const tenantPolicy = getPolicyForTenant(tenantId);
  return tenantPolicy.roles[role as UserRole]!;
};
