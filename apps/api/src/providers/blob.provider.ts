import { BlobServiceClient, BlockBlobClient, BlobUploadCommonResponse } from "@azure/storage-blob";
import { Readable } from "stream";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { appConfig } from "@/config";

/**
 * Configuration interface for Azure Blob Storage
 */
export interface AzureBlobConfig {
  connectionString: string;
  containerName: string;
}

/**
 * Upload options for blob storage
 */
export interface BlobUploadOptions {
  /**
   * The name/path for the blob in the container
   */
  blobName: string;

  /**
   * Content type of the file (e.g., 'image/jpeg', 'application/pdf')
   */
  contentType?: string;

  /**
   * Custom metadata to attach to the blob
   */
  metadata?: Record<string, string>;

  /**
   * Tags to attach to the blob for categorization
   */
  tags?: Record<string, string>;

  /**
   * Whether to overwrite existing blob with same name
   * @default true
   */
  overwrite?: boolean;

  /**
   * AbortSignal to cancel the upload operation
   */
  abortSignal?: AbortSignal;
}

/**
 * Result of a successful blob upload
 */
export interface BlobUploadResult {
  /**
   * The full URL to access the uploaded blob
   */
  url: string;

  /**
   * The name of the uploaded blob
   */
  blobName: string;

  /**
   * ETag of the uploaded blob
   */
  etag: string;

  /**
   * Size of the uploaded blob in bytes
   */
  contentLength?: number;

  /**
   * Last modified timestamp
   */
  lastModified?: Date;

  /**
   * Request ID for tracking
   */
  requestId?: string;
}

/**
 * Azure Blob Storage File Management Provider
 *
 * Provides stream-based file upload capabilities to Azure Blob Storage
 * without loading files into memory or temporary local storage.
 */
export class BlobProvider {
  private blobServiceClient: BlobServiceClient;
  private containerName: string;

  constructor(config: AzureBlobConfig) {
    this.blobServiceClient = BlobServiceClient.fromConnectionString(config.connectionString);
    this.containerName = config.containerName;

    logger.info(
      {
        containerName: this.containerName,
      },
      "Blob provider initialized"
    );
  }

  /**
   * Upload a file stream directly to Azure Blob Storage
   *
   * This method accepts a readable stream and uploads it directly to Azure Blob Storage
   * without buffering the entire file in memory or writing to temporary storage.
   *
   * @param stream - Readable stream containing the file data
   * @param options - Upload configuration options
   * @returns Promise resolving to upload result with blob URL and metadata
   *
   * @example
   * ```typescript
   * const fileStream = fs.createReadStream('path/to/file.pdf');
   * const result = await fileProvider.uploadStream(fileStream, {
   *   blobName: 'documents/file.pdf',
   *   contentType: 'application/pdf',
   *   metadata: { uploadedBy: 'user123' },
   *   tags: { department: 'finance' }
   * });
   * console.log('File uploaded to:', result.url);
   * ```
   */
  public async uploadStream(
    stream: Readable,
    options: BlobUploadOptions
  ): Promise<BlobUploadResult> {
    const startTime = Date.now();
    const requestId = RequestContextHelper.getRequestId();
    const userId = RequestContextHelper.getUser()!.id;
    const tenantId = RequestContextHelper.getTenant()!.id;

    logger.info(
      {
        blobName: options.blobName,
        contentType: options.contentType,
        overwrite: options.overwrite ?? true,
        hasMetadata: !!options.metadata,
        hasTags: !!options.tags,
        requestId,
        userId,
        tenantId,
      },
      "Starting stream upload to Azure Blob Storage"
    );

    try {
      // Get container client
      const containerClient = this.blobServiceClient.getContainerClient(this.containerName);

      // Get block blob client for the specific blob
      const blockBlobClient: BlockBlobClient = containerClient.getBlockBlobClient(options.blobName);

      // Prepare upload options
      const uploadOptions: any = {
        blobHTTPHeaders: {
          blobContentType: options.contentType,
        },
        metadata: {
          ...options.metadata,
          uploadedBy: userId,
          tenantId: tenantId,
          uploadedAt: new Date().toISOString(),
          requestId: requestId,
        },
        tags: options.tags,
        abortSignal: options.abortSignal,
      };

      // Check if blob exists and handle overwrite setting
      if (options.overwrite === false) {
        const exists = await blockBlobClient.exists();
        if (exists) {
          throw new Error(`Blob '${options.blobName}' already exists and overwrite is disabled`);
        }
      }

      logger.info(
        {
          blobName: options.blobName,
          uploadOptions: {
            contentType: uploadOptions.blobHTTPHeaders?.blobContentType,
            metadataKeys: Object.keys(uploadOptions.metadata || {}),
            tagKeys: Object.keys(uploadOptions.tags || {}),
          },
        },
        "Uploading stream to blob storage"
      );

      // Upload the stream directly to Azure Blob Storage
      const uploadResponse: BlobUploadCommonResponse = await blockBlobClient.uploadStream(
        stream,
        undefined, // bufferSize - let Azure SDK use default
        undefined, // maxConcurrency - let Azure SDK use default
        uploadOptions
      );

      const uploadDuration = Date.now() - startTime;

      logger.info(
        {
          blobName: options.blobName,
          etag: uploadResponse.etag,
          lastModified: uploadResponse.lastModified,
          requestId: uploadResponse.requestId,
          uploadDurationMs: uploadDuration,
        },
        "Stream upload completed successfully"
      );

      // Construct the blob URL
      const blobUrl = blockBlobClient.url;

      const blobProperties = await blockBlobClient.getProperties();

      const result: BlobUploadResult = {
        url: blobUrl,
        blobName: options.blobName,
        etag: uploadResponse.etag || "",
        contentLength: blobProperties.contentLength,
        lastModified: uploadResponse.lastModified,
        requestId: uploadResponse.requestId,
      };

      logger.info(
        {
          result: {
            url: result.url,
            blobName: result.blobName,
            etag: result.etag,
            contentLength: result.contentLength,
          },
          uploadDurationMs: uploadDuration,
        },
        "File upload result generated"
      );

      return result;
    } catch (error) {
      const uploadDuration = Date.now() - startTime;

      logger.error(
        {
          blobName: options.blobName,
          error: error instanceof Error ? error.message : String(error),
          uploadDurationMs: uploadDuration,
          requestId,
        },
        "Stream upload failed"
      );

      // Re-throw with more context
      throw new Error(
        `Failed to upload stream to blob '${options.blobName}': ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  public async downloadStream(blobName: string): Promise<any> {
    const containerClient = this.blobServiceClient.getContainerClient(this.containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    // Get the download response with metadata
    const downloadResponse = await blockBlobClient.download();

    if (!downloadResponse.readableStreamBody) {
      throw new Error(`No readable stream available for blob: ${blobName}`);
    }

    return downloadResponse.readableStreamBody;
  }
}

export const blobProvider = new BlobProvider({
  connectionString: appConfig.blob.connectionString,
  containerName: appConfig.blob.rootFolderName,
});
