import { appConfig } from "@/config";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { Prisma, PrismaClient } from "@repo/prisma/client";

const prisma = new PrismaClient({
  datasources: {
    db: {
      url:
        "postgresql://" +
        encodeURIComponent(appConfig.database.user) +
        ":" +
        encodeURIComponent(appConfig.database.password) +
        "@" +
        appConfig.database.host +
        ":" +
        appConfig.database.port +
        "/" +
        appConfig.database.database +
        "?schema=" +
        appConfig.database.schema,
    },
  },
  log: ["query", "info", "warn", "error"],
});

export const initDB = async () => {
  try {
    await prisma.$connect();
    logger.info("✅ Database connected successfully");
  } catch (err) {
    logger.error(err, "❌ Failed to connect to database:");
    throw err;
  }
};

export const closeDB = async () => {
  await prisma.$disconnect();
  logger.info("🛑 Database connection closed");
};

export type DatabaseClient = Omit<
  PrismaClient,
  "$extends" | "$transaction" | "$disconnect" | "$connect" | "$on"
>;

export const dbClient = (): DatabaseClient => {
  return prisma as DatabaseClient;
};

export const withTransaction = async <T>(
  callback: (tx: DatabaseClient) => Promise<T>
): Promise<T> => {
  const tenantId = RequestContextHelper.getTenant()?.id;

  return await prisma.$transaction(async (tx) => {
    // Set tenant context for the transaction
    await tx.$executeRawUnsafe(`SET app.current_tenant = '${tenantId}'`);
    return await callback(tx as DatabaseClient);
  });
};
