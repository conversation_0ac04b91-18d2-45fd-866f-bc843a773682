import supertokens, { RecipeUserId } from "supertokens-node";
import { error<PERSON><PERSON><PERSON>, middleware } from "supertokens-node/framework/express";
import type { TypeInput } from "supertokens-node/types";

import EmailPassword from "supertokens-node/recipe/emailpassword";
import EmailVerification from "supertokens-node/recipe/emailverification";
import Session from "supertokens-node/recipe/session";
import UserMetadata from "supertokens-node/recipe/usermetadata";

import { appConfig } from "@/config";
import { AuthError, AuthErrorType } from "@/exception-handling/auth.error";
import { logger } from "@/providers/logger.provider";
import { AuthUser } from "@/shared/model/auth.user";
import { UserClaims } from "@/shared/model/auth.user-claims";
import { verifySession } from "supertokens-node/recipe/session/framework/express";

const DEFAULT_SUPERTOKENS_TENANT_ID = "public";
const DEFAULT_SUPERTOKENS_APP_ID = "public";

let isInitialized = false;

const SuperTokensConfig: TypeInput = {
  supertokens: {
    connectionURI: appConfig.auth.connectionURI,
  },
  appInfo: {
    appName: "PMS App",
    apiDomain: appConfig.auth.serverDomain,
    websiteDomain: appConfig.auth.websiteDomain,
    apiBasePath: "/auth",
    websiteBasePath: "/auth",
  },
  recipeList: [
    EmailVerification.init({
      mode: "REQUIRED",
      override: {
        apis: (originalImplementation) => {
          return {
            ...originalImplementation,
            verifyEmailPOST: undefined,
            generateEmailVerifyTokenPOST: undefined,
          };
        },
      },
    }),
    EmailPassword.init({
      override: {
        functions: (originalFunction) => ({
          ...originalFunction,

          async signIn(input) {
            const response = await originalFunction.signIn(input);

            if (response.status === "OK") {
              await Session.revokeAllSessionsForUser(response.user.id);
              const isEmailVerified = await EmailVerification.isEmailVerified(
                response.recipeUserId
              );
              if (!isEmailVerified) {
                logger.error(
                  {
                    userId: response.user.id,
                    email: response.user.emails[0],
                  },
                  "User tried to login without verifying email"
                );
                throw new AuthError(AuthErrorType.UnAuthorized);
              }
            }
            return response;
          },
        }),
        apis: (originalImplementation) => {
          return {
            ...originalImplementation,
            emailExistsGET: undefined,
            generatePasswordResetTokenPOST: undefined,
            signUpPOST: undefined,
          };
        },
      },
    }),
    UserMetadata.init(),
    Session.init({
      cookieSameSite: "lax",
      exposeAccessTokenToFrontendInCookieBasedAuth: true,
      override: {
        functions: (originalImplementation) => {
          return {
            ...originalImplementation,
            createNewSession: async function (input) {
              const authUserMetadata = await getAuthUserMetadata(input.userId);
              // Make sure an active user has metadata updated in supertokens
              if (!authUserMetadata) {
                throw new AuthError(AuthErrorType.UnAuthorized);
              }
              return originalImplementation.createNewSession({
                ...input,
                accessTokenPayload: {
                  user: authUserMetadata,
                },
              });
            },
          };
        },
      },
    }),
  ],
};

export const initAuthProvider = async () => {
  if (isInitialized) return; // Prevent re-init

  try {
    supertokens.init(SuperTokensConfig);
    isInitialized = true;
    logger.info("✅ Auth provider initialized successfully");
  } catch (err) {
    logger.error(err, "❌ Failed to initialize auth provider");
    throw err;
  }
};

export const getUserDetailsByEmail = async (email: string): Promise<AuthUser> => {
  let response = await supertokens.listUsersByAccountInfo(DEFAULT_SUPERTOKENS_TENANT_ID, {
    email: email,
  });
  if (response && response.length > 0) {
    const user = response[0]!;
    return {
      id: user.id,
      email: user.emails[0]!,
      isEmailVerified: user.loginMethods[0]!.verified,
    };
  }
  throw new AuthError(AuthErrorType.UserDoesNotExists);
};

export const getAuthUserMetadata = async (user_id: string): Promise<UserClaims> => {
  const response = await UserMetadata.getUserMetadata(user_id);
  return response.metadata;
};

export const signUpUsingEmailPassword = async (
  userClaims: UserClaims,
  password: string
): Promise<AuthUser> => {
  const res = await EmailPassword.signUp(DEFAULT_SUPERTOKENS_TENANT_ID, userClaims.email, password);
  if (res.status === "OK") {
    await UserMetadata.updateUserMetadata(res.user.id, userClaims);
    return {
      id: res.user.id,
      email: res.user.emails[0]!,
      isEmailVerified: res.user.loginMethods[0]!.verified,
    };
  }
  throw new AuthError(AuthErrorType.UserAlreadyExists);
};

export const getEmailVerificationToken = async (
  email: string,
  revokePreviousEmailVerificationTokens: boolean = true
): Promise<string> => {
  const authUser = await getUserDetailsByEmail(email);
  const recipeUserId = new RecipeUserId(authUser.id);

  if (revokePreviousEmailVerificationTokens) {
    EmailVerification.revokeEmailVerificationTokens(
      DEFAULT_SUPERTOKENS_TENANT_ID,
      recipeUserId,
      email
    );
  }

  const res = await EmailVerification.createEmailVerificationToken(
    DEFAULT_SUPERTOKENS_TENANT_ID,
    recipeUserId,
    email
  );
  if (res.status === "OK") {
    return res.token;
  }
  throw new AuthError(AuthErrorType.UserAlreadyVerified);
};

export const getResetPasswordToken = async (
  email: string,
  user_id: string
): Promise<
  | {
      status: "OK";
      token: string;
    }
  | {
      status: "UNKNOWN_USER_ID_ERROR";
    }
> => {
  const res = await EmailPassword.createResetPasswordToken(
    DEFAULT_SUPERTOKENS_TENANT_ID,
    user_id,
    email
  );
  return res;
};

export const verifyEmailByToken = async (token: string): Promise<AuthUser> => {
  const res = await EmailVerification.verifyEmailUsingToken(DEFAULT_SUPERTOKENS_TENANT_ID, token);

  if (res.status === "OK") {
    return {
      id: res.user.recipeUserId.getAsString(),
      email: res.user.email,
      isEmailVerified: true,
    };
  }
  throw new AuthError(AuthErrorType.InvalidToken);
};

export const rollbackEmailVerification = async (id: string, email: string): Promise<void> => {
  const recipeUserId = new RecipeUserId(id);
  const res = await EmailVerification.unverifyEmail(recipeUserId, email);
  if (res.status === "OK") {
    return;
  }
  throw new AuthError(AuthErrorType.InvalidToken);
};

export const updateAuthUser = async (userClaims: UserClaims): Promise<void> => {
  const authUser = await getUserDetailsByEmail(userClaims.email);
  await UserMetadata.updateUserMetadata(authUser.id, userClaims);
};

export const deactivateUser = async (email: string): Promise<void> => {
  const authUser = await getUserDetailsByEmail(email);
  await supertokens.deleteUser(authUser.id);
};

export const authProviderMiddleware = middleware();

export const authProviderErrorHandler = errorHandler();

export const statelessVerifySession = verifySession();

export const statefullVerifySession = verifySession({ checkDatabase: true });

export { Session };
