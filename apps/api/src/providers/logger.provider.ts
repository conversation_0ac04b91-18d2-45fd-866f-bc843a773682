import { RequestContextHelper } from "@/server/context";
import pino from "pino";

export const logger = pino({
  transport:
    process.env.API_SERVER_NODE_ENV === "development"
      ? { target: "pino-pretty", options: { colorize: true, translateTime: true } }
      : undefined,
  mixin() {
    return {
      "x-request-id": RequestContextHelper.getRequestId(),
      "x-user-id": RequestContextHelper.getUser()?.id,
      "x-tenant-id": RequestContextHelper.getTenant()?.id,
    };
  },
});
