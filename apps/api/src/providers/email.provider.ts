import { appConfig } from "@/config";
import { logger } from "@/providers/logger.provider";
import { EmailClient } from "@azure/communication-email";
import fs from "fs/promises";
import Handlebars from "handlebars";
import path from "path";

// Email template types
interface EmailTemplateData {
  name?: string;
  verifyUrl?: string;
  resetLink?: string;
  logo?: string;
  year?: number;
}

interface SendEmailOptions {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  template: string;
  data?: EmailTemplateData;
  senderAddress?: string;
}

// Convenient email helper functions
enum EmailTemplates {
  EMAIL_VERIFY = "email-verify",
  PASSWORD_RESET = "password-reset",
  RESEND_VERIFICATION = "resend-verification-email",
  TENANT_VERIFY = "tenant-verify-email",
  USER_DEACTIVATED = "user-deactivated",
  TENANT_DEACTIVATED = "tenant-deactivated",
}

let emailClient: EmailClient;
// TODO: move to config
const logoUrl =
  "https://cdn.builder.io/api/v1/image/assets%2Feacf4d198f7d471b831a310bf0945bc6%2F9af88a351923405ca30afd8d23a44e8b?format=webp&width=800";

// Initialize email provider
export const initEmailProvider = async () => {
  try {
    emailClient = new EmailClient(appConfig.email.connectionString);
    logger.info("✅ Email provider initialized successfully");
  } catch (err) {
    logger.error(err, "❌ Failed to initialize email provider");
    throw err;
  }
};

// Template cache
const templateCache = new Map<string, HandlebarsTemplateDelegate>();

// Load and compile template
async function loadTemplate(templateName: string): Promise<HandlebarsTemplateDelegate> {
  if (templateCache.has(templateName)) {
    return templateCache.get(templateName)!;
  }

  try {
    const templatePath = path.resolve(
      process.cwd(),
      `src/config/email/templates/${templateName}.html`
    );
    const templateContent = await fs.readFile(templatePath, "utf-8");
    const compiledTemplate = Handlebars.compile(templateContent);

    templateCache.set(templateName, compiledTemplate);
    return compiledTemplate;
  } catch (error) {
    throw new Error(`Failed to load email template '${templateName}': ${error}`);
  }
}

async function sendEmail(options: SendEmailOptions): Promise<void> {
  try {
    // Load and compile template
    const template = await loadTemplate(options.template);

    // Prepare template data with defaults
    const templateData: EmailTemplateData = {
      year: new Date().getFullYear(),
      logo: logoUrl,
      ...options.data,
    };

    // Render HTML content
    const htmlContent = template(templateData);

    // Prepare recipients
    const toRecipients = Array.isArray(options.to)
      ? options.to.map((email) => ({ address: email }))
      : [{ address: options.to }];

    const ccRecipients = options.cc
      ? Array.isArray(options.cc)
        ? options.cc.map((email) => ({ address: email }))
        : [{ address: options.cc }]
      : undefined;

    const bccRecipients = options.bcc
      ? Array.isArray(options.bcc)
        ? options.bcc.map((email) => ({ address: email }))
        : [{ address: options.bcc }]
      : undefined;

    // Send email
    const poller = await emailClient.beginSend({
      senderAddress: options.senderAddress!,
      content: {
        subject: options.subject,
        html: htmlContent,
      },
      recipients: {
        to: toRecipients,
        cc: ccRecipients,
        bcc: bccRecipients,
      },
    });

    await poller.pollUntilDone();
    logger.info(`✅ Email sent successfully to ${options.to}`);
  } catch (error) {
    logger.error(error, "❌ Failed to send email:");
    throw error;
  }
}

export async function sendVerificationEmail(
  email: string,
  name: string,
  verifyUrl: string
): Promise<void> {
  await sendEmail({
    to: email,
    subject: "Please verify your email",
    template: EmailTemplates.EMAIL_VERIFY,
    data: {
      name,
      verifyUrl,
    },
    senderAddress: appConfig.email.senderEmail,
  });
}

export async function sendTenantVerificationEmail(
  email: string,
  name: string,
  verifyUrl: string
): Promise<void> {
  await sendEmail({
    to: email,
    subject: "Please verify your business",
    template: EmailTemplates.EMAIL_VERIFY,
    data: {
      name,
      verifyUrl,
    },
    senderAddress: appConfig.email.senderEmail,
  });
}

export async function sendPasswordResetEmail(
  email: string,
  name: string,
  resetLink: string
): Promise<void> {
  await sendEmail({
    to: email,
    subject: "Reset your password",
    template: EmailTemplates.PASSWORD_RESET,
    data: {
      name,
      resetLink,
    },
    senderAddress: appConfig.email.senderEmail,
  });
}

export async function sendUserDeactivatedEmail(email: string, name: string): Promise<void> {
  await sendEmail({
    to: email,
    subject: "User Deactivated",
    template: EmailTemplates.USER_DEACTIVATED,
    data: {
      name,
    },
    senderAddress: appConfig.email.senderEmail,
  });
}

export async function sendTenantDeactivatedEmail(email: string[], name: string): Promise<void> {
  await sendEmail({
    to: email,
    subject: "Account Deactivated",
    template: EmailTemplates.USER_DEACTIVATED,
    data: {
      name,
    },
    senderAddress: appConfig.email.senderEmail,
  });
}
