export enum AuthErrorType {
  UserAlreadyExists = "UserAlreadyExists",
  UserDoesNotExists = "UserDoesNotExists",
  UserAlreadyVerified = "UserAlreadyVerified",
  InvalidToken = "InvalidToken",
  TokenExpired = "TokenExpired",
  PermissionDenied = "PermissionDenied",
  ServiceUnavailable = "ServiceUnavailable",
  UnAuthorized = "UnAuthorized",
}
export class AuthError extends Error {
  constructor(
    public type: AuthErrorType,
    public details?: Record<string, any>
  ) {
    super(`Auth failure: ${type}`);
    Object.setPrototypeOf(this, new.target.prototype);
  }
}
