import { BaseError } from "@/exception-handling/base.error";
import { TenantUpdateRequest } from "@/features/tenant-management/model/tenant-update.request";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import { TenantStatus, UserStatus } from "@repo/prisma/client";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Patch, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { TenantService } from "@/features/tenant-management/service/tenant.service";
@JsonController("/tenant")
@UseBefore(hasPermission(Resource.Tenant, [Action.Create]))
export class TenantUpdateController {
  @Patch("/")
  public async process(
    @Body() payload: TenantUpdateRequest,
    @Res() res: Response
  ): Promise<Response> {
    logger.info(
      {
        tenantId: payload.id,
        updateFields: Object.keys(payload).filter(
          (key) => key !== "id" && payload[key as keyof TenantUpdateRequest] !== undefined
        ),
      },
      "Starting tenant update process"
    );

    logger.info(
      {
        tenantId: payload.id,
        authenticatedUserId: RequestContextHelper.getUser()!.id,
      },
      "Checking if tenant exists and is active"
    );

    // Check if tenant exists and is active
    const tenant = await TenantService.getTenantById(payload.id, true);

    if (!tenant) {
      logger.error(
        {
          tenantId: payload.id,
        },
        "Tenant not found for update"
      );
      throw new BaseError("TENANT_NOT_FOUND", "Tenant not found", StatusCodes.NOT_FOUND, {});
    }

    logger.info(
      {
        tenantId: payload.id,
        currentStatus: tenant.status,
      },
      "Tenant found, checking current status"
    );

    if (tenant.status !== TenantStatus.Active) {
      logger.error(
        {
          tenantId: payload.id,
          currentStatus: tenant.status,
        },
        "Tenant is not active, cannot update"
      );
      throw new BaseError(
        "TENANT_INACTIVE",
        "Tenant must be active to update",
        StatusCodes.BAD_REQUEST,
        {}
      );
    }

    const activeAndPendingUserCount = tenant.users.filter(
      (user) => user.status === UserStatus.Active || user.status === UserStatus.PendingVerification
    ).length;

    // If maxUsers is being updated, validate against current user count
    if (payload.maxUsers !== undefined) {
      logger.info(
        {
          tenantId: payload.id,
          currentUserCount: activeAndPendingUserCount,
          newMaxUsers: payload.maxUsers,
        },
        "Validating max users against current user count"
      );

      if (payload.maxUsers < activeAndPendingUserCount) {
        logger.error(
          {
            tenantId: payload.id,
            currentUserCount: activeAndPendingUserCount,
            newMaxUsers: payload.maxUsers,
          },
          "Max users validation failed - new limit is less than current user count"
        );
        throw new BaseError(
          "INVALID_MAX_USERS",
          `Maximum users cannot be less than current active and pending users count (${activeAndPendingUserCount})`,
          StatusCodes.BAD_REQUEST,
          { currentUserCount: activeAndPendingUserCount }
        );
      }
    }

    const updatedTenant = await TenantService.updateTenant(payload);

    logger.info(
      {
        tenantId: payload.id,
        tenantCode: tenant.code,
        tenantName: tenant.name,
      },
      "Tenant updated successfully"
    );

    return res.status(StatusCodes.OK).json(BaseResponse.ok());
  }
}
