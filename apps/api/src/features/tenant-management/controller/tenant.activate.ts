import { BaseError } from "@/exception-handling/base.error";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import { TenantStatus } from "@repo/prisma/client";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { JsonController, Param, Patch, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { TenantService } from "@/features/tenant-management/service/tenant.service";
@JsonController("/tenant")
@UseBefore(hasPermission(Resource.Tenant, [Action.Update]))
export class TenantActivateController {
  @Patch("/:tenantId/activate")
  public async process(
    @Param("tenantId") tenantId: string,
    @Res() res: Response
  ): Promise<Response> {
    logger.info(
      {
        tenantId,
      },
      "Starting tenant activation process"
    );

    logger.info(
      {
        tenantId,
        authenticatedUserId: RequestContextHelper.getUser()!.id,
      },
      "Checking if tenant exists and is inactive"
    );

    // Check if tenant exists and is active
    const tenant = await TenantService.getTenantById(tenantId);

    if (!tenant) {
      logger.error(
        {
          tenantId,
        },
        "Tenant not found for activation"
      );
      throw new BaseError("TENANT_NOT_FOUND", "Tenant not found", StatusCodes.NOT_FOUND, {});
    }

    logger.info(
      {
        tenantId,
        currentStatus: tenant.status,
      },
      "Tenant found, checking current status"
    );

    if (tenant.status !== TenantStatus.Inactive) {
      logger.error(
        {
          tenantId,
          currentStatus: tenant.status,
        },
        "Tenant is not inactive, cannot activate"
      );
      throw new BaseError(
        "TENANT_ALREADY_ACTIVE",
        "Tenant must be inactive to activate",
        StatusCodes.BAD_REQUEST,
        {}
      );
    }

    logger.info(
      {
        tenantId,
        authenticatedUserId: RequestContextHelper.getUser()!.id,
      },
      "Activating tenant"
    );

    // Activate tenant
    await TenantService.updateTenantStatus(tenantId, TenantStatus.Active);

    logger.info(
      {
        tenantId,
        tenantCode: tenant.code,
        tenantName: tenant.name,
      },
      "Tenant activated successfully"
    );

    return res.status(StatusCodes.OK).json(BaseResponse.ok());
  }
}
