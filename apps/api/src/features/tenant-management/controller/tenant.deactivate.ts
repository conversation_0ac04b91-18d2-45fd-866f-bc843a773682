import { BaseError } from "@/exception-handling/base.error";
import { sendTenantDeactivatedEmail } from "@/providers/email.provider";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import { TenantStatus, UserRole } from "@repo/prisma/client";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { JsonController, Param, Patch, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { TenantService } from "@/features/tenant-management/service/tenant.service";
@JsonController("/tenant")
@UseBefore(hasPermission(Resource.Tenant, [Action.Delete]))
export class TenantDeactivateController {
  @Patch("/:tenantId/deactivate")
  public async process(
    @Param("tenantId") tenantId: string,
    @Res() res: Response
  ): Promise<Response> {
    logger.info(
      {
        tenantId,
      },
      "Starting tenant deactivation process"
    );

    logger.info(
      {
        tenantId,
        authenticatedUserId: RequestContextHelper.getUser()!.id,
      },
      "Checking if tenant exists and is active"
    );

    // Check if tenant exists and is active
    const tenant = await TenantService.getTenantById(tenantId, true);

    if (!tenant) {
      logger.error(
        {
          tenantId,
        },
        "Tenant not found for deactivation"
      );
      throw new BaseError("TENANT_NOT_FOUND", "Tenant not found", StatusCodes.NOT_FOUND, {});
    }

    logger.info(
      {
        tenantId,
        currentStatus: tenant.status,
      },
      "Tenant found, checking current status"
    );

    if (tenant.status !== TenantStatus.Active) {
      logger.error(
        {
          tenantId,
          currentStatus: tenant.status,
        },
        "Tenant is not active, cannot deactivate"
      );
      throw new BaseError(
        "TENANT_NOT_ACTIVE",
        "Tenant must be active to deactivate",
        StatusCodes.BAD_REQUEST,
        {}
      );
    }

    logger.info(
      {
        tenantId,
        authenticatedUserId: RequestContextHelper.getUser()!.id,
      },
      "Deactivating tenant"
    );

    await TenantService.updateTenantStatus(tenantId, TenantStatus.Inactive);

    logger.info(
      {
        tenantId,
        tenantCode: tenant.code,
        tenantName: tenant.name,
      },
      "Tenant deactivated successfully"
    );

    logger.info({ tenantId }, "Fetching admin emails for notification");

    const adminEmails = tenant.users
      .filter((user) => user.role === UserRole.TenantAdmin)
      .map((user) => user.emailId);

    logger.info(
      {
        tenantId,
        adminEmails: adminEmails,
      },
      "Sending deactivation notification email"
    );

    sendTenantDeactivatedEmail(adminEmails, tenant.name!);

    return res.status(StatusCodes.OK).json(BaseResponse.ok());
  }
}
