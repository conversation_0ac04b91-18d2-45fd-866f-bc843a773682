import { TenantListRequest } from "@/features/tenant-management/model/tenant-list.request";
import {
  TenantListResponse,
  TenantResponse,
} from "@/features/tenant-management/model/tenant-list.response";
import { dbClient } from "@/providers/db.provider";
import { logger } from "@/providers/logger.provider";
import { BaseResponse } from "@/shared/model/base.response";
import { Prisma, UserStatus } from "@repo/prisma/client";
import { Body, JsonController, Post, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { hasPermission } from "@/middleware/has-permission.middleware";

@JsonController("/tenants/find")
@UseBefore(hasPermission(Resource.Tenant, [Action.Read]))
export class TenantListController {
  @Post("/")
  public async process(
    @Body() payload: TenantListRequest
  ): Promise<BaseResponse<TenantListResponse>> {
    logger.info(
      {
        filters: payload.filters,
        sort: payload.sort,
        limit: payload.limit,
        cursor: payload.cursor ? "present" : "none",
      },
      "Starting tenant list query"
    );

    // Build where clause from filters
    const whereClause = this.buildWhereClause(payload);

    // Build order by clause from sort
    const orderBy = this.buildOrderByClause(payload);

    // Build cursor-based pagination
    const cursorWhere = this.buildCursorClause(payload.cursor, payload.sort);

    // Set pagination parameters
    const limit = payload.limit!;
    const take = limit + 1; // Take one extra to check if there are more results

    logger.info(
      {
        whereClause,
        orderBy,
        cursorWhere,
        limit,
        take,
      },
      "Built query parameters for tenant listing"
    );

    // Combine where clauses
    const finalWhere: Prisma.TenantWhereInput = {
      AND: [whereClause, cursorWhere].filter((clause) => Object.keys(clause).length > 0),
    };

    logger.info(
      {
        finalWhere,
      },
      "Executing tenant query"
    );

    const tenants = await dbClient().tenant.findMany({
      where: finalWhere,
      orderBy,
      take,
    });

    logger.info(
      {
        tenantCount: tenants.length,
        hasMore: tenants.length > limit,
      },
      "Tenant query completed"
    );

    // Check if there are more results
    const hasMore = tenants.length > limit;
    if (hasMore) {
      tenants.pop(); // Remove the extra record
    }

    // Get tenant IDs for user count query
    const tenantIds = tenants.map((tenant) => tenant.id);

    logger.info(
      {
        tenantIds,
        tenantCount: tenantIds.length,
      },
      "Fetching user counts for tenants"
    );

    // Get current active users count per tenant
    const userCounts = await dbClient().user.groupBy({
      by: ["tenantId"],
      where: {
        tenantId: { in: tenantIds },
        status: {
          in: [UserStatus.Active, UserStatus.PendingVerification],
        },
        isDeleted: false,
      },
      _count: {
        id: true,
      },
    });

    logger.info(
      {
        userCountsFound: userCounts.length,
        userCountMap: Object.fromEntries(
          userCounts.map((count) => [count.tenantId, count._count.id])
        ),
      },
      "User counts retrieved and mapped"
    );

    // Create a map for quick lookup
    const userCountMap = new Map(userCounts.map((count) => [count.tenantId, count._count.id]));

    // Generate next cursor based on sort field
    const nextCursor =
      hasMore && tenants.length > 0
        ? this.generateCursor(tenants[tenants.length - 1]!, payload.sort)
        : undefined;

    logger.info(
      {
        hasMore,
        nextCursor,
        resultCount: tenants.length,
      },
      "Generating response with pagination info"
    );

    const tenantsResponse: TenantResponse[] = tenants.map((tenant) => ({
      id: tenant.id,
      name: tenant.name,
      code: tenant.code,
      subscriptionType: tenant.subscriptionType,
      maxActiveUsers: tenant.maxActiveUsers,
      currentActiveUsers: userCountMap.get(tenant.id) || 0,
      status: tenant.status,
    }));

    const response: TenantListResponse = {
      tenants: tenantsResponse,
      nextCursor,
      hasMore,
    };

    logger.info(
      {
        totalResults: tenantsResponse.length,
        hasMore,
        nextCursor: nextCursor ? "present" : "none",
      },
      "Tenant list query completed successfully"
    );

    return BaseResponse.success(response);
  }

  private buildWhereClause(payload: TenantListRequest): Prisma.TenantWhereInput {
    const where: Prisma.TenantWhereInput = {
      isDeleted: false, // Always exclude deleted tenants
    };

    if (!payload.filters) {
      return where;
    }

    const { filters } = payload;

    // Name filter - simple substring match
    if (filters.name) {
      where.name = { contains: filters.name, mode: "insensitive" };
    }

    // Code filter - simple substring match
    if (filters.code) {
      where.code = { contains: filters.code, mode: "insensitive" };
    }

    // Subscription type filter - IN operator
    if (filters.subscriptionType && filters.subscriptionType.length > 0) {
      where.subscriptionType = { in: filters.subscriptionType as any };
    }

    // Status filter - IN operator
    if (filters.status && filters.status.length > 0) {
      where.status = { in: filters.status as any };
    }

    return where;
  }

  private buildOrderByClause(payload: TenantListRequest): Prisma.TenantOrderByWithRelationInput[] {
    if (!payload.sort?.field) {
      return [{ createdAt: "desc" }]; // Default sort
    }
    const { field, direction = "asc" } = payload.sort;
    const dbField = field || "createdAt";
    return [{ [dbField]: direction }];
  }

  private buildCursorClause(
    cursor?: string,
    sort?: { field?: string; direction?: string }
  ): Prisma.TenantWhereInput {
    if (!cursor) {
      return {};
    }

    try {
      const decodedCursor = JSON.parse(Buffer.from(cursor, "base64").toString());
      const { field = "createdAt", direction = "desc" } = sort || {};
      const dbField = field || "createdAt";
      const cursorValue = decodedCursor.value;
      const cursorId = decodedCursor.id;

      // For cursor-based pagination, we need to handle the direction
      if (direction === "asc") {
        return {
          OR: [
            { [dbField]: { gt: cursorValue } },
            {
              AND: [
                { [dbField]: { equals: cursorValue } },
                { id: { gt: cursorId } }, // Use id as tiebreaker
              ],
            },
          ],
        };
      } else {
        return {
          OR: [
            { [dbField]: { lt: cursorValue } },
            {
              AND: [
                { [dbField]: { equals: cursorValue } },
                { id: { lt: cursorId } }, // Use id as tiebreaker
              ],
            },
          ],
        };
      }
    } catch (error) {
      // Invalid cursor, ignore it
      return {};
    }
  }

  private generateCursor(tenant: any, sort?: { field?: string; direction?: string }): string {
    const { field = "createdAt" } = sort || {};

    // Map field names to actual database fields
    const dbField = field || "createdAt";
    const cursorData = {
      value: tenant[dbField],
      id: tenant.id, // Include id as tiebreaker
    };

    return Buffer.from(JSON.stringify(cursorData)).toString("base64");
  }
}
