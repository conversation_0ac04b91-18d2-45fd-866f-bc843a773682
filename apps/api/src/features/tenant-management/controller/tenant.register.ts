import { appConfig } from "@/config";
import { TenantRegisterRequest } from "@/features/tenant-management/model/tenant-register.request";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { getEmailVerificationToken, signUpUsingEmailPassword } from "@/providers/auth.provider";
import { withTransaction } from "@/providers/db.provider";
import { sendTenantVerificationEmail } from "@/providers/email.provider";
import { logger } from "@/providers/logger.provider";
import { UserClaims } from "@/shared/model/auth.user-claims";
import { BaseResponse } from "@/shared/model/base.response";
import { Resource, Action } from "@/config/policy/rules";
import { UserRole } from "@repo/prisma/client";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Post, Res, UseBefore } from "routing-controllers";
import { TenantService } from "@/features/tenant-management/service/tenant.service";
@JsonController("/tenant/register")
@UseBefore(hasPermission(Resource.Tenant, [Action.Create]))
export class TenantRegisterController {
  @Post("/")
  public async process(
    @Body() payload: TenantRegisterRequest,
    @Res() res: Response
  ): Promise<Response> {
    logger.info(
      {
        tenantName: payload.name,
        adminEmail: payload.adminEmailId,
        subscriptionType: payload.subscriptionType,
        maxUsers: payload.maxUsers,
      },
      "Starting tenant registration process"
    );

    const result = await withTransaction(async (tx) => {
      return await TenantService.createTenant(payload, tx);
    });

    logger.info(
      {
        tenantId: result.tenant.id,
        tenantCode: result.tenant.code,
        tenantName: result.tenant.name,
      },
      "Tenant created successfully"
    );

    logger.info(
      {
        userId: result.user.id,
        userEmail: result.user.emailId,
        tenantId: result.tenant.id,
      },
      "Admin user created successfully"
    );

    logger.info(
      {
        userId: result.user.id,
        userEmail: result.user.emailId,
      },
      "Registering admin user in auth system"
    );

    const userClaims: UserClaims = {
      id: result.user.id,
      name: result.user.name,
      role: UserRole.TenantAdmin,
      displayName: result.user.displayName,
      email: result.user.emailId,
      status: result.user.status,
      tenant: {
        id: result.tenant.id,
        name: result.tenant.name,
        code: result.tenant.code,
        subscriptionType: result.tenant.subscriptionType,
        type: result.tenant.type,
        status: result.tenant.status,
      },
    };

    await signUpUsingEmailPassword(userClaims, crypto.randomUUID());

    logger.info(
      {
        userId: result.user.id,
        userEmail: result.user.emailId,
      },
      "Sending verification email to admin user"
    );

    // Get verification token and send email
    const token = await getEmailVerificationToken(payload.adminEmailId);
    const verificationLink = `${appConfig.auth.websiteDomain}/auth/verify-email?token=${encodeURIComponent(token)}&rid=emailverification`;
    sendTenantVerificationEmail(payload.adminEmailId, payload.adminName, verificationLink);

    logger.info(
      {
        tenantId: result.tenant.id,
        userId: result.user.id,
        userEmail: result.user.emailId,
        tenantCode: result.tenant.code,
      },
      "Tenant registration completed successfully"
    );

    return res.status(StatusCodes.OK).json(BaseResponse.ok());
  }
}
