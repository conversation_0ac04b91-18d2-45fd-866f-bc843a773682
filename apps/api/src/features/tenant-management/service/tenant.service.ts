import { dbClient, DatabaseClient } from "@/providers/db.provider";
import { Tenant, TenantStatus, TenantType, UserRole, User } from "@repo/prisma/client";
import { TenantRegisterRequest, TenantUpdateRequest } from "@/features/tenant-management/model";
import { RequestContextHelper } from "@/server/context";
export class TenantService {
  static async getTenantById(
    tenantId: string,
    includeUserData: boolean = false,
    prismaClient: DatabaseClient = dbClient()
  ) {
    const tenant = await prismaClient.tenant.findUnique({
      where: {
        id: tenantId,
        isDeleted: false,
      },
    });

    if (!includeUserData) {
      return {
        ...tenant,
        users: [],
      };
    }

    const users = await dbClient().user.findMany({
      where: {
        tenantId: tenantId,
        isDeleted: false,
      },
    });

    return {
      ...tenant,
      users: users,
    };
  }

  static async getActiveTenantById(
    tenantId: string,
    includeUserData: boolean = false,
    prismaClient: DatabaseClient = dbClient()
  ) {
    const tenant = await this.getTenantById(tenantId, includeUserData, prismaClient);
    if (tenant && tenant.status === TenantStatus.Active) {
      return tenant;
    }
  }

  static async createTenant(
    payload: TenantRegisterRequest,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<{ tenant: Tenant; user: User }> {
    let tenantCount = (await prismaClient.tenant.count()) || 0;
    ++tenantCount;
    const tenantCode = `PMS${tenantCount.toString().padStart(4, "0")}`;

    const createdTenant = await prismaClient.tenant.create({
      data: {
        name: payload.name,
        code: tenantCode,
        subscriptionType: payload.subscriptionType,
        maxActiveUsers: payload.maxUsers,
        type: TenantType.Customer,
        status: TenantStatus.Active,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });

    const createdUser = await prismaClient.user.create({
      data: {
        name: payload.adminName,
        displayName: payload.adminName,
        emailId: payload.adminEmailId,
        role: UserRole.TenantAdmin,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
        tenantId: createdTenant.id,
      },
    });

    return { tenant: createdTenant, user: createdUser };
  }

  static async updateTenant(
    payload: TenantUpdateRequest,
    prismaClient: DatabaseClient = dbClient()
  ) {
    const updatedTenant = await prismaClient.tenant.update({
      where: {
        id: payload.id,
        isDeleted: false,
      },
      data: {
        name: payload.name,
        subscriptionType: payload.subscriptionType,
        maxActiveUsers: payload.maxUsers,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });

    return updatedTenant;
  }

  static async updateTenantStatus(
    tenantId: string,
    status: TenantStatus,
    prismaClient: DatabaseClient = dbClient()
  ) {
    const updatedTenant = await prismaClient.tenant.update({
      where: {
        id: tenantId,
        isDeleted: false,
      },
      data: {
        status: status,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });

    return updatedTenant;
  }
}
