import { TenantSubscriptionType } from "@repo/prisma/client";
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsU<PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";

export class TenantUpdateRequest {
  @IsUUID()
  @IsNotEmpty()
  id!: string;

  @IsString()
  @IsOptional()
  name!: string;

  @IsNotEmpty()
  @IsOptional()
  subscriptionType!: TenantSubscriptionType;

  @IsNumber()
  @Min(1)
  @Max(100)
  @IsOptional()
  maxUsers!: number;
}
