import { TenantSubscriptionType } from "@repo/prisma/client";
import {
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPhoneNumber,
  IsString,
  <PERSON>,
  <PERSON>,
} from "class-validator";

export class TenantRegisterRequest {
  @IsString()
  @IsNotEmpty()
  name!: string;

  @IsString()
  @IsNotEmpty()
  adminName!: string;

  @IsEmail()
  @IsNotEmpty()
  adminEmailId!: string;

  @IsNotEmpty()
  subscriptionType!: TenantSubscriptionType;

  @IsNumber()
  @Min(1)
  @Max(100)
  maxUsers!: number;
}
