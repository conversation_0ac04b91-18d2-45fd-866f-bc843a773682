import { TenantStatus, TenantSubscriptionType } from "@repo/prisma/client";
import { Type } from "class-transformer";
import {
  IsArray,
  IsEnum,
  IsIn,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from "class-validator";

/**
 * User-specific filters
 */
export class TenantFilters {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  code?: string;

  @IsOptional()
  @IsArray()
  @IsEnum(TenantSubscriptionType, { each: true })
  subscriptionType?: TenantSubscriptionType[]; // Array = IN operator

  @IsOptional()
  @IsArray()
  @IsEnum(TenantStatus, { each: true })
  status?: TenantStatus[]; // Array = IN operator
}

/**
 * Sorting configuration
 */
export class TenantSort {
  @IsOptional()
  @IsString()
  @IsIn(["name", "code"])
  field?: string;

  @IsOptional()
  @IsString()
  @IsIn(["asc", "desc"])
  direction?: "asc" | "desc";
}

/**
 * User list request with pagination, filtering, and sorting
 */
export class TenantListRequest {
  @IsOptional()
  @ValidateNested()
  @Type(() => TenantFilters)
  filters?: TenantFilters;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 20;

  @IsOptional()
  @IsString()
  cursor?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => TenantSort)
  sort?: TenantSort;
}
