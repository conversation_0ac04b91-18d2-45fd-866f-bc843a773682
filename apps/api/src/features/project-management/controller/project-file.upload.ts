import { hasPermission } from "@/middleware/has-permission.middleware";
import { BaseResponse } from "@/shared/model/base.response";
import { BaseError } from "@/exception-handling/base.error";
import { RequestContextHelper } from "@/server/context";
import type { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { JsonController, Post, Param, Req, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { v4 as uuidv4 } from "uuid";
import { fileService } from "@/shared/service/file.service";
import { ProjectService } from "../service/project.service";
import { logger } from "@/providers/logger.provider";
import { FileUploadResponse } from "../model/file-upload.response";
import { withTransaction } from "@/providers/db.provider";
import { ProjectStatus } from "@repo/prisma/client";
@JsonController("/projects/:projectId/upload")
@UseBefore(hasPermission(Resource.Project, [Action.Create]))
export class ProjectFileUploadController {
  @Post("/")
  public async process(
    @Param("projectId") projectId: string,
    @Req() req: Request,
    @Res() res: Response
  ): Promise<BaseResponse<FileUploadResponse>> {
    logger.info(
      {
        projectId,
        method: req.method,
        url: req.url,
        path: req.path,
        contentType: req.headers["content-type"],
      },
      "🚀 File upload controller called"
    );

    // Validate content-type
    const contentType = req.headers["content-type"];
    if (!contentType || !contentType.includes("multipart/form-data")) {
      logger.error(
        {
          projectId,
          contentType,
          allHeaders: req.headers,
        },
        "Invalid content type for file upload"
      );
      throw new BaseError(
        "INVALID_REQUEST",
        "Content-Type must be multipart/form-data",
        StatusCodes.BAD_REQUEST,
        {}
      );
    }

    const fileId = uuidv4();
    const pathPrefix =
      RequestContextHelper.getTenant()!.id + "/projects/" + projectId + "/files/" + fileId;

    logger.info(
      {
        projectId,
        fileId,
        pathPrefix,
        tenantId: RequestContextHelper.getTenant()!.id,
      },
      "Starting file upload with fileService.streamUpload"
    );

    try {
      const project = await ProjectService.getById(projectId);
      if (!project || project.status === ProjectStatus.Completed) {
        logger.error(
          {
            projectId,
          },
          "Project not found for file upload. Or project is completed."
        );
        throw new BaseError("PROJECT_NOT_FOUND", "Project not found", StatusCodes.NOT_FOUND, {});
      }

      const result = await fileService.streamUpload(req, pathPrefix, ["application/pdf"]);

      logger.info(
        {
          projectId,
          fileId,
          result,
        },
        "File upload successful, saving to database"
      );

      const dbResponse = await withTransaction(async (tx) => {
        if (project.status === ProjectStatus.New) {
          logger.info(
            {
              projectId,
            },
            "Project is updated to ongoing"
          );
          await ProjectService.updateStatus(projectId, ProjectStatus.OnGoing, tx);
        }

        const projectFile = await ProjectService.createFileEntry(
          projectId,
          {
            id: fileId,
            name: result.metadata.originalFileName,
            rootFolderPath: pathPrefix,
            originalFilePath: result.metadata.path,
            objectStoragePath: result.blob.blobName,
            size: result.blob.contentLength!,
            batchNumber: Number(result.metadata.batchNumber),
            contentType: result.metadata.contentType,
          },
          tx
        );

        return { file: projectFile };
      });

      logger.info(
        {
          projectId,
          fileId,
          dbResponse,
        },
        "File upload and database entry successful"
      );

      const fileUploadResponse: FileUploadResponse = {
        id: dbResponse.file.id,
        name: dbResponse.file.name,
        createdAt: dbResponse.file.createdAt,
        size: dbResponse.file.size,
        path: dbResponse.file.originalFilePath,
        status: dbResponse.file.status,
      };

      return BaseResponse.success(fileUploadResponse);
    } catch (error) {
      logger.error(error, "Error uploading file");

      if (error instanceof BaseError) {
        throw error;
      }

      throw new BaseError(
        "FILE_UPLOAD_FAILED",
        error instanceof Error ? error.message : "Unknown error",
        StatusCodes.INTERNAL_SERVER_ERROR,
        {}
      );
    }
  }
}
