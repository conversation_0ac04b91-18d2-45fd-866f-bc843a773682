import { ProjectListRequest } from "@/features/project-management/model/project-list.request";
import { ProjectListResponse } from "@/features/project-management/model/project-list.response";
import { dbClient } from "@/providers/db.provider";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import { Prisma } from "@repo/prisma/client";
import { Body, JsonController, Post, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { ProjectCategoryBase } from "../model/project.base";

@JsonController("/projects/find")
@UseBefore(hasPermission(Resource.Project, [Action.Read]))
export class ProjectListController {
  @Post("/")
  public async process(
    @Body() payload: ProjectListRequest
  ): Promise<BaseResponse<ProjectListResponse>> {
    logger.info(
      {
        filters: payload.filters,
        sort: payload.sort,
        limit: payload.limit,
        cursor: payload.cursor ? "present" : "none",
      },
      "Starting project list query"
    );

    // Build where clause from filters
    const whereClause = this.buildWhereClause(payload);

    // Build order by clause from sort
    const orderBy = this.buildOrderByClause(payload);

    // Build cursor-based pagination
    const cursorWhere = this.buildCursorClause(payload.cursor, payload.sort);

    // Set pagination parameters
    const limit = payload.limit!;
    const take = limit + 1; // Take one extra to check if there are more results

    logger.info(
      {
        whereClause,
        orderBy,
        cursorWhere,
        limit,
        take,
      },
      "Built query parameters for project listing"
    );

    // Tenant filter for security
    const tenantWhere: Prisma.ProjectWhereInput = {
      tenantId: RequestContextHelper.getTenant()!.id!,
      isDeleted: false,
    };

    // Combine where clauses
    const finalWhere: Prisma.ProjectWhereInput = {
      AND: [tenantWhere, whereClause, cursorWhere].filter(
        (clause) => Object.keys(clause).length > 0
      ),
    };

    logger.info(
      {
        finalWhere,
      },
      "Executing project query"
    );

    const projects = await dbClient().project.findMany({
      where: finalWhere,
      orderBy,
      take,
      include: {
        categories: {
          where: { isDeleted: false },
          include: {
            category: {
              include: {
                glossary: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
        users: {
          include: {
            user: {
              select: {
                name: true,
                role: true,
              },
            },
          },
        },
        _count: {
          select: {
            files: {
              where: {
                isDeleted: false,
              },
            },
          },
        },
      },
    });

    logger.info(
      {
        projectCount: projects.length,
        hasMore: projects.length > limit,
      },
      "Project query completed"
    );

    // Check if there are more results
    const hasMore = projects.length > limit;
    if (hasMore) {
      projects.pop(); // Remove the extra record
    }

    // Generate next cursor based on sort field
    const nextCursor =
      hasMore && projects.length > 0
        ? this.generateCursor(projects[projects.length - 1]!, payload.sort)
        : undefined;

    logger.info(
      {
        hasMore,
        nextCursor,
        resultCount: projects.length,
      },
      "Generating response with pagination info"
    );

    const responseProjects = projects.map((project) => {
      // There is always 1 project handler assigned to a project
      const assignee = {
        id: project.users[0]!.userId,
        name: project.users[0]!.user.name,
        role: project.users[0]!.user.role,
      };

      const categories: ProjectCategoryBase[] = Object.values(
        project.categories.reduce(
          (acc, pc) => {
            const categoryId = pc.glossaryCategoryId;

            if (!acc[categoryId]) {
              acc[categoryId] = {
                glossaryCategoryId: categoryId,
                glossaryCategoryName: pc.category.glossary.name,
                items: [],
              };
            }

            acc[categoryId].items.push({
              glossaryItemId: pc.glossaryItemId,
              glossaryItemName: pc.category.name,
            });

            return acc;
          },
          {} as Record<string, ProjectCategoryBase>
        )
      );

      return {
        id: project.id,
        code: project.code,
        vessel: project.vessel,
        assignee: assignee,
        categories: categories,
        hullNumber: project.hullNumber,
        imoNumber: project.imoNumber,
        vesselDeliveryDate: project.vesselDeliveryDate,
        status: project.status,
        fileCount: project._count.files,
        createdAt: project.createdAt,
        updatedAt: project.updatedAt,
      };
    });

    const response = {
      projects: responseProjects,
      nextCursor,
      hasMore,
    };

    return BaseResponse.success(response);
  }

  private buildWhereClause(payload: ProjectListRequest): Prisma.ProjectWhereInput {
    const where: Prisma.ProjectWhereInput = {};

    if (payload.filters?.code) {
      where.code = {
        contains: payload.filters.code,
        mode: "insensitive",
      };
    }

    if (payload.filters?.vessel) {
      where.vessel = {
        contains: payload.filters.vessel,
        mode: "insensitive",
      };
    }

    if (payload.filters?.status && payload.filters.status.length > 0) {
      where.status = {
        in: payload.filters.status,
      };
    }

    if (payload.filters?.projectHandler) {
      where.users = {
        some: {
          userId: payload.filters.projectHandler,
        },
      };
    }

    if (payload.filters?.vesselType) {
      where.categories = {
        some: {
          glossaryItemId: payload.filters.vesselType,
        },
      };
    }

    return where;
  }

  private buildOrderByClause(
    payload: ProjectListRequest
  ): Prisma.ProjectOrderByWithRelationInput[] {
    const orderBy: Prisma.ProjectOrderByWithRelationInput[] = [];

    if (payload.sort?.field && payload.sort?.direction) {
      const direction = payload.sort.direction;

      switch (payload.sort.field) {
        case "vessel":
          orderBy.push({ vessel: direction });
          break;
        case "code":
          orderBy.push({ code: direction });
          break;
        case "createdAt":
          orderBy.push({ createdAt: direction });
          break;
        case "updatedAt":
          orderBy.push({ updatedAt: direction });
          break;
      }
    } else {
      // Default sort by updatedAt desc
      orderBy.push({ updatedAt: "desc" });
    }

    // Always add id as tiebreaker for stable pagination
    orderBy.push({ id: "desc" });

    return orderBy;
  }

  private buildCursorClause(cursor: string | undefined, sort: any): Prisma.ProjectWhereInput {
    if (!cursor) {
      return {};
    }

    try {
      const decodedCursor = JSON.parse(Buffer.from(cursor, "base64").toString("utf-8"));
      const { value, id } = decodedCursor;

      const sortField = sort?.field || "updatedAt";
      const sortDirection = sort?.direction || "desc";

      if (sortDirection === "desc") {
        return {
          OR: [{ [sortField]: { lt: value } }, { [sortField]: value, id: { lt: id } }],
        };
      } else {
        return {
          OR: [{ [sortField]: { gt: value } }, { [sortField]: value, id: { gt: id } }],
        };
      }
    } catch (error) {
      logger.warn({ cursor, error }, "Invalid cursor provided, ignoring");
      return {};
    }
  }

  private generateCursor(project: any, sort: any): string {
    const sortField = sort?.field || "updatedAt";
    const value = project[sortField];
    const id = project.id;

    const cursorData = { value, id };
    return Buffer.from(JSON.stringify(cursorData)).toString("base64");
  }
}
