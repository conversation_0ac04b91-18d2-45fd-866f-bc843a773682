import { hasPermission } from "@/middleware/has-permission.middleware";
import { BaseResponse } from "@/shared/model/base.response";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Param, Patch, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { ProjectService } from "@/features/project-management/service/project.service";
import { BaseError } from "@/exception-handling/base.error";
import { UserService } from "@/features/user-management/service/user.service";
import { ProjectUpdateRequest } from "@/features/project-management/model/project-update.request";
import { logger } from "@/providers/logger.provider";
import { ProjectStatus, UserRole } from "@/types-export";
import { RequestContextHelper } from "@/server/context";
import { withTransaction } from "@/providers/db.provider";

@JsonController("/project/:id")
@UseBefore(hasPermission(Resource.Project, [Action.Update]))
export class ProjectUpdateController {
  @Patch("/")
  public async process(
    @Param("id") projectId: string,
    @Body() payload: ProjectUpdateRequest,
    @Res() res: Response
  ): Promise<Response> {
    const projectDetails = await ProjectService.getById(projectId);

    if (!projectDetails) {
      logger.error(
        {
          projectId,
        },
        "Project not found for update"
      );
      throw new BaseError("PROJECT_NOT_FOUND", "Project not found", StatusCodes.NOT_FOUND, {});
    }

    if (projectDetails.status === ProjectStatus.Completed) {
      logger.error(
        {
          projectId,
        },
        "Project is completed. Cannot update"
      );
      throw new BaseError("PROJECT_COMPLETED", "Project is completed", StatusCodes.BAD_REQUEST, {});
    }

    const user = await UserService.getActiveUserById(payload.assignee.id);
    if (
      !user ||
      user.tenant.id !== RequestContextHelper.getTenant()!.id! ||
      user.role !== UserRole.ProjectHandler
    ) {
      logger.error(
        {
          userId: payload.assignee.id,
        },
        "User not found for project creation"
      );
      throw new BaseError("USER_NOT_FOUND", "User not found", StatusCodes.NOT_FOUND, {});
    }

    const result = await withTransaction(async (tx) => {
      return await ProjectService.update(projectId, payload, tx);
    });

    return res.status(StatusCodes.CREATED).json(BaseResponse.ok());
  }
}
