import { hasPermission } from "@/middleware/has-permission.middleware";
import { BaseResponse } from "@/shared/model/base.response";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Post, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { ProjectCreateRequest } from "@/features/project-management/model/project-create.request";
import { ProjectService } from "@/features/project-management/service/project.service";
import { BaseError } from "@/exception-handling/base.error";
import { UserService } from "@/features/user-management/service/user.service";
import { RequestContextHelper } from "@/server/context";
import { logger } from "@/providers/logger.provider";
import { UserRole } from "@repo/prisma/client";
@JsonController("/project")
@UseBefore(hasPermission(Resource.Project, [Action.Create]))
export class ProjectCreateController {
  @Post("/")
  public async process(
    @Body() payload: ProjectCreateRequest,
    @Res() res: Response
  ): Promise<Response> {
    logger.info(
      {
        payload: payload.assignee.id,
      },
      "Creating project"
    );

    const user = await UserService.getActiveUserById(payload.assignee.id);
    if (
      !user ||
      user.tenant.id !== RequestContextHelper.getTenant()!.id! ||
      user.role !== UserRole.ProjectHandler
    ) {
      logger.error(
        {
          userId: payload.assignee.id,
        },
        "User not found for project creation"
      );
      throw new BaseError("USER_NOT_FOUND", "User not found", StatusCodes.NOT_FOUND, {});
    }

    const result = await ProjectService.create(payload);

    return res.status(StatusCodes.CREATED).json(BaseResponse.ok());
  }
}
