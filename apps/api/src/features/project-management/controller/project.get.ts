import { dbClient } from "@/providers/db.provider";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import { ProjectFileStatus } from "@repo/prisma/client";
import { Get, JsonController, Param, Params, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { ProjectCategoryBase } from "../model/project.base";
import { ProjectGetResponse } from "../model/project-get.response";
import { BaseError } from "@/exception-handling/base.error";
import { StatusCodes } from "http-status-codes";
import { IdParam } from "@/types-export";

@JsonController("/projects/:id")
@UseBefore(hasPermission(Resource.Project, [Action.Read]))
export class ProjectGetController {
  @Get("/")
  public async process(@Params() params: IdParam): Promise<BaseResponse<ProjectGetResponse>> {
    const projectId = params.id;

    logger.info({ projectId }, "Getting project details");

    try {
      // Get project with all related data
      const project = await dbClient().project.findUnique({
        where: {
          id: projectId,
          tenantId: RequestContextHelper.getTenant()!.id!,
          isDeleted: false,
        },
        include: {
          categories: {
            where: { isDeleted: false },
            include: {
              category: {
                include: {
                  glossary: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
          users: {
            where: { isDeleted: false },
            include: {
              user: {
                select: {
                  name: true,
                  role: true,
                },
              },
            },
          },
          _count: {
            select: {
              files: {
                where: {
                  isDeleted: false,
                },
              },
            },
          },
        },
      });

      if (!project) {
        throw new BaseError("PROJECT_NOT_FOUND", "Project not found", StatusCodes.NOT_FOUND, {});
      }

      // Get file count by status
      const fileCountByStatus = await dbClient().projectFile.groupBy({
        by: ["status"],
        where: {
          projectId: projectId,
          isDeleted: false,
          tenantId: RequestContextHelper.getTenant()!.id!,
        },
        _count: {
          status: true,
        },
      });

      // Create fileCountMap with all statuses initialized to 0
      const fileCountMap: { [key in ProjectFileStatus]: number } = {
        [ProjectFileStatus.Cancelled]: 0,
        [ProjectFileStatus.Uploaded]: 0,
        [ProjectFileStatus.Classifying]: 0,
        [ProjectFileStatus.Classified]: 0,
        [ProjectFileStatus.Marking]: 0,
        [ProjectFileStatus.Marked]: 0,
        [ProjectFileStatus.Extracting]: 0,
        [ProjectFileStatus.Extracted]: 0,
      };

      // Fill in actual counts
      fileCountByStatus.forEach((statusCount) => {
        fileCountMap[statusCount.status] = statusCount._count.status;
      });

      // Get assignee (project handler) - there should always be one
      if (!project.users || project.users.length === 0) {
        throw new BaseError(
          "PROJECT_ASSIGNEE_NOT_FOUND",
          "Project assignee not found",
          StatusCodes.INTERNAL_SERVER_ERROR,
          {}
        );
      }

      const assignee = {
        id: project.users[0]!.userId,
        name: project.users[0]!.user.name,
        role: project.users[0]!.user.role,
      };

      // Transform categories to match ProjectCategoryBase structure
      const categories: ProjectCategoryBase[] = Object.values(
        project.categories.reduce(
          (acc, pc) => {
            const categoryId = pc.glossaryCategoryId;

            if (!acc[categoryId]) {
              acc[categoryId] = {
                glossaryCategoryId: categoryId,
                glossaryCategoryName: pc.category.glossary.name,
                items: [],
              };
            }

            acc[categoryId].items.push({
              glossaryItemId: pc.glossaryItemId,
              glossaryItemName: pc.category.name,
            });

            return acc;
          },
          {} as Record<string, ProjectCategoryBase>
        )
      );

      const response: ProjectGetResponse = {
        id: project.id,
        code: project.code,
        vessel: project.vessel,
        assignee: assignee,
        categories: categories,
        hullNumber: project.hullNumber,
        imoNumber: project.imoNumber,
        vesselDeliveryDate: project.vesselDeliveryDate,
        status: project.status,
        fileCount: project._count.files,
        fileCountMap: fileCountMap,
        createdAt: project.createdAt,
        updatedAt: project.updatedAt,
      };

      logger.info(
        {
          projectId,
          fileCount: project._count.files,
          categoriesCount: categories.length,
        },
        "Project details retrieved successfully"
      );

      return BaseResponse.success(response);
    } catch (error) {
      logger.error({ projectId, error }, "Error getting project details");

      if (error instanceof BaseError) {
        throw error;
      }

      throw new BaseError(
        "PROJECT_GET_FAILED",
        "Failed to get project details",
        StatusCodes.INTERNAL_SERVER_ERROR,
        {}
      );
    }
  }
}
