import { hasPermission } from "@/middleware/has-permission.middleware";
import { Action, BaseResponse, IdParam, Resource } from "@/types-export";
import { Body, JsonController, Params, Post, UseBefore } from "routing-controllers";
import { ProjectFileStateUpdateRequest } from "../model/file-state.request";
import { StatusCodes } from "http-status-codes";
import { ProjectService } from "../service/project.service";
import { RequestContextHelper } from "@/server/context";
import { logger } from "@/providers/logger.provider";
import { BaseError } from "@/exception-handling/base.error";
import {
  ProjectFileStatus,
  ProjectFileSubStatus,
  FileState,
  FileStateStatus,
  UserRole,
} from "@repo/prisma/client";
import { withTransaction } from "@/providers/db.provider";

@JsonController("/projects/:id/classify")
@UseBefore(hasPermission(Resource.Project, [Action.Update]))
export class ProjectFileClassifyController {
  @Post("/")
  public async process(
    @Params() params: IdParam,
    @Body() payload: ProjectFileStateUpdateRequest
  ): Promise<BaseResponse> {
    const projectId = params.id;
    const currentUser = RequestContextHelper.getUser()!;

    logger.info(
      { projectId, fileIds: payload.fileIds, userId: currentUser.id },
      "Starting file classification process"
    );

    try {
      // 1. Validate project exists
      const project = await ProjectService.getById(projectId);
      if (!project) {
        throw new BaseError("PROJECT_NOT_FOUND", "Project not found", StatusCodes.NOT_FOUND, {});
      }

      // 2. Check user permissions - project handler can only classify their assigned projects, tenant admin can classify any project
      if (currentUser.role === UserRole.ProjectHandler) {
        const isProjectHandler = await ProjectService.isUserProjectHandler(
          projectId,
          currentUser.id
        );
        if (!isProjectHandler) {
          throw new BaseError(
            "ACCESS_FORBIDDEN",
            "Only assigned project handler can classify files for this project",
            StatusCodes.FORBIDDEN,
            {}
          );
        }
      }
      // TenantAdmin can classify any project (already validated by hasPermission middleware)

      // 3. Validate files exist and are in uploaded state
      const files = await ProjectService.getFilesByIds(projectId, payload.fileIds);

      if (files.length !== payload.fileIds.length) {
        const foundFileIds = files.map((f) => f.id);
        const missingFileIds = payload.fileIds.filter((id) => !foundFileIds.includes(id));
        throw new BaseError("FILES_NOT_FOUND", "Some files not found", StatusCodes.NOT_FOUND, {
          missingFileIds,
        });
      }

      // Check if all files are in uploaded state
      const nonUploadedFiles = files.filter((file) => file.status !== ProjectFileStatus.Uploaded);
      if (nonUploadedFiles.length > 0) {
        throw new BaseError(
          "INVALID_FILE_STATUS",
          "Files must be in uploaded state to classify",
          StatusCodes.BAD_REQUEST,
          {
            invalidFiles: nonUploadedFiles.map((f) => ({
              id: f.id,
              name: f.name,
              currentStatus: f.status,
            })),
          }
        );
      }

      // TODO Push to queue

      // Process files in transaction
      await withTransaction(async (tx) => {
        for (const file of files) {
          const maxRunData = await tx.projectFileStateHistory.aggregate({
            where: {
              tenantId: RequestContextHelper.getTenant()!.id,
              fileId: file.id,
              state: FileState.Classification,
              isDeleted: false,
            },
            _max: {
              runId: true,
            },
          });
          // since the automation starts at classification, we need to generate a new run id.
          // We need to take the max run id and increment it by 1
          const runId = maxRunData._max.runId ?? 0 + 1;

          // 4. Make entry in project_file_state_history with state as classification and status as queued
          await ProjectService.createFileStateHistory(
            file.id,
            runId,
            FileState.Classification,
            FileStateStatus.Queued,
            null,
            tx
          );

          // 5. Update project_file with status as classifying and sub status as in progress
          await ProjectService.updateFileStatus(
            file.id,
            ProjectFileStatus.Classifying,
            ProjectFileSubStatus.InProgress,
            tx
          );
        }
      });

      logger.info(
        {
          projectId,
          fileIds: payload.fileIds,
          userId: currentUser.id,
          filesProcessed: files.length,
        },
        "File classification initiated successfully"
      );

      // 6. Return success
      return BaseResponse.success({
        message: "File classification initiated successfully",
        filesProcessed: files.length,
        projectId: projectId,
      });
    } catch (error) {
      logger.error(
        {
          projectId,
          fileIds: payload.fileIds,
          userId: currentUser.id,
          error,
        },
        "Error initiating file classification"
      );

      if (error instanceof BaseError) {
        throw error;
      }

      throw new BaseError(
        "CLASSIFICATION_FAILED",
        "Failed to initiate file classification",
        StatusCodes.INTERNAL_SERVER_ERROR,
        {}
      );
    }
  }
}
