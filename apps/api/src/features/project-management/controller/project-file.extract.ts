import { hasPermission } from "@/middleware/has-permission.middleware";
import { Action, BaseResponse, IdParam, Resource } from "@/types-export";
import { Body, JsonController, Params, Post, UseBefore } from "routing-controllers";
import { ProjectFileStateUpdateRequest } from "../model/file-state.request";
import { StatusCodes } from "http-status-codes";
import { ProjectService } from "../service/project.service";
import { RequestContextHelper } from "@/server/context";
import { logger } from "@/providers/logger.provider";
import { BaseError } from "@/exception-handling/base.error";
import {
  ProjectFileStatus,
  ProjectFileSubStatus,
  FileState,
  FileStateStatus,
  UserRole,
} from "@repo/prisma/client";
import { withTransaction } from "@/providers/db.provider";

@JsonController("/projects/:id/extract")
@UseBefore(hasPermission(Resource.Project, [Action.Update]))
export class ProjectFileExtractController {
  @Post("/")
  public async process(
    @Params() params: IdParam,
    @Body() payload: ProjectFileStateUpdateRequest
  ): Promise<BaseResponse> {
    const projectId = params.id;
    const currentUser = RequestContextHelper.getUser()!;

    logger.info(
      { projectId, fileIds: payload.fileIds, userId: currentUser.id },
      "Starting file extraction process"
    );

    try {
      const project = await ProjectService.getById(projectId);
      if (!project) {
        throw new BaseError("PROJECT_NOT_FOUND", "Project not found", StatusCodes.NOT_FOUND, {});
      }

      if (currentUser.role === UserRole.ProjectHandler) {
        const isProjectHandler = await ProjectService.isUserProjectHandler(
          projectId,
          currentUser.id
        );
        if (!isProjectHandler) {
          throw new BaseError(
            "ACCESS_FORBIDDEN",
            "Only assigned project handler can extract files for this project",
            StatusCodes.FORBIDDEN,
            {}
          );
        }
      }

      const files = await ProjectService.getFilesByIds(projectId, payload.fileIds);

      if (files.length !== payload.fileIds.length) {
        const foundFileIds = files.map((f) => f.id);
        const missingFileIds = payload.fileIds.filter((id) => !foundFileIds.includes(id));
        throw new BaseError("FILES_NOT_FOUND", "Some files not found", StatusCodes.NOT_FOUND, {
          missingFileIds,
        });
      }

      // Check if all files are in marked state and sub status is completed
      const markedFiles = files.filter(
        (file) =>
          file.status === ProjectFileStatus.Marked &&
          file.subStatus === ProjectFileSubStatus.Completed
      );
      if (files.length !== markedFiles.length) {
        throw new BaseError(
          "INVALID_FILE_STATUS",
          "All files must be in marked and completed state to extract",
          StatusCodes.BAD_REQUEST,
          {}
        );
      }

      // TODO Push to queue

      // Process files in transaction
      await withTransaction(async (tx) => {
        for (const file of files) {
          const maxRunData = await tx.projectFileStateHistory.aggregate({
            where: {
              tenantId: RequestContextHelper.getTenant()!.id,
              fileId: file.id,
              state: FileState.Mark,
              status: FileStateStatus.Completed,
              isDeleted: false,
            },
            _max: {
              runId: true,
            },
          });

          if (
            !maxRunData ||
            maxRunData._max.runId === null ||
            maxRunData._max.runId === undefined ||
            maxRunData._max.runId === 0
          ) {
            throw new BaseError(
              "INVALID_FILE_STATUS",
              "File has not been marked",
              StatusCodes.BAD_REQUEST,
              {
                invalidFiles: [file.id],
              }
            );
          }

          // use the same runId as mark as marking and extraction are part of the same job
          const runId = maxRunData._max.runId;

          await ProjectService.createFileStateHistory(
            file.id,
            runId,
            FileState.Extract,
            FileStateStatus.Queued,
            null,
            tx
          );

          await ProjectService.updateFileStatus(
            file.id,
            ProjectFileStatus.Extracting,
            ProjectFileSubStatus.InProgress,
            tx
          );
        }
      });

      logger.info(
        {
          projectId,
          fileIds: payload.fileIds,
          userId: currentUser.id,
          filesProcessed: files.length,
        },
        "File extraction initiated successfully"
      );

      // 6. Return success
      return BaseResponse.success({
        message: "File extraction initiated successfully",
        filesProcessed: files.length,
        projectId: projectId,
      });
    } catch (error) {
      logger.error(
        {
          projectId,
          fileIds: payload.fileIds,
          userId: currentUser.id,
          error,
        },
        "Error initiating file extraction"
      );

      if (error instanceof BaseError) {
        throw error;
      }

      throw new BaseError(
        "EXTRACTION_FAILED",
        "Failed to initiate file extraction",
        StatusCodes.INTERNAL_SERVER_ERROR,
        {}
      );
    }
  }
}
