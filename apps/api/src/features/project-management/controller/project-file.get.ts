import { hasPermission } from "@/middleware/has-permission.middleware";
import { BaseError } from "@/exception-handling/base.error";
import type { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { JsonController, Param, Req, Res, UseBefore, Get } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { ProjectService } from "../service/project.service";
import { logger } from "@/providers/logger.provider";
import { blobProvider } from "@/providers/blob.provider";
@JsonController("/projects/:projectId/files/:fileId")
@UseBefore(hasPermission(Resource.Project, [Action.Read]))
export class ProjectFileGetController {
  @Get("/")
  public async process(
    @Param("projectId") projectId: string,
    @Param("fileId") fileId: string,
    @Req() req: Request,
    @Res() res: Response
  ): Promise<Response> {
    logger.info(
      {
        projectId,
        method: req.method,
        url: req.url,
        path: req.path,
        contentType: req.headers["content-type"],
      },
      "🚀 File download controller called"
    );
    const file = await ProjectService.getFileById(projectId, fileId);
    if (!file) {
      throw new BaseError("FILE_NOT_FOUND", "File not found", StatusCodes.NOT_FOUND, {});
    }

    try {
      const downloadStream = await blobProvider.downloadStream(file.objectStoragePath);

      // Set proper headers for file download
      res.setHeader("Content-Type", file.contentType);
      res.setHeader("Content-Disposition", `inline; filename="${file.name}"`);
      res.setHeader("Content-Length", file.size.toString());
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Accept-Ranges", "bytes");

      logger.info(
        {
          projectId,
          fileId,
          fileName: file.name,
          contentType: file.contentType,
          fileSize: file.size,
        },
        "Starting file download stream"
      );

      // Return a promise that resolves when the stream is done
      return new Promise((resolve, reject) => {
        let bytesTransferred = 0;

        downloadStream.on("data", (chunk: any) => {
          bytesTransferred += chunk.length;
        });

        downloadStream.on("end", () => {
          logger.info(
            {
              projectId,
              fileId,
              bytesTransferred,
              expectedSize: file.size,
            },
            "File download completed successfully"
          );
          resolve(res);
        });

        downloadStream.on("error", (error: any) => {
          logger.error(
            { projectId, fileId, error, bytesTransferred },
            "File download stream error"
          );
          if (!res.headersSent) {
            res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
              success: false,
              message: "Error downloading file",
            });
          }
          reject(error);
        });

        res.on("error", (error: any) => {
          logger.error({ projectId, fileId, error, bytesTransferred }, "Response stream error");
          reject(error);
        });

        res.on("close", () => {
          logger.info({ projectId, fileId, bytesTransferred }, "Response connection closed");
        });

        // Pipe the stream to response
        downloadStream.pipe(res);
      });
    } catch (error) {
      logger.error({ projectId, fileId, error }, "Error setting up file download");
      throw new BaseError(
        "FILE_DOWNLOAD_ERROR",
        "Error downloading file",
        StatusCodes.INTERNAL_SERVER_ERROR,
        {}
      );
    }
  }
}
