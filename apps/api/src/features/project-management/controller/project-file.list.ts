import { BaseResponse } from "@/shared/model/base.response";
import { Body, JsonController, Param, Post, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { ProjectFileListRequest } from "../model/project-file-list.request";
import { ProjectFileListResponse, ProjectFileResponse } from "../model/project-file-list.response";
import { ProjectService } from "../service/project.service";
import { logger } from "@/providers/logger.provider";
import { BaseError } from "@/exception-handling/base.error";
import { StatusCodes } from "http-status-codes";
import { dbClient } from "@/providers/db.provider";
import { RequestContextHelper } from "@/server/context";
import { Prisma } from "@repo/prisma/client";

@JsonController("/projects/:id/files/find")
@UseBefore(hasPermission(Resource.Project, [Action.Read]))
export class ProjectFileListController {
  @Post("/")
  public async process(
    @Param("id") projectId: string,
    @Body() payload: ProjectFileListRequest
  ): Promise<BaseResponse<ProjectFileListResponse>> {
    logger.info(
      {
        projectId,
        filters: payload.filters,
        sort: payload.sort,
        limit: payload.limit,
        cursor: payload.cursor ? "present" : "none",
      },
      "Starting project file list query"
    );

    // Validate project exists and user has access
    const project = await ProjectService.getById(projectId);
    if (!project) {
      throw new BaseError(
        "PROJECT_NOT_FOUND",
        "Project not found or access denied",
        StatusCodes.NOT_FOUND,
        {}
      );
    }

    // Build where clause from filters
    const whereClause = this.buildWhereClause(payload);

    // Build order by clause from sort
    const orderBy = this.buildOrderByClause(payload);

    // Build cursor-based pagination
    const cursorWhere = this.buildCursorClause(payload.cursor, payload.sort);

    // Set pagination parameters
    const limit = payload.limit!;
    const take = limit + 1; // Take one extra to check if there are more results

    logger.info(
      {
        whereClause,
        orderBy,
        cursorWhere,
        limit,
        take,
      },
      "Built query parameters for project file listing"
    );

    // Tenant and project filter for security
    const tenantWhere: Prisma.ProjectFileWhereInput = {
      projectId: projectId,
      tenantId: RequestContextHelper.getTenant()!.id!,
      isDeleted: false,
    };

    // Combine where clauses
    const finalWhere: Prisma.ProjectFileWhereInput = {
      AND: [tenantWhere, whereClause, cursorWhere].filter(
        (clause) => Object.keys(clause).length > 0
      ),
    };

    logger.info(
      {
        finalWhere,
      },
      "Executing project file query"
    );

    const files = await dbClient().projectFile.findMany({
      where: finalWhere,
      orderBy,
      take,
    });

    logger.info(
      {
        fileCount: files.length,
        hasMore: files.length > limit,
      },
      "Project file query completed"
    );

    // Check if there are more results
    const hasMore = files.length > limit;
    if (hasMore) {
      files.pop(); // Remove the extra record
    }

    // Generate next cursor based on sort field
    const nextCursor =
      hasMore && files.length > 0
        ? this.generateCursor(files[files.length - 1]!, payload.sort)
        : undefined;

    logger.info(
      {
        hasMore,
        nextCursor,
        resultCount: files.length,
      },
      "Generating response with pagination info"
    );

    const responseFiles: ProjectFileResponse[] = files.map((file) => ({
      id: file.id,
      name: file.name,
      createdAt: file.createdAt,
      size: file.size,
      path: file.originalFilePath,
      batchNumber: file.batchNumber,
      status: file.status,
      subStatus: file.subStatus,
    }));

    const response: ProjectFileListResponse = {
      files: responseFiles,
      nextCursor,
      hasMore,
    };

    return BaseResponse.success(response);
  }

  private buildWhereClause(payload: ProjectFileListRequest): Prisma.ProjectFileWhereInput {
    const where: Prisma.ProjectFileWhereInput = {};

    if (payload.filters?.name) {
      where.name = {
        contains: payload.filters.name,
        mode: "insensitive",
      };
    }

    if (payload.filters?.path) {
      where.originalFilePath = {
        contains: payload.filters.path,
        mode: "insensitive",
      };
    }

    if (payload.filters?.status && payload.filters.status.length > 0) {
      where.status = {
        in: payload.filters.status,
      };
    }

    return where;
  }

  private buildOrderByClause(
    payload: ProjectFileListRequest
  ): Prisma.ProjectFileOrderByWithRelationInput[] {
    const orderBy: Prisma.ProjectFileOrderByWithRelationInput[] = [];

    if (payload.sort?.field && payload.sort?.direction) {
      const direction = payload.sort.direction;

      switch (payload.sort.field) {
        case "name":
          orderBy.push({ name: direction });
          break;
        case "size":
          orderBy.push({ size: direction });
          break;
        case "createdAt":
          orderBy.push({ createdAt: direction });
          break;
      }
    } else {
      // Default sort by createdAt desc
      orderBy.push({ createdAt: "desc" });
    }

    // Always add id as tiebreaker for stable pagination
    orderBy.push({ id: "desc" });

    return orderBy;
  }

  private buildCursorClause(cursor: string | undefined, sort: any): Prisma.ProjectFileWhereInput {
    if (!cursor) {
      return {};
    }

    try {
      const decodedCursor = JSON.parse(Buffer.from(cursor, "base64").toString("utf-8"));
      const { value, id } = decodedCursor;

      const sortField = sort?.field || "createdAt";
      const sortDirection = sort?.direction || "desc";

      if (sortDirection === "desc") {
        return {
          OR: [{ [sortField]: { lt: value } }, { [sortField]: value, id: { lt: id } }],
        };
      } else {
        return {
          OR: [{ [sortField]: { gt: value } }, { [sortField]: value, id: { gt: id } }],
        };
      }
    } catch (error) {
      logger.warn({ cursor, error }, "Invalid cursor provided, ignoring");
      return {};
    }
  }

  private generateCursor(file: any, sort: any): string {
    const sortField = sort?.field || "createdAt";
    const value = file[sortField];
    const id = file.id;

    const cursorData = { value, id };
    return Buffer.from(JSON.stringify(cursorData)).toString("base64");
  }
}
