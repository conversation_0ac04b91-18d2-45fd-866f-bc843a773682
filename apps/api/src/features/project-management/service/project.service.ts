import { DatabaseClient, dbClient } from "@/providers/db.provider";
import { ProjectCreateRequest } from "@/features/project-management/model/project-create.request";
import { ProjectUpdateRequest } from "@/features/project-management/model/project-update.request";
import { RequestContextHelper } from "@/server/context";
import { ProjectStatus } from "@/types-export";
import {
  ProjectFile,
  ProjectFileStatus,
  ProjectFileSubStatus,
  FileState,
  FileStateStatus,
} from "@repo/prisma/client";
import { FileUploadCreateRequest } from "../model/file-upload.create";

const ALLOWED_STATUS_SUBSTATUS: Record<ProjectFileStatus, ProjectFileSubStatus[]> = {
  Uploaded: [ProjectFileSubStatus.Completed],
  Classifying: [ProjectFileSubStatus.InProgress],
  Classified: [ProjectFileSubStatus.Completed, ProjectFileSubStatus.Failed],
  Marking: [ProjectFileSubStatus.InProgress],
  Marked: [ProjectFileSubStatus.Completed, ProjectFileSubStatus.Failed],
  Extracting: [ProjectFileSubStatus.InProgress],
  Extracted: [ProjectFileSubStatus.Completed, ProjectFileSubStatus.Failed],
  Cancelled: [ProjectFileSubStatus.Completed],
};

export class ProjectService {
  static async create(payload: ProjectCreateRequest, prismaClient: DatabaseClient = dbClient()) {
    const projectCategories: any[] = [];

    payload.categories.forEach((category) => {
      category.items.forEach((categoryItem) => {
        projectCategories.push({
          glossaryCategoryId: category.glossaryCategoryId,
          glossaryItemId: categoryItem.glossaryItemId,
          tenantId: RequestContextHelper.getTenant()!.id,
          lastModifiedBy: RequestContextHelper.getUser()!.id,
        });
      });
    });

    const result = await prismaClient.project.create({
      data: {
        code: payload.code,
        vessel: payload.vessel,
        hullNumber: payload.hullNumber,
        imoNumber: payload.imoNumber,
        vesselDeliveryDate: new Date(payload.vesselDeliveryDate),
        status: ProjectStatus.New,
        tenantId: RequestContextHelper.getTenant()!.id,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
        categories: {
          create: projectCategories,
        },
        users: {
          create: [
            {
              userId: payload.assignee.id,
              tenantId: RequestContextHelper.getTenant()!.id,
              lastModifiedBy: RequestContextHelper.getUser()!.id,
            },
          ],
        },
      },
    });

    return result;
  }

  static async update(
    projectId: string,
    payload: ProjectUpdateRequest,
    prismaClient: DatabaseClient = dbClient()
  ) {
    // update the project handler
    if (payload.assignee?.id) {
      const existingProjectHandler = await prismaClient.projectUser.findFirst({
        where: {
          projectId: projectId,
          isDeleted: false,
          tenantId: RequestContextHelper.getTenant()!.id,
        },
      });
      if (existingProjectHandler && existingProjectHandler.userId !== payload.assignee.id) {
        await prismaClient.projectUser.update({
          where: {
            id: existingProjectHandler.id,
          },
          data: {
            lastModifiedBy: RequestContextHelper.getUser()!.id,
            isDeleted: true,
          },
        });
        await prismaClient.projectUser.create({
          data: {
            projectId: projectId,
            userId: payload.assignee.id,
            tenantId: RequestContextHelper.getTenant()!.id,
            lastModifiedBy: RequestContextHelper.getUser()!.id,
          },
        });
      }
    }

    const existingProjectCategories = await prismaClient.projectCategory.findMany({
      where: {
        projectId: projectId,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id,
      },
    });

    const projectCategories: any[] = [];
    for (const category of payload.categories) {
      // check if its a multi-select category. if yes, mark all existing rows as deleted and create new rows
      if (category.items?.length > 1) {
        await prismaClient.projectCategory.updateMany({
          where: {
            projectId: projectId,
            glossaryCategoryId: category.glossaryCategoryId,
            isDeleted: false,
            tenantId: RequestContextHelper.getTenant()!.id,
          },
          data: {
            lastModifiedBy: RequestContextHelper.getUser()!.id,
            isDeleted: true,
          },
        });
        await prismaClient.projectCategory.createMany({
          data: category.items.map((categoryItem) => ({
            projectId: projectId,
            glossaryCategoryId: category.glossaryCategoryId,
            glossaryItemId: categoryItem.glossaryItemId,
            tenantId: RequestContextHelper.getTenant()!.id,
            lastModifiedBy: RequestContextHelper.getUser()!.id,
          })),
        });
      } else {
        const existingCatgeoryRow = existingProjectCategories.find(
          (item) => item.glossaryCategoryId === category.glossaryCategoryId
        );
        if (existingCatgeoryRow) {
          await prismaClient.projectCategory.update({
            where: {
              id: existingCatgeoryRow.id,
            },
            data: {
              glossaryItemId: category.items[0]?.glossaryItemId,
              lastModifiedBy: RequestContextHelper.getUser()!.id,
            },
          });
        } else {
          projectCategories.push({
            projectId: projectId,
            glossaryCategoryId: category.glossaryCategoryId,
            glossaryItemId: category.items[0]?.glossaryItemId,
            tenantId: RequestContextHelper.getTenant()!.id,
            lastModifiedBy: RequestContextHelper.getUser()!.id,
          });
        }
      }
    }

    if (projectCategories.length > 0) {
      await prismaClient.projectCategory.createMany({
        data: projectCategories,
      });
    }

    const result = await prismaClient.project.update({
      where: {
        id: projectId,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id,
      },
      data: {
        code: payload.code,
        vessel: payload.vessel,
        hullNumber: payload.hullNumber,
        imoNumber: payload.imoNumber,
        vesselDeliveryDate: new Date(payload.vesselDeliveryDate),
        status: payload.status,
        tenantId: RequestContextHelper.getTenant()!.id,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });

    return result;
  }

  static async getById(id: string, prismaClient: DatabaseClient = dbClient()) {
    return await prismaClient.project.findUnique({
      where: {
        id: id,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id,
      },
    });
  }

  static async createFileEntry(
    projectId: string,
    file: FileUploadCreateRequest,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<ProjectFile> {
    const isallowed = ALLOWED_STATUS_SUBSTATUS[ProjectFileStatus.Uploaded].includes(
      ProjectFileSubStatus.Completed
    );
    if (!isallowed) {
      throw new Error("Invalid status/substatus");
    }
    const createdEntry = await prismaClient.projectFile.create({
      data: {
        id: file.id,
        projectId: projectId,
        name: file.name,
        rootFolderPath: file.rootFolderPath,
        originalFilePath: file?.originalFilePath,
        objectStoragePath: file.objectStoragePath,
        size: file.size,
        status: ProjectFileStatus.Uploaded,
        subStatus: ProjectFileSubStatus.Completed,
        batchNumber: file.batchNumber,
        contentType: file.contentType,
        tenantId: RequestContextHelper.getTenant()!.id,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });
    return createdEntry;
  }

  static async updateStatus(
    projectId: string,
    status: ProjectStatus,
    prismaClient: DatabaseClient = dbClient()
  ) {
    await prismaClient.project.update({
      where: {
        id: projectId,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id,
      },
      data: {
        status: status,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });
  }

  static async getFileById(
    projectId: string,
    fileId: string,
    prismaClient: DatabaseClient = dbClient()
  ) {
    return await prismaClient.projectFile.findUnique({
      where: {
        id: fileId,
        projectId: projectId,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id,
      },
    });
  }

  /**
   * Update project file status and sub-status
   */
  static async updateFileStatus(
    fileId: string,
    status: ProjectFileStatus,
    subStatus: ProjectFileSubStatus,
    prismaClient: DatabaseClient = dbClient()
  ) {
    // Validate status/substatus combination
    const allowedSubStatuses = ALLOWED_STATUS_SUBSTATUS[status];
    if (!allowedSubStatuses.includes(subStatus)) {
      throw new Error(`Invalid status/substatus combination: ${status}/${subStatus}`);
    }

    return await prismaClient.projectFile.update({
      where: {
        id: fileId,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id,
      },
      data: {
        status: status,
        subStatus: subStatus,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });
  }

  /**
   * Create file state history entry
   */
  static async createFileStateHistory(
    fileId: string,
    runId: number,
    state: FileState,
    status: FileStateStatus,
    systemData?: any,
    prismaClient: DatabaseClient = dbClient()
  ) {
    return await prismaClient.projectFileStateHistory.create({
      data: {
        fileId: fileId,
        runId: runId,
        state: state,
        status: status,
        systemData: systemData || null,
        tenantId: RequestContextHelper.getTenant()!.id,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });
  }

  /**
   * Get files by IDs with project validation
   */
  static async getFilesByIds(
    projectId: string,
    fileIds: string[],
    prismaClient: DatabaseClient = dbClient()
  ) {
    return await prismaClient.projectFile.findMany({
      where: {
        id: { in: fileIds },
        projectId: projectId,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id,
      },
    });
  }

  /**
   * Check if user is project handler for the given project
   */
  static async isUserProjectHandler(
    projectId: string,
    userId: string,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<boolean> {
    const projectUser = await prismaClient.projectUser.findFirst({
      where: {
        projectId: projectId,
        userId: userId,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id,
      },
    });
    return !!projectUser;
  }
}
