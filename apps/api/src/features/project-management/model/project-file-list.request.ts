import { ProjectFileStatus } from "@repo/prisma/client";
import { Type } from "class-transformer";
import {
  <PERSON>Array,
  IsEnum,
  IsIn,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Max,
  Min,
  ValidateNested,
} from "class-validator";

/**
 * Project-specific filters
 */
export class ProjectFileFilter {
  @IsOptional()
  @IsString()
  name?: string; // String = substring search

  @IsOptional()
  @IsString()
  path?: string; // String = substring search

  @IsOptional()
  @IsArray()
  @IsEnum(ProjectFileStatus, { each: true })
  status?: ProjectFileStatus[]; // Array = IN operator
}

/**
 * Sorting configuration
 */
export class ProjectFileSort {
  @IsOptional()
  @IsString()
  @IsIn(["name", "size", "createdAt"])
  field?: string;

  @IsOptional()
  @IsString()
  @IsIn(["asc", "desc"])
  direction?: "asc" | "desc";
}

/**
 * Project list request with pagination, filtering, and sorting
 */
export class ProjectFileListRequest {
  @IsOptional()
  @ValidateNested()
  @Type(() => ProjectFileFilter)
  filters?: ProjectFileFilter;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 20;

  @IsOptional()
  @IsString()
  cursor?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => ProjectFileSort)
  sort?: ProjectFileSort;
}
