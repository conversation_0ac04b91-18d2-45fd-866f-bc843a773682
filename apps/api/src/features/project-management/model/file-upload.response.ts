import { BlobUploadResult } from "@/providers/blob.provider";
import { ProjectFileStatus } from "@/types-export";

export interface FileServiceUploadResponse {
  success: boolean;
  blob: BlobUploadResult;
  metadata: {
    originalFileName: string;
    contentType: string;
    batchNumber: string;
    path: string;
  };
}

export interface FileUploadResponse {
  id: string;
  name: string;
  createdAt: Date;
  size: number;
  path: string | null;
  status: ProjectFileStatus;
}
