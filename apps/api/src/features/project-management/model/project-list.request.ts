import { ProjectStatus } from "@repo/prisma/client";
import { Type } from "class-transformer";
import {
  IsArray,
  IsEnum,
  IsIn,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Max,
  Min,
  ValidateNested,
} from "class-validator";

/**
 * Project-specific filters
 */
export class ProjectFilters {
  @IsOptional()
  @IsString()
  code?: string; // String = substring search

  @IsOptional()
  @IsString()
  vessel?: string; // String = substring search

  @IsOptional()
  @IsUUID()
  vesselType?: string; // Glossary item ID for vessel type

  @IsOptional()
  @IsArray()
  @IsEnum(ProjectStatus, { each: true })
  status?: ProjectStatus[]; // Array = IN operator

  @IsOptional()
  @IsUUID()
  projectHandler?: string; // User ID of project handler
}

/**
 * Sorting configuration
 */
export class ProjectSort {
  @IsOptional()
  @IsString()
  @IsIn(["vessel", "code", "createdAt", "updatedAt"])
  field?: string;

  @IsOptional()
  @IsString()
  @IsIn(["asc", "desc"])
  direction?: "asc" | "desc";
}

/**
 * Project list request with pagination, filtering, and sorting
 */
export class ProjectListRequest {
  @IsOptional()
  @ValidateNested()
  @Type(() => ProjectFilters)
  filters?: ProjectFilters;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 20;

  @IsOptional()
  @IsString()
  cursor?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => ProjectSort)
  sort?: ProjectSort;
}
