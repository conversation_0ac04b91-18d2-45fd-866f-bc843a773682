import { UserR<PERSON> } from "@/types-export";
import { Type } from "class-transformer";
import { <PERSON>Array, IsNotEmpty, IsOptional, IsString, IsUUID, ValidateNested } from "class-validator";

export class ProjectCategoryItemBase {
  @IsUUID()
  @IsNotEmpty()
  glossaryItemId!: string;

  @IsString()
  @IsOptional()
  glossaryItemName?: string;
}

export class ProjectCategoryBase {
  @IsUUID()
  @IsNotEmpty()
  glossaryCategoryId!: string;

  @IsString()
  @IsOptional()
  glossaryCategoryName?: string;

  @IsArray()
  @ValidateNested()
  @Type(() => ProjectCategoryItemBase)
  items!: ProjectCategoryItemBase[];
}

export class ProjectUserBase {
  @IsUUID()
  @IsNotEmpty()
  id!: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  role?: UserRole;
}
