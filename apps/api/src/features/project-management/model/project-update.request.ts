import { ProjectStatus } from "@/types-export";
import {
  IsDateString,
  IsIn,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  ValidateNested,
} from "class-validator";
import { Type } from "class-transformer";
import {
  ProjectCategoryBase,
  ProjectUserBase,
} from "@/features/project-management/model/project.base";

export class ProjectUpdateRequest {
  @IsOptional()
  @ValidateNested()
  @Type(() => ProjectUserBase)
  assignee!: ProjectUserBase;

  @IsString()
  @IsOptional()
  code!: string;

  @IsString()
  @IsOptional()
  vessel!: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => ProjectCategoryBase)
  categories!: ProjectCategoryBase[];

  @IsString()
  @IsOptional()
  @MaxLength(50)
  hullNumber!: string;

  @IsString()
  @IsOptional()
  @MaxLength(7)
  @Matches(/^[0-9]{7}$/, {
    message: "IMO number must be 7 digits",
  })
  imoNumber!: string;

  @IsDateString()
  @IsOptional()
  vesselDeliveryDate!: Date;

  @IsIn([ProjectStatus.Completed])
  @IsOptional()
  status!: ProjectStatus;
}
