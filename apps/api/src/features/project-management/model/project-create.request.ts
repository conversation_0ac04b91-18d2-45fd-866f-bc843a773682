import { Type } from "class-transformer";
import {
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  MaxLength,
  ValidateNested,
} from "class-validator";
import {
  ProjectCategoryBase,
  ProjectUserBase,
} from "@/features/project-management/model/project.base";

export class ProjectCreateRequest {
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => ProjectUserBase)
  assignee!: ProjectUserBase;

  @IsString()
  @IsNotEmpty()
  code!: string;

  @IsString()
  @IsNotEmpty()
  vessel!: string;

  @IsArray()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => ProjectCategoryBase)
  categories!: ProjectCategoryBase[];

  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  hullNumber!: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(7)
  @Matches(/^[0-9]{7}$/, {
    message: "IMO number must be 7 digits",
  })
  imoNumber!: string;

  @IsDateString()
  @IsOptional()
  vesselDeliveryDate!: Date;
}
