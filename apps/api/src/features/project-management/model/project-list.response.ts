import { ProjectStatus } from "@repo/prisma/client";
import {
  ProjectCategoryBase,
  ProjectUserBase,
} from "@/features/project-management/model/project.base";

export interface ProjectListResponse {
  projects: ProjectResponse[];
  nextCursor?: string;
  hasMore: boolean;
}

export interface ProjectResponse {
  id: string;
  code: string;
  vessel: string;
  assignee: ProjectUserBase;
  categories: ProjectCategoryBase[];
  hullNumber: string;
  imoNumber: string;
  vesselDeliveryDate: Date | null;
  status: ProjectStatus;
  fileCount: number;
  createdAt: Date;
  updatedAt: Date;
}
