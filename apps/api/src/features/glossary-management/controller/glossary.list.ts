import { BaseResponse } from "@/shared/model/base.response";
import { Body, JsonController, Post, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { GlossaryListRequest } from "@/features/glossary-management/model/glossary-list.request";
import {
  GlossaryListResponse,
  GlossaryResponse,
} from "@/features/glossary-management/model/glossary-list.response";
import { dbClient } from "@/providers/db.provider";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { GlossaryType, Prisma } from "@repo/prisma/client";

@JsonController("/glossaries/find")
@UseBefore(hasPermission(Resource.Glossary, [Action.Read]))
export class GlossaryListController {
  @Post("/")
  public async process(
    @Body() payload: GlossaryListRequest
  ): Promise<BaseResponse<GlossaryListResponse>> {
    logger.info(
      {
        filters: payload.filters,
        sort: payload.sort,
        limit: payload.limit,
        cursor: payload.cursor ? "present" : "none",
      },
      "Starting glossary list query"
    );

    // Build where clause from filters
    const whereClause = this.buildWhereClause(payload);

    // Build order by clause from sort
    const orderBy = this.buildOrderByClause(payload);

    // Build cursor-based pagination
    const cursorWhere = this.buildCursorClause(payload.cursor, payload.sort);

    // Set pagination parameters
    const limit = payload.limit!;
    const take = limit + 1; // Take one extra to check if there are more results

    logger.info(
      {
        whereClause,
        orderBy,
        cursorWhere,
        limit,
        take,
      },
      "Built query parameters for glossary listing"
    );

    // Combine where clauses
    const finalWhere: Prisma.GlossaryWhereInput = {
      AND: [whereClause, cursorWhere].filter((clause) => Object.keys(clause).length > 0),
    };

    logger.info(
      {
        finalWhere,
      },
      "Executing glossary query"
    );

    const glossaries = await dbClient().glossary.findMany({
      where: finalWhere,
      orderBy,
      take,
    });

    logger.info(
      {
        glossaryCount: glossaries.length,
        hasMore: glossaries.length > limit,
      },
      "Glossary query completed"
    );

    // Check if there are more results
    const hasMore = glossaries.length > limit;
    if (hasMore) {
      glossaries.pop(); // Remove the extra record
    }

    // Generate next cursor based on sort field
    const nextCursor =
      hasMore && glossaries.length > 0
        ? this.generateCursor(glossaries[glossaries.length - 1]!, payload.sort)
        : undefined;

    logger.info(
      {
        hasMore,
        nextCursor,
        resultCount: glossaries.length,
      },
      "Generating response with pagination info"
    );

    const glossariesResponse: GlossaryResponse[] = glossaries.map((glossary) => ({
      id: glossary.id,
      name: glossary.name,
      type: glossary.type,
      remark: glossary.remark,
      status: glossary.status,
      createdAt: glossary.createdAt,
      updatedAt: glossary.updatedAt,
    }));

    const response: GlossaryListResponse = {
      glossaries: glossariesResponse,
      nextCursor,
      hasMore,
    };

    logger.info(
      {
        totalResults: glossariesResponse.length,
        hasMore,
        nextCursor: nextCursor ? "present" : "none",
      },
      "Glossary list query completed successfully"
    );

    return BaseResponse.success(response);
  }

  private buildWhereClause(payload: GlossaryListRequest): Prisma.GlossaryWhereInput {
    const where: Prisma.GlossaryWhereInput = {
      isDeleted: false,
      OR: [{ tenantId: RequestContextHelper.getTenant()!.id! }, { type: GlossaryType.Global }],
    };

    if (!payload.filters) {
      return where;
    }

    const { filters } = payload;

    // Name filter - simple substring match
    if (filters.name) {
      where.name = { contains: filters.name, mode: "insensitive" };
    }

    // Type filter - IN operator
    if (filters.type && filters.type.length > 0) {
      where.type = { in: filters.type as any };
    }

    // Status filter - IN operator
    if (filters.status && filters.status.length > 0) {
      where.status = { in: filters.status as any };
    }

    return where;
  }

  private buildOrderByClause(
    payload: GlossaryListRequest
  ): Prisma.GlossaryOrderByWithRelationInput[] {
    if (!payload.sort?.field) {
      return [{ createdAt: "desc" }]; // Default sort
    }
    const { field, direction = "asc" } = payload.sort;
    const dbField = field || "createdAt";
    return [{ [dbField]: direction }];
  }

  private buildCursorClause(
    cursor?: string,
    sort?: { field?: string; direction?: string }
  ): Prisma.GlossaryWhereInput {
    if (!cursor) {
      return {};
    }

    try {
      const decodedCursor = JSON.parse(Buffer.from(cursor, "base64").toString());
      const { field = "createdAt", direction = "desc" } = sort || {};
      const dbField = field || "createdAt";
      const cursorValue = decodedCursor.value;
      const cursorId = decodedCursor.id;

      // For cursor-based pagination, we need to handle the direction
      if (direction === "asc") {
        return {
          OR: [
            { [dbField]: { gt: cursorValue } },
            {
              AND: [
                { [dbField]: { equals: cursorValue } },
                { id: { gt: cursorId } }, // Use id as tiebreaker
              ],
            },
          ],
        };
      } else {
        return {
          OR: [
            { [dbField]: { lt: cursorValue } },
            {
              AND: [
                { [dbField]: { equals: cursorValue } },
                { id: { lt: cursorId } }, // Use id as tiebreaker
              ],
            },
          ],
        };
      }
    } catch (error) {
      // Invalid cursor, ignore it
      return {};
    }
  }

  private generateCursor(glossary: any, sort?: { field?: string; direction?: string }): string {
    const { field = "createdAt" } = sort || {};

    // Map field names to actual database fields
    const dbField = field || "createdAt";
    const cursorData = {
      value: glossary[dbField],
      id: glossary.id, // Include id as tiebreaker
    };

    return Buffer.from(JSON.stringify(cursorData)).toString("base64");
  }
}
