import { hasPermission } from "@/middleware/has-permission.middleware";
import { BaseResponse } from "@/shared/model/base.response";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { JsonController, Param, <PERSON>, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { GlossaryService } from "@/features/glossary-management/service/glossary.service";
import { GlossaryStatus } from "@/types-export";
import { logger } from "@/providers/logger.provider";
import { BaseError } from "@/exception-handling/base.error";

@JsonController("/glossary/:id/deactivate")
@UseBefore(hasPermission(Resource.Glossary, [Action.Update]))
export class GlossaryDeActivateController {
  @Patch("/")
  public async process(@Param("id") id: string, @Res() res: Response): Promise<Response> {
    const glossaryDetails = await GlossaryService.getGlossaryById(id);
    if (!glossaryDetails || glossaryDetails.status !== GlossaryStatus.Active) {
      logger.error(
        {
          glossaryId: id,
        },
        "Glossary not found for deactivation. Check the status."
      );
      throw new BaseError("GLOSSARY_NOT_FOUND", "Glossary not found", StatusCodes.NOT_FOUND, {});
    }

    const updatedGlossary = await GlossaryService.updateGlossaryStatus(id, GlossaryStatus.Inactive);

    return res.status(StatusCodes.OK).json(BaseResponse.ok());
  }
}
