import { hasPermission } from "@/middleware/has-permission.middleware";
import { BaseResponse } from "@/shared/model/base.response";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Param, Patch, Post, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { GlossaryService } from "@/features/glossary-management/service/glossary.service";
import { GlossaryUpdateRequest } from "@/features/glossary-management/model/glossary-update.request";
import { logger } from "@/providers/logger.provider";
import { BaseError } from "@/exception-handling/base.error";

@JsonController("/glossary/:id")
@UseBefore(hasPermission(Resource.Glossary, [Action.Update]))
export class GlossaryUpdateController {
  @Patch("/")
  public async process(
    @Param("id") id: string,
    @Body() payload: GlossaryUpdateRequest,
    @Res() res: Response
  ): Promise<Response> {
    const glossaryDetails = await GlossaryService.getGlossaryById(id);
    if (!glossaryDetails) {
      logger.error(
        {
          glossaryId: id,
        },
        "Glossary not found for update"
      );
      throw new BaseError("GLOSSARY_NOT_FOUND", "Glossary not found", StatusCodes.NOT_FOUND, {});
    }

    const updatedGlossary = await GlossaryService.updateGlossary(id, payload);

    return res.status(StatusCodes.OK).json(BaseResponse.ok());
  }
}
