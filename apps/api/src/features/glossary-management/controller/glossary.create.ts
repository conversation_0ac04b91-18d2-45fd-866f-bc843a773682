import { hasPermission } from "@/middleware/has-permission.middleware";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Post, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { GlossaryCreateRequest } from "@/features/glossary-management/model/glossary-create.request";
import { GlossaryService } from "@/features/glossary-management/service/glossary.service";
import { GlossaryType, TenantType, UserRole } from "@repo/prisma/client";
import { TenantService } from "@/features/tenant-management/service/tenant.service";

@JsonController("/glossary")
@UseBefore(hasPermission(Resource.Glossary, [Action.Create]))
export class GlossaryCreateController {
  @Post("/")
  public async process(
    @Body() payload: GlossaryCreateRequest,
    @Res() res: Response
  ): Promise<Response> {
    const tenantDetails = await TenantService.getActiveTenantById(
      RequestContextHelper.getTenant()!.id!,
      false
    );

    if (
      tenantDetails!.type === TenantType.Platform &&
      RequestContextHelper.getUser()!.role === UserRole.SuperAdmin
    ) {
      payload.type = GlossaryType.Global;
    } else {
      payload.type = GlossaryType.Self;
    }

    const glossaryDetails = await GlossaryService.createGlossary(payload);

    return res.status(StatusCodes.CREATED).json(BaseResponse.ok());
  }
}
