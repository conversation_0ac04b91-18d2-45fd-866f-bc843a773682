import { Type } from "class-transformer";
import {
  <PERSON><PERSON><PERSON>y,
  <PERSON><PERSON><PERSON>,
  <PERSON>In,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  <PERSON>,
  <PERSON>,
  ValidateNested,
} from "class-validator";
import { GlossaryStatus, GlossaryType } from "@repo/prisma/client";

/**
 * Glossary-specific filters
 */
export class GlossaryFilters {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsArray()
  @IsEnum(GlossaryType, { each: true })
  type?: GlossaryType[]; // Array = IN operator

  @IsOptional()
  @IsArray()
  @IsEnum(GlossaryStatus, { each: true })
  status?: GlossaryStatus[]; // Array = IN operator
}

/**
 * Sorting configuration
 */
export class GlossarySort {
  @IsOptional()
  @IsString()
  @IsIn(["name", "updatedAt"])
  field?: string;

  @IsOptional()
  @IsString()
  @IsIn(["asc", "desc"])
  direction?: "asc" | "desc";
}

/**
 * User list request with pagination, filtering, and sorting
 */
export class GlossaryListRequest {
  @IsOptional()
  @ValidateNested()
  @Type(() => GlossaryFilters)
  filters?: GlossaryFilters;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 20;

  @IsOptional()
  @IsString()
  cursor?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => GlossarySort)
  sort?: GlossarySort;
}
