import { dbClient, DatabaseClient } from "@/providers/db.provider";
import { Glossary, GlossaryStatus, GlossaryType } from "@repo/prisma/client";
import { RequestContextHelper } from "@/server/context";
import { GlossaryCreateRequest } from "@/features/glossary-management/model/glossary-create.request";
import { GlossaryUpdateRequest } from "@/features/glossary-management/model/glossary-update.request";

export class GlossaryService {
  static async createGlossary(
    payload: GlossaryCreateRequest,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<Glossary> {
    const createdGlossary = await prismaClient.glossary.create({
      data: {
        name: payload.name,
        type: payload.type as GlossaryType,
        remark: payload.remark,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
    });
    return createdGlossary;
  }

  static async updateGlossary(
    id: string,
    payload: GlossaryUpdateRequest,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<Glossary> {
    const updatedGlossary = await prismaClient.glossary.update({
      where: {
        id: id,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
      data: {
        name: payload.name,
        remark: payload.remark,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
    });
    return updatedGlossary;
  }

  static async getGlossaryById(id: string, prismaClient: DatabaseClient = dbClient()) {
    const result = await prismaClient.glossary.findUnique({
      where: {
        id: id,
        isDeleted: false,
        OR: [{ tenantId: RequestContextHelper.getTenant()!.id! }, { type: GlossaryType.Global }],
      },
    });
    return result;
  }

  static async updateGlossaryStatus(
    id: string,
    status: GlossaryStatus,
    prismaClient: DatabaseClient = dbClient()
  ) {
    await prismaClient.glossary.update({
      where: {
        id: id,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
      data: {
        status: status,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });
  }
}
