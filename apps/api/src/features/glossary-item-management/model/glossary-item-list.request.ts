import { Type } from "class-transformer";
import {
  IsArray,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  <PERSON>,
  <PERSON>,
  ValidateNested,
} from "class-validator";
import { GlossaryStatus } from "@repo/prisma/client";

/**
 * Glossary Item-specific filters
 */
export class GlossaryItemFilters {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  code?: string;

  @IsOptional()
  @IsString()
  referenceCode?: string;

  @IsOptional()
  @IsArray()
  @IsEnum(GlossaryStatus, { each: true })
  status?: GlossaryStatus[]; // Array = IN operator
}

/**
 * Sorting configuration
 */
export class GlossaryItemSort {
  @IsOptional()
  @IsString()
  @IsIn(["name", "createdAt", "updatedAt"])
  field?: string;

  @IsOptional()
  @IsString()
  @IsIn(["asc", "desc"])
  direction?: "asc" | "desc";
}

/**
 * Glossary Item list request with pagination, filtering, and sorting
 */
export class GlossaryItemListRequest {
  @IsOptional()
  @ValidateNested()
  @Type(() => GlossaryItemFilters)
  filters?: GlossaryItemFilters;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 20;

  @IsOptional()
  @IsString()
  cursor?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => GlossaryItemSort)
  sort?: GlossaryItemSort;
}

export class IdParam {
  @IsUUID()
  @IsNotEmpty()
  id!: string;
}
