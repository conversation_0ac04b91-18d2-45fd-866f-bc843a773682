import { dbClient, DatabaseClient } from "@/providers/db.provider";
import { GlossaryItem, GlossaryStatus, GlossaryType } from "@repo/prisma/client";
import { RequestContextHelper } from "@/server/context";
import { GlossaryItemCreateRequest } from "@/features/glossary-item-management/model/glossary-item-create.request";
import { GlossaryItemUpdateRequest } from "@/features/glossary-item-management/model/glossary-item-update.request";
import { StatusCodes } from "http-status-codes";
import { BaseError } from "@/exception-handling/base.error";

export class GlossaryItemService {
  static async create(
    glossaryId: string,
    glossaryItemType: GlossaryType,
    payload: GlossaryItemCreateRequest,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<GlossaryItem> {
    let count = await prismaClient.glossaryItem.count({
      where: {
        glossaryId: glossaryId,
        isDeleted: false,
      },
    });

    if (!count) {
      count = 1;
    } else {
      ++count;
    }

    const glossaryItemCode = `ITEM${count.toString().padStart(4, "0")}`;

    const referenceCodes = await prismaClient.glossaryItem.findMany({
      where: {
        glossaryId: glossaryId,
        isDeleted: false,
      },
      select: {
        referenceCode: true,
      },
    });

    const referenceCodesArray = referenceCodes.map((item) => item.referenceCode);

    if (referenceCodesArray.includes(payload.referenceCode)) {
      throw new BaseError(
        "REFERENCE_CODE_ALREADY_EXISTS",
        "Reference code already exists",
        StatusCodes.BAD_REQUEST,
        {}
      );
    }

    const createdGlossary = await prismaClient.glossaryItem.create({
      data: {
        glossaryId: glossaryId,
        name: payload.name,
        referenceCode: payload.referenceCode,
        code: glossaryItemCode,
        type: glossaryItemType,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
    });
    return createdGlossary;
  }

  static async update(
    glossaryItemId: string,
    payload: GlossaryItemUpdateRequest,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<GlossaryItem> {
    const glossaryItemDetails = await this.getById(glossaryItemId);

    const referenceCodes = await prismaClient.glossaryItem.findMany({
      where: {
        glossaryId: glossaryItemDetails?.glossaryId,
        isDeleted: false,
      },
      select: {
        referenceCode: true,
      },
    });

    const referenceCodesArray = referenceCodes.map((item) => item.referenceCode);

    if (referenceCodesArray.includes(payload.referenceCode)) {
      throw new BaseError(
        "REFERENCE_CODE_ALREADY_EXISTS",
        "Reference code already exists",
        StatusCodes.BAD_REQUEST,
        {}
      );
    }

    const updatedGlossary = await prismaClient.glossaryItem.update({
      where: {
        id: glossaryItemId,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
      data: {
        name: payload.name,
        referenceCode: payload.referenceCode,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
    });
    return updatedGlossary;
  }

  static async getById(glossaryItemId: string, prismaClient: DatabaseClient = dbClient()) {
    const result = await prismaClient.glossaryItem.findUnique({
      where: {
        id: glossaryItemId,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
    });
    return result;
  }

  static async getByIds(glossaryItemIds: string[], prismaClient: DatabaseClient = dbClient()) {
    const result = await prismaClient.glossaryItem.findMany({
      where: {
        id: { in: glossaryItemIds },
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
    });
    return result;
  }

  static async updateStatus(
    glossaryItemId: string,
    status: GlossaryStatus,
    prismaClient: DatabaseClient = dbClient()
  ) {
    await prismaClient.glossaryItem.update({
      where: {
        id: glossaryItemId,
        isDeleted: false,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
      data: {
        status: status,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });
  }
}
