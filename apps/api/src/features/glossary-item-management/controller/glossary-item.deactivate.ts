import { hasPermission } from "@/middleware/has-permission.middleware";
import { BaseResponse } from "@/shared/model/base.response";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { JsonController, Param, Patch, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { GlossaryItemService } from "@/features/glossary-item-management/service/glossary-item.service";
import { GlossaryStatus } from "@/types-export";
import { logger } from "@/providers/logger.provider";
import { BaseError } from "@/exception-handling/base.error";

@JsonController("/glossary-item/:id/deactivate")
@UseBefore(hasPermission(Resource.GlossaryItem, [Action.Delete]))
export class GlossaryItemDeActivateController {
  @Patch("/")
  public async process(@Param("id") id: string, @Res() res: Response): Promise<Response> {
    const glossaryDetails = await GlossaryItemService.getById(id);
    if (!glossaryDetails) {
      logger.error(
        {
          glossaryItemId: id,
        },
        "Glossary item not found for deactivation"
      );
      throw new BaseError(
        "GLOSSARY_ITEM_NOT_FOUND",
        "Glossary item not found",
        StatusCodes.NOT_FOUND,
        {}
      );
    }

    const updatedGlossary = await GlossaryItemService.updateStatus(id, GlossaryStatus.Inactive);

    return res.status(StatusCodes.OK).json(BaseResponse.ok());
  }
}
