import { hasPermission } from "@/middleware/has-permission.middleware";
import { BaseResponse } from "@/shared/model/base.response";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Param, Patch, Post, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { GlossaryItemUpdateRequest } from "@/features/glossary-item-management/model/glossary-item-update.request";
import { GlossaryItemService } from "@/features/glossary-item-management/service/glossary-item.service";
import { BaseError } from "@/exception-handling/base.error";
import { logger } from "@/providers/logger.provider";

@JsonController("/glossary-item/:id")
@UseBefore(hasPermission(Resource.GlossaryItem, [Action.Update]))
export class GlossaryItemUpdateController {
  @Patch("/")
  public async process(
    @Param("id") id: string,
    @Body() payload: GlossaryItemUpdateRequest,
    @Res() res: Response
  ): Promise<Response> {
    const glossaryDetails = await GlossaryItemService.getById(id);
    if (!glossaryDetails) {
      logger.error(
        {
          glossaryItemId: id,
        },
        "Glossary item not found for update"
      );
      throw new BaseError(
        "GLOSSARY_ITEM_NOT_FOUND",
        "Glossary item not found",
        StatusCodes.NOT_FOUND,
        {}
      );
    }

    const updatedGlossary = await GlossaryItemService.update(id, payload);

    return res.status(StatusCodes.OK).json(BaseResponse.ok());
  }
}
