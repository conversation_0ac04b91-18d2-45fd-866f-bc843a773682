import { hasPermission } from "@/middleware/has-permission.middleware";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Param, Post, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { GlossaryType, TenantType, UserRole } from "@repo/prisma/client";
import { TenantService } from "@/features/tenant-management/service/tenant.service";
import { GlossaryItemCreateRequest } from "@/features/glossary-item-management/model/glossary-item-create.request";
import { GlossaryService } from "@/features/glossary-management/service/glossary.service";
import { GlossaryItemService } from "@/features/glossary-item-management/service/glossary-item.service";
import { logger } from "@/providers/logger.provider";
import { BaseError } from "@/exception-handling/base.error";

@JsonController("/glossary/:id")
@UseBefore(hasPermission(Resource.GlossaryItem, [Action.Create]))
export class GlossaryItemCreateController {
  @Post("/")
  public async process(
    @Param("id") id: string,
    @Body() payload: GlossaryItemCreateRequest,
    @Res() res: Response
  ): Promise<Response> {
    const glossaryDetails = await GlossaryService.getGlossaryById(id);
    if (!glossaryDetails) {
      logger.error(
        {
          glossaryId: id,
        },
        "Glossary not found for item creation"
      );
      throw new BaseError("GLOSSARY_NOT_FOUND", "Glossary not found", StatusCodes.NOT_FOUND, {});
    }

    const tenantDetails = await TenantService.getActiveTenantById(
      RequestContextHelper.getTenant()!.id!,
      false
    );

    let glossaryItemType;
    if (
      tenantDetails!.type === TenantType.Platform &&
      RequestContextHelper.getUser()!.role === UserRole.SuperAdmin
    ) {
      glossaryItemType = GlossaryType.Global;
    } else {
      glossaryItemType = GlossaryType.Self;
    }

    const glossaryItemDetails = await GlossaryItemService.create(id, glossaryItemType, payload);

    return res.status(StatusCodes.CREATED).json(BaseResponse.ok());
  }
}
