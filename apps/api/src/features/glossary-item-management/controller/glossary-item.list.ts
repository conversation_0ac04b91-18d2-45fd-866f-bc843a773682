import { BaseResponse } from "@/shared/model/base.response";
import { Body, JsonController, Param, Params, Post, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { hasPermission } from "@/middleware/has-permission.middleware";
import {
  GlossaryItemListResponse,
  GlossaryItemResponse,
} from "@/features/glossary-item-management/model/glossary-item-list.response";
import { GlossaryItemListRequest } from "@/features/glossary-item-management/model/glossary-item-list.request";
import { dbClient } from "@/providers/db.provider";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { Prisma, GlossaryType } from "@repo/prisma/client";
import { GlossaryService } from "@/features/glossary-management/service/glossary.service";
import { BaseError } from "@/exception-handling/base.error";
import { StatusCodes } from "http-status-codes";
import { IdParam } from "@/features/glossary-item-management/model/glossary-item-list.request";

@JsonController("/glossaries/:id/items/find")
@UseBefore(hasPermission(Resource.GlossaryItem, [Action.Read]))
export class GlossaryItemListController {
  @Post("/")
  public async process(
    @Params() params: IdParam,
    @Body() payload: GlossaryItemListRequest
  ): Promise<BaseResponse<GlossaryItemListResponse>> {
    logger.info(
      {
        glossaryId: params.id,
        filters: payload.filters,
        sort: payload.sort,
        limit: payload.limit,
        cursor: payload.cursor ? "present" : "none",
      },
      "Starting glossary item list query"
    );

    // First, verify the glossary exists and user has access to it
    const glossary = await GlossaryService.getGlossaryById(params.id);
    if (!glossary) {
      throw new BaseError("GLOSSARY_NOT_FOUND", "Glossary not found", StatusCodes.NOT_FOUND, {});
    }

    // Build where clause from filters
    const whereClause = this.buildWhereClause(payload, params.id);

    // Build order by clause from sort
    const orderBy = this.buildOrderByClause(payload);

    // Build cursor-based pagination
    const cursorWhere = this.buildCursorClause(payload.cursor, payload.sort);

    // Set pagination parameters
    const limit = payload.limit!;
    const take = limit + 1; // Take one extra to check if there are more results

    logger.info(
      {
        whereClause,
        orderBy,
        cursorWhere,
        limit,
        take,
      },
      "Built query parameters for glossary item listing"
    );

    // Combine where clauses
    const finalWhere: Prisma.GlossaryItemWhereInput = {
      AND: [whereClause, cursorWhere].filter((clause) => Object.keys(clause).length > 0),
    };

    logger.info(
      {
        finalWhere,
      },
      "Executing glossary item query"
    );

    const glossaryItems = await dbClient().glossaryItem.findMany({
      where: finalWhere,
      orderBy,
      take,
    });

    logger.info(
      {
        glossaryItemCount: glossaryItems.length,
        hasMore: glossaryItems.length > limit,
      },
      "Glossary item query completed"
    );

    // Check if there are more results
    const hasMore = glossaryItems.length > limit;
    if (hasMore) {
      glossaryItems.pop(); // Remove the extra record
    }

    // Generate next cursor based on sort field
    const nextCursor =
      hasMore && glossaryItems.length > 0
        ? this.generateCursor(glossaryItems[glossaryItems.length - 1]!, payload.sort)
        : undefined;

    logger.info(
      {
        hasMore,
        nextCursor,
        resultCount: glossaryItems.length,
      },
      "Generating response with pagination info"
    );

    const glossaryItemsResponse: GlossaryItemResponse[] = glossaryItems.map((item) => ({
      id: item.id,
      code: item.code,
      name: item.name,
      referenceCode: item.referenceCode,
      type: item.type,
      status: item.status,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
    }));

    const response: GlossaryItemListResponse = {
      glossaryItems: glossaryItemsResponse,
      glossaryName: glossary.name,
      glossaryId: glossary.id,
      nextCursor,
      hasMore,
    };

    logger.info(
      {
        totalResults: glossaryItemsResponse.length,
        hasMore,
        nextCursor: nextCursor ? "present" : "none",
        glossaryName: glossary.name,
      },
      "Glossary item list query completed successfully"
    );

    return BaseResponse.success(response);
  }

  private buildWhereClause(
    payload: GlossaryItemListRequest,
    glossaryId: string
  ): Prisma.GlossaryItemWhereInput {
    const where: Prisma.GlossaryItemWhereInput = {
      glossaryId: glossaryId,
      isDeleted: false,
      // Add tenant filter for security
      OR: [
        { tenantId: RequestContextHelper.getTenant()!.id! }, // Own tenant's glossaries
        { type: GlossaryType.Global }, // Global glossaries accessible to all
      ],
    };

    if (!payload.filters) {
      return where;
    }

    const { filters } = payload;

    // Name filter - simple substring match
    if (filters.name) {
      where.name = { contains: filters.name, mode: "insensitive" };
    }

    // Code filter - simple substring match
    if (filters.code) {
      where.code = { contains: filters.code, mode: "insensitive" };
    }

    // Reference code filter - simple substring match
    if (filters.referenceCode) {
      where.referenceCode = { contains: filters.referenceCode, mode: "insensitive" };
    }

    // Status filter - IN operator
    if (filters.status && filters.status.length > 0) {
      where.status = { in: filters.status as any };
    }

    return where;
  }

  private buildOrderByClause(
    payload: GlossaryItemListRequest
  ): Prisma.GlossaryItemOrderByWithRelationInput[] {
    if (!payload.sort?.field) {
      return [{ createdAt: "desc" }]; // Default sort
    }
    const { field, direction = "asc" } = payload.sort;
    const dbField = field || "createdAt";
    return [{ [dbField]: direction }];
  }

  private buildCursorClause(
    cursor?: string,
    sort?: { field?: string; direction?: string }
  ): Prisma.GlossaryItemWhereInput {
    if (!cursor) {
      return {};
    }

    try {
      const decodedCursor = JSON.parse(Buffer.from(cursor, "base64").toString());
      const { field = "createdAt", direction = "desc" } = sort || {};
      const dbField = field || "createdAt";
      const cursorValue = decodedCursor.value;
      const cursorId = decodedCursor.id;

      // For cursor-based pagination, we need to handle the direction
      if (direction === "asc") {
        return {
          OR: [
            { [dbField]: { gt: cursorValue } },
            {
              AND: [
                { [dbField]: { equals: cursorValue } },
                { id: { gt: cursorId } }, // Use id as tiebreaker
              ],
            },
          ],
        };
      } else {
        return {
          OR: [
            { [dbField]: { lt: cursorValue } },
            {
              AND: [
                { [dbField]: { equals: cursorValue } },
                { id: { lt: cursorId } }, // Use id as tiebreaker
              ],
            },
          ],
        };
      }
    } catch (error) {
      // Invalid cursor, ignore it
      return {};
    }
  }

  private generateCursor(glossaryItem: any, sort?: { field?: string; direction?: string }): string {
    const { field = "createdAt" } = sort || {};

    // Map field names to actual database fields
    const dbField = field || "createdAt";
    const cursorData = {
      value: glossaryItem[dbField],
      id: glossaryItem.id, // Include id as tiebreaker
    };

    return Buffer.from(JSON.stringify(cursorData)).toString("base64");
  }
}
