import { UserProfileResponse } from "@/features/user-management/model/user-profile.response";
import { dbClient, DatabaseClient } from "@/providers/db.provider";
import { logger } from "@/providers/logger.provider";
import { getPolicyForRole } from "@/providers/policy.provider";
import { TenantStatus, User, UserStatus } from "@repo/prisma/client";
import { UserSignupRequest, UserUpdateRequest } from "@/features/user-management/model";
import { RequestContextHelper } from "@/server/context";

export class UserService {
  static async getUserById(
    userId: string,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<UserProfileResponse | undefined> {
    logger.info({ userId }, "Getting user by id");
    const user = await prismaClient.user.findUnique({
      where: {
        id: userId,
        isDeleted: false,
      },
    });

    if (user) {
      return await this.getUserProfile(user, prismaClient);
    }
  }

  static async getUserByEmailId(
    emailId: string,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<UserProfileResponse | undefined> {
    logger.info({ emailId }, "Getting user by emailId");
    const user = await prismaClient.user.findUnique({
      where: {
        emailId: emailId,
        isDeleted: false,
      },
    });

    if (user) {
      return await this.getUserProfile(user, prismaClient);
    }
  }

  static async getActiveUserById(
    userId: string,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<UserProfileResponse | undefined> {
    const user = await this.getUserById(userId, prismaClient);
    if (user && user.status === UserStatus.Active && user.tenant?.status === TenantStatus.Active) {
      return user;
    }
  }

  static async getActiveUserByEmailId(
    emailId: string,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<UserProfileResponse | undefined> {
    const user = await this.getUserByEmailId(emailId, prismaClient);
    if (user && user.status === UserStatus.Active && user.tenant?.status === TenantStatus.Active) {
      return user;
    }
  }

  static async createUser(
    payload: UserSignupRequest,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<UserProfileResponse> {
    const createdUser = await prismaClient.user.create({
      data: {
        name: payload.name,
        displayName: payload.displayName,
        emailId: payload.emailId,
        role: payload.role,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
        tenantId: RequestContextHelper.getTenant()!.id!,
      },
    });
    logger.info(
      {
        userId: createdUser.id,
        userEmail: createdUser.emailId,
        tenantId: createdUser.tenantId,
      },
      "User created successfully in database"
    );
    const userProfile = await this.getUserById(createdUser.id);
    return userProfile!;
  }

  static async updateUser(
    payload: UserUpdateRequest,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<UserProfileResponse> {
    const updatedUser = await prismaClient.user.update({
      where: {
        id: payload.id,
      },
      data: {
        name: payload.name,
        displayName: payload.displayName,
        role: payload.role,
        lastModifiedBy: RequestContextHelper.getUser()!.id,
      },
    });
    logger.info(
      {
        userId: updatedUser.id,
        userEmail: updatedUser.emailId,
        tenantId: updatedUser.tenantId,
      },
      "User created successfully in database"
    );
    const userProfile = await this.getUserById(updatedUser.id);
    return userProfile!;
  }

  static async updateUserStatus(
    userId: string,
    status: UserStatus,
    prismaClient: DatabaseClient = dbClient()
  ): Promise<UserProfileResponse> {
    const updatedUser = await prismaClient.user.update({
      where: {
        id: userId,
      },
      data: {
        status: status,
        lastModifiedBy: RequestContextHelper.getUser()?.id || userId,
      },
    });
    logger.info(
      {
        userId: updatedUser.id,
        userEmail: updatedUser.emailId,
        tenantId: updatedUser.tenantId,
        status: updatedUser.status,
      },
      "User status updated successfully in database"
    );
    const userProfile = await this.getUserById(updatedUser.id);
    return userProfile!;
  }

  private static async getUserProfile(
    user: User,
    prismaClient: DatabaseClient
  ): Promise<UserProfileResponse | undefined> {
    const tenant = await prismaClient.tenant.findUnique({
      where: {
        id: user.tenantId,
        isDeleted: false,
      },
    });

    // Get privileges from policy provider for the user's role
    const rolePolicy = getPolicyForRole(user.tenantId, user.role);
    const privileges: { [resource: string]: string[] } = {};

    if (rolePolicy && rolePolicy.resources) {
      Object.entries(rolePolicy.resources).forEach(([resource, actions]) => {
        privileges[resource] = actions || [];
      });
    }

    // Construct the response
    const userProfileResponse: UserProfileResponse = {
      id: user.id,
      name: user.name,
      role: user.role,
      displayName: user.displayName,
      email: user.emailId,
      status: user.status,
      privileges: rolePolicy,
      tenant: {
        id: tenant!.id,
        name: tenant!.name,
        code: tenant!.code,
        subscriptionType: tenant!.subscriptionType,
        type: tenant!.type,
        status: tenant!.status,
        maxActiveUsers: tenant!.maxActiveUsers,
      },
    };
    return userProfileResponse;
  }
}
