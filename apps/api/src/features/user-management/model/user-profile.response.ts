import { RoleDefinition } from "@/config/policy/rules";
import {
  TenantStatus,
  TenantSubscriptionType,
  TenantType,
  UserRole,
  UserStatus,
} from "@repo/prisma/client";

export interface UserProfileResponse {
  id: string;
  name: string;
  role: UserRole;
  displayName: string;
  email: string;
  status: UserStatus;
  privileges: RoleDefinition;
  tenant: {
    id: string;
    name: string;
    code: string;
    subscriptionType: TenantSubscriptionType;
    type: TenantType;
    status: TenantStatus;
    maxActiveUsers: number;
  };
}
