import { UserRole, UserStatus } from "@repo/prisma/client";
import { Type } from "class-transformer";
import {
  IsArray,
  IsEnum,
  IsIn,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from "class-validator";

/**
 * User-specific filters
 */
export class UserFilters {
  @IsOptional()
  @IsString()
  name?: string; // String = substring search

  @IsOptional()
  @IsString()
  email?: string; // String = substring search

  @IsOptional()
  @IsArray()
  @IsEnum(UserRole, { each: true })
  role?: UserRole[]; // Array = IN operator

  @IsOptional()
  @IsArray()
  @IsEnum(UserStatus, { each: true })
  status?: UserStatus[]; // Array = IN operator
}

/**
 * Sorting configuration
 */
export class UserSort {
  @IsOptional()
  @IsString()
  @IsIn(["name", "emailId"])
  field?: string;

  @IsOptional()
  @IsString()
  @IsIn(["asc", "desc"])
  direction?: "asc" | "desc";
}

/**
 * User list request with pagination, filtering, and sorting
 */
export class UserListRequest {
  @IsOptional()
  @ValidateNested()
  @Type(() => UserFilters)
  filters?: UserFilters;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 20;

  @IsOptional()
  @IsString()
  cursor?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => UserSort)
  sort?: UserSort;
}
