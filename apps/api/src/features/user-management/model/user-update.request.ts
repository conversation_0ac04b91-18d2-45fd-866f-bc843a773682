import { UserR<PERSON> } from "@repo/prisma/client";
import { IsIn, IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";

export class UserUpdateRequest {
  @IsUUID()
  @IsNotEmpty()
  id!: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  name?: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  displayName?: string;

  @IsIn([UserRole.TenantAdmin, UserRole.ProjectHandler])
  @IsOptional()
  role?: UserRole;
}
