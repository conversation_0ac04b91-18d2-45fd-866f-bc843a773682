import { Type } from "class-transformer";
import {
  ArrayMaxSize,
  ArrayMinSize,
  Equals,
  IsArray,
  IsEmail,
  IsString,
  ValidateNested,
} from "class-validator";

export class ForgotPasswordRequest {
  @ValidateNested({ each: true })
  @Type(() => FormField)
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(1)
  formFields!: FormField[];
}

class FormField {
  @IsString()
  @Equals("email")
  id!: string;

  @IsEmail()
  value!: string;
}
