import { appConfig } from "@/config";
import { ForgotPasswordRequest } from "@/features/user-management/model/forgot-password.request";
import {
  getAuthUserMetadata,
  getResetPasswordToken,
  getUserDetailsByEmail,
} from "@/providers/auth.provider";
import { sendPasswordResetEmail } from "@/providers/email.provider";
import { logger } from "@/providers/logger.provider";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Post, Res } from "routing-controllers";
import { AuthUser } from "@/shared/model/auth.user";
@JsonController("/auth/user/password/reset/token")
export class UserForgotPasswordController {
  @Post("/")
  public async process(@Body() payload: ForgotPasswordRequest, @Res() res: Response) {
    // check if user exists in auth db. silently fail if not found
    // Not safe to send error message back to the user as it can lead to email enumeration attacks
    let authUser: AuthUser;
    const inputEmail = payload.formFields[0]!.value;
    try {
      authUser = await getUserDetailsByEmail(inputEmail);
    } catch (err) {
      logger.error(err, "User not found in auth db");
      return res.status(StatusCodes.OK).json({ status: "OK" });
    }

    // if user is verified, generate token and send reset password email
    // If not verified siliently fail.
    if (!authUser.isEmailVerified) {
      logger.error({ userId: authUser.id, email: authUser.email }, "User email not verified");
      return res.status(StatusCodes.OK).json({ status: "OK" });
    }

    const authUserMetadata = await getAuthUserMetadata(authUser.id);

    const response = await getResetPasswordToken(inputEmail, authUser.id);

    if (response.status === "OK") {
      const resetLink = `${appConfig.auth.websiteDomain}/auth/reset-password?token=${encodeURIComponent(response.token)}&rid=emailpassword`;
      sendPasswordResetEmail(inputEmail, authUserMetadata.name, resetLink);
    }

    return res.status(StatusCodes.OK).json(response);
  }
}
