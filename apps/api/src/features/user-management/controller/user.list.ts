import { UserListRequest } from "@/features/user-management/model/user-list.request";
import {
  UserListResponse,
  UserResponse,
} from "@/features/user-management/model/user-list.response";
import { dbClient } from "@/providers/db.provider";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import { Prisma } from "@repo/prisma/client";
import { Body, JsonController, Post, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { hasPermission } from "@/middleware/has-permission.middleware";
@JsonController("/users/find")
@UseBefore(hasPermission(Resource.User, [Action.Read]))
export class UserListController {
  @Post("/")
  public async process(@Body() payload: UserListRequest): Promise<BaseResponse<UserListResponse>> {
    logger.info(
      {
        filters: payload.filters,
        sort: payload.sort,
        limit: payload.limit,
        cursor: payload.cursor ? "present" : "none",
      },
      "Starting user list query"
    );

    // Build where clause from filters
    const whereClause = this.buildWhereClause(payload);

    // Build order by clause from sort
    const orderBy = this.buildOrderByClause(payload);

    // Build cursor-based pagination
    const cursorWhere = this.buildCursorClause(payload.cursor, payload.sort);

    // Set pagination parameters
    const limit = payload.limit!;
    const take = limit + 1; // Take one extra to check if there are more results

    logger.info(
      {
        whereClause,
        orderBy,
        cursorWhere,
        limit,
        take,
      },
      "Built query parameters for user listing"
    );

    // TODO temporary tenant filter. should be RLS
    const tenantWhere: Prisma.UserWhereInput = {
      tenantId: RequestContextHelper.getTenant()!.id!,
      isDeleted: false,
    };

    // Combine where clauses
    const finalWhere: Prisma.UserWhereInput = {
      AND: [tenantWhere, whereClause, cursorWhere].filter(
        (clause) => Object.keys(clause).length > 0
      ),
    };

    logger.info(
      {
        finalWhere,
      },
      "Executing user query"
    );

    const users = await dbClient().user.findMany({
      where: finalWhere,
      orderBy,
      take,
    });

    logger.info(
      {
        userCount: users.length,
        hasMore: users.length > limit,
      },
      "User query completed"
    );

    // Check if there are more results
    const hasMore = users.length > limit;
    if (hasMore) {
      users.pop(); // Remove the extra record
    }

    // Generate next cursor based on sort field
    const nextCursor =
      hasMore && users.length > 0
        ? this.generateCursor(users[users.length - 1]!, payload.sort)
        : undefined;

    logger.info(
      {
        hasMore,
        nextCursor,
        resultCount: users.length,
      },
      "Generating response with pagination info"
    );

    const usersResponse: UserResponse[] = users.map((user) => ({
      id: user.id,
      name: user.name,
      displayName: user.displayName,
      emailId: user.emailId,
      role: user.role,
      status: user.status,
    }));

    const response: UserListResponse = {
      users: usersResponse,
      nextCursor,
      hasMore,
    };

    logger.info(
      {
        totalResults: usersResponse.length,
        hasMore,
        nextCursor: nextCursor ? "present" : "none",
      },
      "User list query completed successfully"
    );

    return BaseResponse.success(response);
  }

  private buildWhereClause(payload: UserListRequest): Prisma.UserWhereInput {
    const where: Prisma.UserWhereInput = {};

    if (!payload.filters) {
      return where;
    }

    const { filters } = payload;

    // Name filter - simple substring match
    if (filters.name) {
      where.name = { contains: filters.name, mode: "insensitive" };
    }

    // Email filter - simple substring match
    if (filters.email) {
      where.emailId = { contains: filters.email, mode: "insensitive" };
    }

    // Role filter - IN operator
    if (filters.role && filters.role.length > 0) {
      where.role = { in: filters.role as any };
    }

    // Status filter - IN operator
    if (filters.status && filters.status.length > 0) {
      where.status = { in: filters.status as any };
    }

    return where;
  }

  private buildOrderByClause(payload: UserListRequest): Prisma.UserOrderByWithRelationInput[] {
    if (!payload.sort?.field) {
      return [{ createdAt: "desc" }]; // Default sort
    }
    const { field, direction = "asc" } = payload.sort;
    const dbField = field || "createdAt";
    return [{ [dbField]: direction }];
  }

  private buildCursorClause(
    cursor?: string,
    sort?: { field?: string; direction?: string }
  ): Prisma.UserWhereInput {
    if (!cursor) {
      return {};
    }

    try {
      const decodedCursor = JSON.parse(Buffer.from(cursor, "base64").toString());
      const { field = "createdAt", direction = "desc" } = sort || {};
      const dbField = field || "createdAt";
      const cursorValue = decodedCursor.value;
      const cursorId = decodedCursor.id;

      // For cursor-based pagination, we need to handle the direction
      if (direction === "asc") {
        return {
          OR: [
            { [dbField]: { gt: cursorValue } },
            {
              AND: [
                { [dbField]: { equals: cursorValue } },
                { id: { gt: cursorId } }, // Use id as tiebreaker
              ],
            },
          ],
        };
      } else {
        return {
          OR: [
            { [dbField]: { lt: cursorValue } },
            {
              AND: [
                { [dbField]: { equals: cursorValue } },
                { id: { lt: cursorId } }, // Use id as tiebreaker
              ],
            },
          ],
        };
      }
    } catch (error) {
      // Invalid cursor, ignore it
      return {};
    }
  }

  private generateCursor(user: any, sort?: { field?: string; direction?: string }): string {
    const { field = "createdAt" } = sort || {};

    // Map field names to actual database fields
    const dbField = field || "createdAt";
    const cursorData = {
      value: user[dbField],
      id: user.id, // Include id as tiebreaker
    };

    return Buffer.from(JSON.stringify(cursorData)).toString("base64");
  }
}
