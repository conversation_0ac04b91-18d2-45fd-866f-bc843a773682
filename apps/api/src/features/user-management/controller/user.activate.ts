import { appConfig } from "@/config";
import { BaseError } from "@/exception-handling/base.error";
import { UserService } from "@/features/user-management/service/user.service";
import { getEmailVerificationToken, signUpUsingEmailPassword } from "@/providers/auth.provider";
import { sendVerificationEmail } from "@/providers/email.provider";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import { UserStatus } from "@repo/prisma/client";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { JsonController, Param, Patch, Req, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { UserClaims, mapToUserClaims } from "@/shared/model/auth.user-claims";
import { TenantService } from "@/features/tenant-management/service/tenant.service";

@JsonController("/user")
@UseBefore(hasPermission(Resource.User, [Action.Create]))
export class UserActivateController {
  @Patch("/:userId/activate")
  public async process(@Param("userId") userId: string, @Req() req: Request, @Res() res: Response) {
    // If its a self activate, throw error
    if (RequestContextHelper.getUser()!.id === userId) {
      throw new BaseError(
        "INVALID_REQUEST",
        "You cannot activate yourself",
        StatusCodes.BAD_REQUEST,
        {}
      );
    }

    const userProfile = await UserService.getUserById(userId);
    if (!userProfile || userProfile!.status !== UserStatus.Inactive) {
      logger.error(
        {
          userId: userId,
        },
        "User not found or the status is not inactive. Cant activate an active user"
      );
      throw new BaseError("USER_NOT_FOUND", "user not found", StatusCodes.NOT_FOUND, {});
    }

    const tenantDetails = await TenantService.getTenantById(userProfile.tenant.id, true);
    // check if the active and pending verification count has already reached the limit of maxusers. if yes, throw error.
    const activeAndPendingUserCount = tenantDetails.users.filter(
      (user) => user.status === UserStatus.Active || user.status === UserStatus.PendingVerification
    ).length;

    if (activeAndPendingUserCount >= userProfile.tenant.maxActiveUsers) {
      throw new BaseError(
        "MAX_USERS_REACHED",
        "Max users reached. Cannot activate more users",
        StatusCodes.BAD_REQUEST,
        {}
      );
    }

    const updatedUserProfile = await UserService.updateUserStatus(
      userId,
      UserStatus.PendingVerification
    );
    const userClaims: UserClaims = mapToUserClaims(updatedUserProfile!);

    await signUpUsingEmailPassword(userClaims, crypto.randomUUID());
    const token = await getEmailVerificationToken(userProfile.email);

    const verificationLink = `${appConfig.auth.websiteDomain}/auth/verify-email?token=${encodeURIComponent(token)}&rid=emailverification`;
    sendVerificationEmail(userProfile.email, userProfile.name, verificationLink);

    return res.status(StatusCodes.OK).json(BaseResponse.success());
  }
}
