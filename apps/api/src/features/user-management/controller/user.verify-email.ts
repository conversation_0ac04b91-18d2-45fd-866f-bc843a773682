import { EmailVerificationRequest } from "@/features/user-management/model/email-verify.request";
import {
  getAuthUserMetadata,
  getResetPasswordToken,
  rollbackEmailVerification,
  verifyEmailByToken,
} from "@/providers/auth.provider";
import { AuthUser } from "@/shared/model/auth.user";
import { UserStatus } from "@repo/prisma/client";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Post, Res } from "routing-controllers";
import { UserService } from "@/features/user-management/service/user.service";
import { UserClaims, mapToUserClaims } from "@/shared/model/auth.user-claims";
import { updateAuthUser } from "@/providers/auth.provider";
import { logger } from "@/providers/logger.provider";
@JsonController("/auth/user/email/verify")
export class UserVerifyEmailController {
  @Post("/")
  public async process(@Body() payload: EmailVerificationRequest, @Res() res: Response) {
    const response: AuthUser = await verifyEmailByToken(payload.token);

    try {
      const authUserMetadata = await getAuthUserMetadata(response.id);
      const updatedUser = await UserService.updateUserStatus(
        authUserMetadata.id,
        UserStatus.Active
      );
      const userClaims: UserClaims = mapToUserClaims(updatedUser!);

      await updateAuthUser(userClaims);

      const tokenResp = await getResetPasswordToken(response.email, response.id);
      return res.status(StatusCodes.OK).json(tokenResp);
    } catch (err) {
      logger.error(err, "Failed to verify user. Rolling back email verification");
      await rollbackEmailVerification(response.id, response.email);
      return res.status(StatusCodes.OK).json({ status: "OK" });
    }
  }
}
