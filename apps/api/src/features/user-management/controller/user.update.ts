import { BaseError } from "@/exception-handling/base.error";
import { UserUpdateRequest } from "@/features/user-management/model/user-update.request";
import { UserService } from "@/features/user-management/service/user.service";
import { updateAuthUser } from "@/providers/auth.provider";
import { RequestContextHelper } from "@/server/context";
import { mapToUserClaims, UserClaims } from "@/shared/model/auth.user-claims";
import { BaseResponse } from "@/shared/model/base.response";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Patch, Req, Res } from "routing-controllers";
import { UserRole, UserStatus } from "@repo/prisma/client";
import { TenantService } from "@/features/tenant-management/service/tenant.service";
import { logger } from "@/providers/logger.provider";
import { dbClient, withTransaction } from "@/providers/db.provider";
@JsonController("/user")
export class UserUpdateController {
  @Patch("/")
  public async process(
    @Body() payload: UserUpdateRequest,
    @Req() req: Request,
    @Res() res: Response
  ) {
    if (RequestContextHelper.getUser()!.role === UserRole.ProjectHandler) {
      if (RequestContextHelper.getUser()!.id !== payload.id) {
        throw new BaseError(
          "INVALID_REQUEST",
          "Project handlers cannot update other users",
          StatusCodes.BAD_REQUEST,
          {}
        );
      }
    }

    // If its a self update, role cant be updated
    if (RequestContextHelper.getUser()!.id === payload.id) {
      if (payload.role) {
        throw new BaseError(
          "INVALID_REQUEST",
          "You cannot change your own role",
          StatusCodes.BAD_REQUEST,
          {}
        );
      }
    }
    const userProfile = await UserService.getUserById(payload.id);

    if (!userProfile || userProfile!.status !== UserStatus.Active) {
      logger.error(
        {
          userId: payload.id,
        },
        "User not found or the status is not active. system cant update inactive or pending verification users"
      );
      throw new BaseError("USER_NOT_FOUND", "User not found", StatusCodes.NOT_FOUND, {});
    }

    const result = await withTransaction(async (tx) => {
      const updatedUser = await UserService.updateUser(payload, tx);

      const tenantDetails = await TenantService.getTenantById(
        RequestContextHelper.getTenant()!.id!,
        true,
        tx
      );

      const activeTenantAdminUsers = tenantDetails.users.filter(
        (user) => user.status === UserStatus.Active && user.role === UserRole.TenantAdmin
      );

      if (activeTenantAdminUsers.length === 0) {
        logger.error(
          {
            tenantId: RequestContextHelper.getTenant()!.id!,
          },
          "At least one active tenant admin user is required for a tenant"
        );
        throw new BaseError(
          "NO_ACTIVE_TENANT_ADMIN",
          "No active tenant admin users found",
          StatusCodes.BAD_REQUEST,
          {}
        );
      }
      return {
        updatedUser,
      };
    });

    const userClaims: UserClaims = mapToUserClaims(result.updatedUser!);

    await updateAuthUser(userClaims);

    return res.status(StatusCodes.OK).json(BaseResponse.success());
  }
}
