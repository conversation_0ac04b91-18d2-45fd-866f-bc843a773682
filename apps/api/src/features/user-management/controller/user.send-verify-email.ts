import { appConfig } from "@/config";
import { BaseError } from "@/exception-handling/base.error";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { getEmailVerificationToken } from "@/providers/auth.provider";
import { sendVerificationEmail } from "@/providers/email.provider";
import { logger } from "@/providers/logger.provider";
import { BaseResponse } from "@/shared/model/base.response";
import { UserStatus } from "@repo/prisma/client";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { JsonController, Param, Post, Req, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { UserService } from "@/features/user-management/service/user.service";

@JsonController("/user")
@UseBefore(hasPermission(Resource.User, [Action.Update]))
export class UserSendVerificationEmailController {
  @Post("/:userId/send-verification-email")
  public async process(@Param("userId") userId: string, @Req() req: Request, @Res() res: Response) {
    const userProfile = await UserService.getUserById(userId);

    if (!userProfile || userProfile.status !== UserStatus.PendingVerification) {
      logger.error(
        {
          userId: userId,
        },
        "No user found with the given id and status pending verification."
      );
      throw new BaseError("USER_NOT_FOUND", "User not found", StatusCodes.NOT_FOUND, {});
    }

    const token = await getEmailVerificationToken(userProfile.email);

    const verificationLink = `${appConfig.auth.websiteDomain}/auth/verify-email?token=${encodeURIComponent(token)}&rid=emailverification`;
    sendVerificationEmail(userProfile.email, userProfile.name, verificationLink);

    return res.status(StatusCodes.OK).json(BaseResponse.ok());
  }
}
