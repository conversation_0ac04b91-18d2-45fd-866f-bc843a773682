import { BaseError } from "@/exception-handling/base.error";
import { UserService } from "@/features/user-management/service/user.service";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { deactivateUser } from "@/providers/auth.provider";
import { sendUserDeactivatedEmail } from "@/providers/email.provider";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import { UserRole, UserStatus } from "@repo/prisma/client";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { JsonController, Param, Patch, Req, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { TenantService } from "@/features/tenant-management/service/tenant.service";
@JsonController("/user")
@UseBefore(hasPermission(Resource.User, [Action.Delete]))
export class UserDeactivateController {
  @Patch("/:userId/deactivate")
  public async process(@Param("userId") userId: string, @Req() req: Request, @Res() res: Response) {
    logger.info(
      {
        targetUserId: userId,
      },
      "Starting user deactivation process"
    );

    logger.info(
      {
        targetUserId: userId,
        authenticatedUserId: RequestContextHelper.getUser()!.id,
      },
      "Checking if user is trying to deactivate themselves"
    );

    // If its a self deactivate, throw error
    if (RequestContextHelper.getUser()!.id === userId) {
      logger.error(
        {
          targetUserId: userId,
          authenticatedUserId: RequestContextHelper.getUser()!.id,
        },
        "User attempted to deactivate themselves"
      );
      throw new BaseError(
        "INVALID_REQUEST",
        "You cannot deactivate yourself",
        StatusCodes.BAD_REQUEST,
        {}
      );
    }

    logger.info(
      {
        targetUserId: userId,
      },
      "Fetching user profile for deactivation"
    );

    const userProfile = await UserService.getUserById(userId);
    if (!userProfile || userProfile.status === UserStatus.Inactive) {
      logger.error(
        {
          targetUserId: userId,
        },
        "Active user not found for deactivation"
      );
      throw new BaseError("USER_NOT_FOUND", "Active user not found", StatusCodes.NOT_FOUND, {});
    }

    logger.info(
      {
        targetUserId: userId,
        userEmail: userProfile.email,
        userRole: userProfile.role,
        tenantId: userProfile.tenant.id,
      },
      "User found, checking role-specific constraints"
    );

    // check how many active and pending verification admin users exist for this tenant. if there is only one, and we are deactivating it, throw error.
    if (userProfile.role === UserRole.TenantAdmin) {
      logger.info(
        {
          targetUserId: userId,
          tenantId: userProfile.tenant.id,
        },
        "User is TenantAdmin, checking if this is the last admin user"
      );

      const tenantDetails = await TenantService.getTenantById(userProfile.tenant.id, true);
      const activeAndPendingUsers = tenantDetails.users.filter(
        (user) =>
          (user.status === UserStatus.Active || user.status === UserStatus.PendingVerification) &&
          user.role === UserRole.TenantAdmin
      );

      logger.info(
        {
          targetUserId: userId,
          tenantId: userProfile.tenant.id,
          activeAdminCount: activeAndPendingUsers.length,
        },
        "Found active admin users for tenant"
      );

      if (
        activeAndPendingUsers &&
        activeAndPendingUsers.length === 1 &&
        activeAndPendingUsers[0]!.id === userId
      ) {
        logger.error(
          {
            targetUserId: userId,
            tenantId: userProfile.tenant.id,
          },
          "Cannot deactivate last admin user for tenant"
        );
        throw new BaseError(
          "LAST_ADMIN_USER",
          "Cannot deactivate last admin user",
          StatusCodes.BAD_REQUEST,
          {}
        );
      }
    }

    logger.info(
      {
        targetUserId: userId,
        authenticatedUserId: RequestContextHelper.getUser()!.id,
      },
      "Deactivating user in database"
    );

    const updatedUserProfile = await UserService.updateUserStatus(userId, UserStatus.Inactive);

    logger.info(
      {
        targetUserId: userId,
        userEmail: userProfile.email,
      },
      "Deactivating user in auth system"
    );

    await deactivateUser(userProfile.email);

    logger.info(
      {
        targetUserId: userId,
        userEmail: userProfile.email,
        userName: userProfile.name,
      },
      "Sending deactivation notification email"
    );

    sendUserDeactivatedEmail(userProfile.email, userProfile.name);

    logger.info(
      {
        targetUserId: userId,
        userEmail: userProfile.email,
        userName: userProfile.name,
        userRole: userProfile.role,
      },
      "User deactivation completed successfully"
    );

    return res.status(StatusCodes.OK).json(BaseResponse.success());
  }
}
