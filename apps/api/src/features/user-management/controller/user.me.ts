import { BaseError } from "@/exception-handling/base.error";
import { UserService } from "@/features/user-management/service/user.service";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import { TenantStatus, UserStatus } from "@repo/prisma/client";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Get, JsonController, Req, Res } from "routing-controllers";

@JsonController("/user/me")
export class UserProfileController {
  @Get("/")
  public async process(@Req() req: Request, @Res() res: Response) {
    logger.info("Getting user profile");
    const userProfile = await UserService.getUserById(RequestContextHelper.getUser()!.id);

    if (!userProfile) {
      throw new BaseError("USER_NOT_FOUND", "User not found", StatusCodes.NOT_FOUND, {});
    }

    if (
      userProfile.status !== UserStatus.Active ||
      userProfile.tenant.status !== TenantStatus.Active
    ) {
      logger.error(
        {
          userId: userProfile.id,
          email: userProfile.email,
          tenantId: userProfile.tenant.id,
        },
        "User or tenant is not active"
      );
      throw new BaseError("INVALID_USER", "Invalid User", StatusCodes.FORBIDDEN, {});
    }
    return res
      .status(StatusCodes.OK)
      .json(BaseResponse.success(userProfile, "User profile retrieved successfully"));
  }
}
