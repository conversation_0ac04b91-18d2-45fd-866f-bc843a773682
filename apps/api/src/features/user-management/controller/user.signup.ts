import { appConfig } from "@/config";
import { BaseError } from "@/exception-handling/base.error";
import { UserSignupRequest } from "@/features/user-management/model/user-create.request";
import { hasPermission } from "@/middleware/has-permission.middleware";
import { getEmailVerificationToken, signUpUsingEmailPassword } from "@/providers/auth.provider";
import { sendVerificationEmail } from "@/providers/email.provider";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { UserClaims, mapToUserClaims } from "@/shared/model/auth.user-claims";
import { BaseResponse } from "@/shared/model/base.response";
import { UserStatus, TenantStatus } from "@repo/prisma/client";
import type { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Body, JsonController, Post, Res, UseBefore } from "routing-controllers";
import { Resource, Action } from "@/config/policy/rules";
import { AuthUser } from "@/shared/model/auth.user";
import { UserService } from "@/features/user-management/service/user.service";
import { TenantService } from "@/features/tenant-management/service/tenant.service";
@JsonController("/user")
@UseBefore(hasPermission(Resource.User, [Action.Create]))
export class UserSignupController {
  @Post("/")
  public async process(
    @Body() payload: UserSignupRequest,
    @Res() res: Response
  ): Promise<Response> {
    logger.info(
      {
        userEmail: payload.emailId,
        userName: payload.name,
        userRole: payload.role,
      },
      "Starting user signup process"
    );

    const tenantDetails = await TenantService.getTenantById(
      RequestContextHelper.getTenant()!.id!,
      true
    );

    if (!tenantDetails || tenantDetails!.status !== TenantStatus.Active) {
      logger.error(
        {
          userEmail: payload.emailId,
          tenantId: RequestContextHelper.getTenant()!.id!,
        },
        "Tenant not found or the status is not active."
      );
      throw new BaseError("TENANT_NOT_FOUND", "tenant not found", StatusCodes.NOT_FOUND, {});
    }

    const activeAndPendingUserCount = tenantDetails.users.filter(
      (user) => user.status === UserStatus.Active || user.status === UserStatus.PendingVerification
    ).length;

    if (activeAndPendingUserCount >= tenantDetails.maxActiveUsers!) {
      throw new BaseError(
        "MAX_USERS_REACHED",
        "Max users reached. Cannot activate more users",
        StatusCodes.BAD_REQUEST,
        {}
      );
    }

    const userProfile = await UserService.createUser(payload);
    logger.info(
      {
        userId: userProfile!.id,
        userEmail: userProfile!.email,
        tenantId: userProfile!.tenant.id,
      },
      "User created successfully in database"
    );

    logger.info(
      {
        userId: userProfile!.id,
        userEmail: userProfile!.email,
      },
      "Registering user in auth system"
    );
    const userClaims: UserClaims = mapToUserClaims(userProfile!);
    const authUser: AuthUser = await signUpUsingEmailPassword(userClaims, crypto.randomUUID());

    logger.info(
      {
        userId: userProfile!.id,
        userEmail: userProfile!.email,
      },
      "Generating verification token and sending email"
    );

    const token = await getEmailVerificationToken(payload.emailId);

    const verificationLink = `${appConfig.auth.websiteDomain}/auth/verify-email?token=${encodeURIComponent(token)}&rid=emailverification`;
    sendVerificationEmail(payload.emailId, payload.name, verificationLink);

    logger.info(
      {
        userId: userProfile!.id,
        userEmail: userProfile!.email,
        userRole: userProfile!.role,
        tenantId: userProfile!.tenant.id,
      },
      "User signup completed successfully"
    );

    return res.status(StatusCodes.OK).json(BaseResponse.ok());
  }
}
