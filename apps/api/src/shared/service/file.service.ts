import { BaseError } from "@/exception-handling/base.error";
import { blo<PERSON><PERSON><PERSON><PERSON>, BlobUploadResult } from "@/providers/blob.provider";
import busboy from "busboy";
import type { Request } from "express";
import { StatusCodes } from "http-status-codes";
import { Readable } from "stream";
import { FileServiceUploadResponse } from "@/features/project-management/model/file-upload.response";
import { logger } from "@/providers/logger.provider";
import { appConfig } from "@/config";

class FileService {
  public async streamUpload(
    req: Request,
    pathPrefix: string,
    allowedTypes: string[]
  ): Promise<FileServiceUploadResponse> {
    const fileMetadata: Record<string, string> = {};
    const abortController = new AbortController();
    let uploadCompleted = false;

    const bb = busboy({
      headers: req.headers,
      limits: {
        fileSize: appConfig.blob.maxFileSize,
        files: 1, // Only allow 1 file
        fields: 1, // Only allow 1 field (metadata)
      },
    });

    try {
      // Promise to handle the upload process
      const uploadPromise = new Promise<any>((resolve, reject) => {
        let hasError = false;

        // Handle form fields
        bb.on("field", (name: string, value: string, info: busboy.FieldInfo) => {
          logger.info(
            {
              name,
              value,
              info,
            },
            "Field received"
          );

          if (hasError) return;

          if (name !== "metadata") {
            hasError = true;
            reject(
              new BaseError(
                "INVALID_REQUEST",
                "Only metadata field is allowed",
                StatusCodes.BAD_REQUEST,
                {}
              )
            );
          }

          if (!value || value.trim() === "") {
            hasError = true;
            reject(
              new BaseError("INVALID_REQUEST", "Metadata is required", StatusCodes.BAD_REQUEST, {})
            );
          }

          try {
            const metadata = JSON.parse(value);
            if (metadata && Object.keys(metadata).length > 0) {
              Object.keys(metadata).forEach((key: string) => {
                fileMetadata[key] = metadata[key];
              });
            }
          } catch (error) {
            hasError = true;
            reject(
              new BaseError(
                "INVALID_REQUEST",
                "Metadata is not a valid JSON",
                StatusCodes.BAD_REQUEST,
                {}
              )
            );
          }

          if (fileMetadata && Object.keys(fileMetadata).length === 0) {
            hasError = true;
            reject(
              new BaseError("INVALID_REQUEST", "Metadata is required", StatusCodes.BAD_REQUEST, {})
            );
          }

          if (!fileMetadata.path || fileMetadata.batchNumber === "") {
            hasError = true;
            reject(
              new BaseError(
                "INVALID_REQUEST",
                "Path and Batch Number is required",
                StatusCodes.BAD_REQUEST,
                {}
              )
            );
          }
        });

        // Handle file upload
        bb.on("file", async (name: string, file: Readable, info: busboy.FileInfo) => {
          if (hasError) {
            file.resume(); // Drain the stream
            return;
          }

          try {
            file.on("error", (err) => {
              hasError = true;
              file.resume(); // drain to unblock Busboy
              reject(err);
            });

            // Check for file size limit exceeded when stream closes
            file.on("close", () => {
              if ((file as any).truncated) {
                hasError = true;
                uploadCompleted = true; // Mark as completed to prevent req.close from aborting
                // Cancel the Azure upload
                abortController.abort();
                logger.warn(
                  {
                    filename,
                    mimeType,
                  },
                  "File size limit exceeded, cancelling Azure upload"
                );
                reject(
                  new BaseError(
                    "FILE_SIZE_LIMIT_EXCEEDED",
                    "File size exceeds the maximum allowed limit",
                    StatusCodes.BAD_REQUEST,
                    {}
                  )
                );
              }
            });

            const { filename, mimeType } = info;

            // Validate file
            if (!filename) {
              hasError = true;
              file.resume();
              reject(
                new BaseError(
                  "INVALID_REQUEST",
                  "Filename is required",
                  StatusCodes.BAD_REQUEST,
                  {}
                )
              );
              return;
            }

            // Validate MIME type if needed
            if (!allowedTypes.includes(mimeType)) {
              hasError = true;
              file.resume();
              reject(
                new BaseError("INVALID_FILE_TYPE", "Invalid file type", StatusCodes.BAD_REQUEST, {})
              );

              return;
            }

            const blobName =
              pathPrefix +
              "/" +
              this.sanitizePath(fileMetadata.path!) +
              "/" +
              this.sanitizeFileName(filename);
            const uploadResponse: BlobUploadResult = await blobProvider.uploadStream(file, {
              blobName: blobName,
              contentType: mimeType,
              metadata: fileMetadata,
              tags: {},
              overwrite: true,
              abortSignal: abortController.signal,
            });

            uploadCompleted = true;
            resolve({
              success: true,
              blob: uploadResponse,
              metadata: {
                ...fileMetadata,
                originalFileName: filename,
                contentType: mimeType,
              },
            });
          } catch (error) {
            hasError = true;
            uploadCompleted = true; // Mark as completed to prevent req.close from aborting
            file.resume(); // Drain the stream

            // Check if the error is due to abort
            if (error instanceof Error && error.name === "AbortError") {
              logger.info("Azure upload cancelled due to file size limit");
              // Don't reject here as the file size limit error will be handled in the close event
              return;
            }

            reject(error);
          }
        });

        // Handle file size limit error
        bb.on("filesLimit", () => {
          hasError = true;
          uploadCompleted = true;
          reject(
            new BaseError(
              "FILES_LIMIT_EXCEEDED",
              "Too many files uploaded. 1 file allowed.",
              StatusCodes.BAD_REQUEST,
              {}
            )
          );
        });

        bb.on("fieldsLimit", () => {
          hasError = true;
          uploadCompleted = true;
          reject(
            new BaseError(
              "INVALID_REQUEST",
              "Too many fields uploaded",
              StatusCodes.BAD_REQUEST,
              {}
            )
          );
        });

        // Handle completion
        bb.on("finish", () => {
          if (hasError) return;
          uploadCompleted = true;
        });

        // Handle errors
        bb.on("error", (error: Error) => {
          hasError = true;
          uploadCompleted = true;
          reject(error);
        });
      });

      // Pipe request to busboy
      req.pipe(bb);

      // Handle request abort/close - only cancel if upload hasn't completed
      req.on("close", () => {
        if (!uploadCompleted && !abortController.signal.aborted) {
          logger.warn("Client disconnected before upload completed, cancelling Azure upload");
          abortController.abort();
        }
      });

      // Wait for upload to complete
      const result = await uploadPromise;

      // Return successful response
      return result;
    } catch (error) {
      if (error instanceof BaseError) {
        throw error;
      }

      // Unknown error
      throw new BaseError(
        "FILE_UPLOAD_FAILED",
        "Upload failed",
        StatusCodes.INTERNAL_SERVER_ERROR,
        error
      );
    }
  }

  /**
   * Sanitize file path to prevent directory traversal and ensure valid format
   */
  private sanitizePath(filePath: string): string {
    if (!filePath) return "";

    return filePath
      .trim()
      .replace(/[<>:"|?*]/g, "") // Remove invalid characters
      .replace(/\.{2,}/g, ".") // Replace multiple dots with single dot
      .replace(/^[./]+|[./]+$/g, "") // Remove leading/trailing dots and slashes
      .replace(/\/+/g, "/"); // Replace multiple slashes with single slash
  }

  /**
   * Sanitize filename to ensure valid format
   */
  private sanitizeFileName(fileName: string): string {
    if (!fileName) return "";

    return fileName
      .trim()
      .replace(/[<>:"/\\|?*]/g, "") // Remove invalid characters
      .replace(/\.{2,}/g, ".") // Replace multiple dots with single dot
      .replace(/^[.]+|[.]+$/g, ""); // Remove leading/trailing dots
  }
}

export const fileService = new FileService();
