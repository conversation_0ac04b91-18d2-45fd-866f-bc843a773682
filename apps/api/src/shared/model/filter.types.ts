import { Type } from "class-transformer";
import { IsN<PERSON>ber, IsOptional } from "class-validator";

/**
 * Range filter - min/max values
 */
export class RangeFilter {
  @IsOptional()
  @IsNumber()
  min?: number;

  @IsOptional()
  @IsNumber()
  max?: number;
}

/**
 * Date range filter - min/max dates
 */
export class DateRangeFilter {
  @IsOptional()
  @Type(() => Date)
  min?: Date;

  @IsOptional()
  @Type(() => Date)
  max?: Date;
}
