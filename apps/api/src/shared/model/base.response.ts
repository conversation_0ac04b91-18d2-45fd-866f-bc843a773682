import { RequestContextHelper } from "@/server/context";
import { ReasonPhrases } from "http-status-codes";

export class BaseResponse<T = any> {
  constructor(
    public readonly success: boolean,
    public readonly data?: T,
    public readonly message?: string,
    public readonly error?: {
      code: string;
      message: string;
    },
    public readonly meta?: Record<string, any>
  ) {}

  public static ok(): BaseResponse {
    return new BaseResponse(true, undefined, ReasonPhrases.OK, undefined, {
      requestId: RequestContextHelper.getRequestId()!,
      timestamp: new Date().toISOString(),
    });
  }

  public static success<T>(data?: T, message?: string): BaseResponse<T> {
    return new BaseResponse(true, data, message || ReasonPhrases.OK, undefined, {
      requestId: RequestContextHelper.getRequestId()!,
      timestamp: new Date().toISOString(),
    });
  }

  public static error(code: string, message: string, meta?: Record<string, any>): BaseResponse {
    return new BaseResponse(false, undefined, undefined, { code, message }, meta);
  }
}
