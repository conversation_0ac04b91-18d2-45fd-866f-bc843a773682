import { UserRole } from "@repo/prisma/client";

// Define all possible actions
export enum Action {
  Create = "create",
  Read = "read",
  Update = "update",
  Delete = "delete",
}

// Define all possible resources
export enum Resource {
  Tenant = "tenant",
  User = "user",
  Glossary = "glossary",
  GlossaryItem = "glossary-item",
  Project = "project",
}

// Mapping of resources to their allowed actions
export type ResourcePermissions = {
  [key in Resource]?: Action[];
};

// Role definition: each role has a set of resource permissions
export interface RoleDefinition {
  resources: ResourcePermissions;
}

// Roles mapping
export type RolesMap = {
  [key in UserRole]?: RoleDefinition;
};

// Tenant-level configuration
export interface TenantRoleConfig {
  roles: RolesMap;
}

// Entire permission config
export interface PermissionConfig {
  default: TenantRoleConfig;
  [tenantId: string]: TenantRoleConfig; // other tenants like "3453fd0f-f047-4a75-8066-60202e0aea62"
}

export const policy_rules: PermissionConfig = {
  default: {
    roles: {
      [UserRole.SuperAdmin]: {
        resources: {
          [Resource.Tenant]: [Action.Create, Action.Read, Action.Update, Action.Delete],
          [Resource.User]: [Action.Create, Action.Read, Action.Update, Action.Delete],
          [Resource.Glossary]: [Action.Create, Action.Read, Action.Update, Action.Delete],
          [Resource.GlossaryItem]: [Action.Create, Action.Read, Action.Update, Action.Delete],
          [Resource.Project]: [Action.Create, Action.Read, Action.Update, Action.Delete],
        },
      },
      [UserRole.TenantAdmin]: {
        resources: {
          [Resource.User]: [Action.Create, Action.Read, Action.Update, Action.Delete],
          [Resource.Glossary]: [Action.Read],
          [Resource.GlossaryItem]: [Action.Create, Action.Read, Action.Update, Action.Delete],
          [Resource.Project]: [Action.Create, Action.Read, Action.Update, Action.Delete],
        },
      },
      [UserRole.ProjectHandler]: {
        resources: {
          [Resource.Glossary]: [Action.Read],
          [Resource.GlossaryItem]: [Action.Read],
          [Resource.Project]: [Action.Create, Action.Read, Action.Update, Action.Delete],
        },
      },
    },
  },
  "11111111-f047-4a75-8066-60202e0aea62": {
    roles: {
      [UserRole.TenantAdmin]: {
        resources: {
          [Resource.User]: [Action.Create, Action.Read, Action.Update, Action.Delete],
        },
      },
      [UserRole.ProjectHandler]: {
        resources: {},
      },
    },
  },
};
