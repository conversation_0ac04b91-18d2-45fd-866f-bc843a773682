import { Transform, Type } from "class-transformer";
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsO<PERSON>al,
  IsString,
  IsUrl,
  <PERSON>,
  <PERSON>,
  <PERSON>idateNested,
  validateSync,
} from "class-validator";
import dotenv from "dotenv";
import "reflect-metadata";

// Load environment variables
dotenv.config();

export enum API_ENVIRONMENT {
  DEVELOPMENT = "development",
  PRODUCTION = "production",
  TEST = "test",
  STAGING = "staging",
}
export class DatabaseConfig {
  @IsString()
  user!: string;

  @IsString()
  password!: string;

  @IsString()
  host!: string;

  @IsNumber()
  @Min(1)
  @Max(65535)
  @Transform(({ value }) => parseInt(value, 10))
  port!: number;

  @IsString()
  database!: string;

  @IsString()
  schema!: string;
}

export class EmailConfig {
  @IsString()
  @IsNotEmpty()
  connectionString!: string;

  @IsEmail()
  @IsNotEmpty()
  senderEmail!: string;
}

export class AuthConfig {
  @IsUrl({ require_tld: false })
  websiteDomain!: string;

  @IsUrl({ require_tld: false })
  serverDomain!: string;

  @IsUrl({ require_tld: false })
  connectionURI!: string;
}

export class ServerConfig {
  @IsNumber()
  @Min(1)
  @Max(65535)
  @Transform(({ value }) => parseInt(value, 10))
  port!: number;

  @IsEnum(API_ENVIRONMENT)
  environment!: API_ENVIRONMENT;
}

export class BlobConfig {
  @IsString()
  @IsNotEmpty()
  connectionString!: string;

  @IsString()
  @IsNotEmpty()
  rootFolderName!: string;

  @IsNumber()
  @IsOptional()
  maxFileSize?: number = 100 * 1024 * 1024; // 100 MB
}
export class AppConfig {
  @ValidateNested()
  @Type(() => ServerConfig)
  server!: ServerConfig;

  @ValidateNested()
  @Type(() => AuthConfig)
  auth!: AuthConfig;

  @ValidateNested()
  @Type(() => DatabaseConfig)
  database!: DatabaseConfig;

  @ValidateNested()
  @Type(() => EmailConfig)
  email!: EmailConfig;

  @ValidateNested()
  @Type(() => BlobConfig)
  blob!: BlobConfig;
  constructor() {
    // Database configuration
    this.database = new DatabaseConfig();
    this.database.user = process.env.API_POSTGRESQL_USER!;
    this.database.password = process.env.API_POSTGRESQL_PASSWORD!;
    this.database.host = process.env.API_POSTGRESQL_HOST!;
    this.database.port = Number(process.env.API_POSTGRESQL_PORT);
    this.database.database = process.env.API_POSTGRESQL_DATABASE_NAME!;
    this.database.schema = process.env.API_POSTGRESQL_TABLE_SCHEMA!;

    // Email configuration
    this.email = new EmailConfig();
    this.email.connectionString = process.env.API_MAIL_SERVER_CONNECTION_STRING!;
    this.email.senderEmail = process.env.API_MAIL_SENDER_ADDRESS!;

    // Auth configuration
    this.auth = new AuthConfig();
    this.auth.websiteDomain = process.env.API_AUTH_WEBSITE_DOMAIN!;
    this.auth.serverDomain = process.env.API_AUTH_SERVER_DOMAIN!;
    this.auth.connectionURI = process.env.API_AUTH_SERVER_CONNECTION_URI!;

    // Server configuration
    this.server = new ServerConfig();
    this.server.port = Number(process.env.API_SERVER_PORT);
    this.server.environment = process.env.API_SERVER_NODE_ENV! as API_ENVIRONMENT;

    this.blob = new BlobConfig();
    this.blob.connectionString = process.env.APP_BLOB_STORAGE_CONNECTION_STRING!;
    this.blob.rootFolderName = process.env.APP_BLOB_STORAGE_FOLDER_NAME!;
    if (process.env.APP_BLOB_STORAGE_MAX_FILE_SIZE) {
      this.blob.maxFileSize = Number(process.env.APP_BLOB_STORAGE_MAX_FILE_SIZE) * 1024 * 1024;
    }

    // Validate the configuration
    this.validate();
  }

  private validate(): void {
    const errors = validateSync(this, { skipMissingProperties: false });

    if (errors.length > 0) {
      throw new Error(`Application configuration validation failed: ${errors}`);
    }
  }
}

// Export singleton instance
export const appConfig = new AppConfig();
