import { logger } from "@/providers/logger.provider";
import type { NextFunction, Request, Response } from "express";

/**
 * Fields that contain PII data and should be masked in logs
 */
const PII_FIELDS = [
  // Password fields
  "password",
  "currentPassword",
  "newPassword",
  "confirmPassword",
  "oldPassword",

  // Tokens and sensitive data
  "token",
  "accessToken",
  "refreshToken",
  "sAccessToken",
  "sRefreshToken",
  "apiKey",
  "secret",
  "privateKey",
  "sessionId",
];

function maskPIIData(obj: any, depth = 0, visited = new WeakSet()): any {
  if (depth > 10) {
    return "[MAX_DEPTH_REACHED]";
  }

  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === "number" || typeof obj === "boolean") {
    return obj;
  }

  if (typeof obj === "string") {
    return obj;
  }

  if (obj instanceof Date) {
    return obj.toISOString();
  }

  if (obj instanceof RegExp) {
    return obj.toString();
  }

  if (typeof obj === "object" && visited.has(obj)) {
    return "[CIRCULAR_REFERENCE]";
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => maskPIIData(item, depth + 1, visited));
  }

  if (typeof obj === "object") {
    visited.add(obj);

    // ✅ NEW: Check if this object has an id/name/key field indicating PII
    const contextualPIIIndicator = detectContextualPII(obj);

    const masked: any = {};

    for (const [key, value] of Object.entries(obj)) {
      const isDirectPIIField = checkIfPIIField(key);

      // ✅ NEW: Check if this is a "value" field and context indicates PII
      const isContextualPIIValue =
        (key === "value" || key === "data" || key === "content") && contextualPIIIndicator;

      if (isDirectPIIField || isContextualPIIValue) {
        masked[key] = maskValue(value);
      } else {
        masked[key] = maskPIIData(value, depth + 1, visited);
      }
    }

    return masked;
  }

  return obj;
}

// ✅ NEW: Detect if object context indicates PII
function detectContextualPII(obj: any): boolean {
  if (typeof obj !== "object" || obj === null) {
    return false;
  }

  // Check common identifier fields
  const identifierFields = ["id", "name", "key", "type", "fieldName", "field"];

  for (const field of identifierFields) {
    if (field in obj && typeof obj[field] === "string") {
      const identifier = obj[field].toLowerCase();

      // Check if the identifier matches any PII field
      if (PII_FIELDS.some((pii) => identifier.includes(pii.toLowerCase()))) {
        return true;
      }
    }
  }

  return false;
}

function checkIfPIIField(key: string): boolean {
  const lowerKey = key.toLowerCase();
  return PII_FIELDS.some((piiField) => lowerKey.includes(piiField.toLowerCase()));
}

function maskString(str: string): string {
  if (str.length <= 2) {
    return str;
  }

  if (str.length > 4) {
    return `${str.substring(0, 2)}***${str.substring(str.length - 2)}`;
  }

  return "***";
}

function maskValue(value: any): any {
  if (typeof value === "string") {
    return maskString(value);
  }

  if (typeof value === "number") {
    return "***";
  }

  return "[MASKED_PII]";
}

/**
 * Request logging middleware that logs all incoming requests with PII masking
 */
export function requestLoggerMiddleware(req: Request, res: Response, next: NextFunction): void {
  try {
    // Log incoming request
    logger.info(
      {
        method: req.method,
        url: req.url,
        path: req.path,
        query: maskPIIData(req.query),
        headers: {
          "user-agent": req.headers["user-agent"],
          "content-type": req.headers["content-type"],
          "content-length": req.headers["content-length"],
          authorization: req.headers.cookie ? "[PRESENT]" : "[NOT_PRESENT]",
          "x-forwarded-for": req.headers["x-forwarded-for"],
          "x-real-ip": req.headers["x-real-ip"],
          host: req.headers.host,
          origin: req.headers.origin,
          referer: req.headers.referer,
        },
        body: req.method !== "GET" ? maskPIIData(req.body) : undefined,
        params: maskPIIData(req.params),
        timestamp: new Date().toISOString(),
      },
      `Incoming ${req.method} request to ${req.path}`
    );
  } catch (err) {
    logger.error(err, "Error in request logger middleware");
  }

  next();
}
