import { AuthError, AuthErrorType } from "@/exception-handling/auth.error";
import { BaseError } from "@/exception-handling/base.error";
import { logger } from "@/providers/logger.provider";
import { RequestContextHelper } from "@/server/context";
import { BaseResponse } from "@/shared/model/base.response";
import type { NextFunction, Request, Response } from "express";
import { ReasonPhrases, StatusCodes } from "http-status-codes";
import { BadRequestError } from "routing-controllers";

export function globalErrorHandler(err: any, req: Request, res: Response, _next: NextFunction) {
  logger.error(err, "API error");

  const validationErrorObj = isClassValidationError(err);

  if (validationErrorObj) {
    return res.status(StatusCodes.BAD_REQUEST).json(validationErrorObj);
  }

  if (err instanceof AuthError && err.type === AuthErrorType.UnAuthorized) {
    return res.status(StatusCodes.UNAUTHORIZED).json();
  }

  const isBaseError = err instanceof BaseError;
  const status = isBaseError ? err.statusCode : StatusCodes.INTERNAL_SERVER_ERROR;
  const response = BaseResponse.error(
    isBaseError ? err.code : "INTERNAL_SERVER_ERROR",
    isBaseError ? err.message : ReasonPhrases.INTERNAL_SERVER_ERROR,
    {
      requestId: RequestContextHelper.getRequestId()!,
      timestamp: new Date().toISOString(),
    }
  );

  // Since supertokens has its own error handler, we need to check if headers have been sent before sending our response.
  // Send response only if headers haven't been sent yet.
  if (!res.headersSent) {
    res.status(status).json(response);
  }
}

function isClassValidationError(err: any) {
  if (err instanceof BadRequestError) {
    const response = {
      success: false,
      error: {
        code: "BAD_REQUEST",
        message: ReasonPhrases.BAD_REQUEST,
        details: {},
      },
      meta: {
        requestId: RequestContextHelper.getRequestId()!,
        timestamp: new Date().toISOString(),
      },
    };
    return response;
  }
}
