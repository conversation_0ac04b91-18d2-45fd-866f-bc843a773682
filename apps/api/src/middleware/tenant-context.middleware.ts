import { RequestContextHelper } from "@/server/context";
import { UserRole } from "@repo/prisma/client";
import { NextFunction, Response } from "express";
import { UserClaims } from "@/shared/model/auth.user-claims";
import { TenantContext } from "@/shared/model/tenant.context";

export const addTenantToRequestContext = (req: any, res: Response, next: NextFunction) => {
  try {
    const user = req.session.getAccessTokenPayload().user as UserClaims;
    let tenantId = user.tenant.id;

    if (user.role === UserRole.SuperAdmin) {
      if (req.headers["x-tenant-id"]) {
        tenantId = req.headers["x-tenant-id"];
      }
    }

    const tenantContext: TenantContext = {
      id: tenantId,
    };

    RequestContextHelper.setTenant(tenantContext);
    next();
  } catch (error) {
    next(error);
  }
};
