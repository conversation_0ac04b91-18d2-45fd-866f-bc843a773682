import { BaseError } from "@/exception-handling/base.error";
import { getPolicyForRole } from "@/providers/policy.provider";
import { RequestContextHelper } from "@/server/context";
import { StatusCodes } from "http-status-codes";
import { NextFunction, Request, Response } from "express";
import { Resource, Action } from "@/config/policy/rules";
import { logger } from "@/providers/logger.provider";

/**
 * Middleware to check if the authenticated user has permission to perform specific actions on a resource
 *
 * @param resourceName - The resource name to check permission for (e.g., "user", "tenant")
 * @param actionNames - Array of action names that are allowed (e.g., ["create", "update"])
 * @returns Express middleware function
 */
export const hasPermission = (resourceName: Resource, actionNames: Action[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      logger.info(
        {
          resourceName,
          actionNames,
        },
        "Checking permissions for resource and actions"
      );

      if (!RequestContextHelper.getUser()) {
        throw new BaseError(
          "ACCESS FORBIDDEN",
          "User Authentication required",
          StatusCodes.FORBIDDEN,
          {}
        );
      }

      // Get tenant ID from request context
      const tenantId = RequestContextHelper.getTenant()!.id!;

      if (!tenantId) {
        throw new BaseError(
          "ACCESS FORBIDDEN",
          "Tenant Authentication required",
          StatusCodes.FORBIDDEN,
          {}
        );
      }

      // Get policy for the user's role in the tenant
      const rolePolicy = getPolicyForRole(tenantId, RequestContextHelper.getUser()!.role);

      if (!rolePolicy || !rolePolicy.resources) {
        logger.error(
          {
            resourceName,
            actionNames,
            rolePolicy,
          },
          "No policy found for role"
        );
        throw new BaseError("FORBIDDEN", "Access Denied", StatusCodes.FORBIDDEN, {
          role: RequestContextHelper.getUser()!.role,
          tenantId: tenantId,
        });
      }

      // Get allowed actions for the resource
      const allowedActions = rolePolicy.resources[resourceName];

      if (!allowedActions || allowedActions.length === 0) {
        logger.error(
          {
            resourceName,
            actionNames,
            allowedActions,
          },
          "No permissions for resource"
        );
        throw new BaseError("FORBIDDEN", "Access Denied", StatusCodes.FORBIDDEN, {
          resource: resourceName,
          role: RequestContextHelper.getUser()!.role,
          tenantId: tenantId,
        });
      }

      // Check if any of the required actions are allowed
      const hasRequiredPermission = actionNames.some((action) =>
        allowedActions.includes(action as any)
      );

      if (!hasRequiredPermission) {
        logger.error(
          {
            resourceName,
            actionNames,
            allowedActions,
          },
          "Insufficient permissions for resource"
        );
        throw new BaseError("FORBIDDEN", "Access Denied", StatusCodes.FORBIDDEN, {
          resource: resourceName,
          requiredActions: actionNames,
          allowedActions: allowedActions,
          role: RequestContextHelper.getUser()!.role,
          tenantId: tenantId,
        });
      }

      // Permission granted, continue to next middleware/controller
      next();
    } catch (error) {
      // If it's already a BaseError, pass it through
      if (error instanceof BaseError) {
        next(error);
      } else {
        // Wrap unexpected errors
        next(
          new BaseError(
            "INTERNAL_ERROR",
            "Permission check failed",
            StatusCodes.INTERNAL_SERVER_ERROR,
            { originalError: error }
          )
        );
      }
    }
  };
};

/**
 * Convenience function to create permission middleware for single action
 *
 * @param resourceName - The resource name to check permission for
 * @param actionName - Single action name that is required
 * @returns Express middleware function
 */
export const requirePermission = (resourceName: Resource, actionName: Action) => {
  return hasPermission(resourceName, [actionName]);
};

/**
 * Convenience function to create permission middleware for read access
 *
 * @param resourceName - The resource name to check read permission for
 * @returns Express middleware function
 */
export const requireReadPermission = (resourceName: Resource) => {
  return requirePermission(resourceName, Action.Read);
};

/**
 * Convenience function to create permission middleware for write access (create/update)
 *
 * @param resourceName - The resource name to check write permission for
 * @returns Express middleware function
 */
export const requireWritePermission = (resourceName: Resource) => {
  return hasPermission(resourceName, [Action.Create, Action.Update]);
};

/**
 * Convenience function to create permission middleware for delete access
 *
 * @param resourceName - The resource name to check delete permission for
 * @returns Express middleware function
 */
export const requireDeletePermission = (resourceName: Resource) => {
  return requirePermission(resourceName, Action.Delete);
};
