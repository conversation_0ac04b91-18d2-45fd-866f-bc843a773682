import { RequestContextHelper } from "@/server/context";
import { UserRole } from "@repo/prisma/client";
import { NextFunction, Response } from "express";
import { UserClaims } from "@/shared/model/auth.user-claims";
import { UserContext } from "@/shared/model/user.context";

export const addUserToRequestContext = (req: any, res: Response, next: NextFunction) => {
  try {
    const user = req.session.getAccessTokenPayload().user as UserClaims;
    const userContext: UserContext = {
      id: user.id,
      name: user.name,
      role: user.role,
      displayName: user.displayName,
      email: user.email,
      status: user.status,
    };

    RequestContextHelper.setUser(userContext);
    next();
  } catch (error) {
    next(error);
  }
};
