import "reflect-metadata";
import { Express } from "express";
import { getMetadataArgsStorage } from "routing-controllers";
import swaggerUi, { SwaggerOptions } from "swagger-ui-express";
import { routingControllersToSpec } from "routing-controllers-openapi";
import { appConfig } from "./config";

import { validationMetadatasToSchemas } from "class-validator-jsonschema";
import { logger } from "./providers/logger.provider";

const schemas = validationMetadatasToSchemas({
  refPointerPrefix: "#/components/schemas/",
});

export function setupApiSwagger(app: Express) {
  const openApiDocument = () => {
    const storage = getMetadataArgsStorage();

    const spec = routingControllersToSpec(
      storage,
      {
        controllers: [],
        routePrefix: "/api/v1",
      },
      {
        openapi: "3.0.0",
        info: {
          title: "Cadet Labs API",
          description: "API documentation for Cadet Labs application",
          version: "1.0.0",
        },

        security: [
          {
            bearerAuth: [],
          },
          {
            cookieAuth: [],
          },
        ],

        components: {
          securitySchemes: {
            bearerAuth: {
              type: "http",
              scheme: "bearer",
              bearerFormat: "JWT",
            },
            cookieAuth: {
              type: "apiKey",
              in: "cookie",
              name: "sAccessToken", // 👈 your cookie name here
            },
          },
          schemas,
        },
        servers: [
          {
            url: `http://localhost:${appConfig.server.port}`,
            description: "Development Server",
          },
        ],
      }
    );
    // 🚫 Remove all `/auth` routes safely
    for (const path in spec.paths) {
      if (path.startsWith("/api/v1/auth")) {
        delete spec.paths[path];
      }
    }

    return spec;
  };
  // Swagger UI options
  const swaggerOptions: SwaggerOptions = {
    explorer: true,
    swaggerOptions: {
      docExpansion: "none",
      filter: true,
      showRequestDuration: true,
      tryItOutEnabled: true,
      withCredentials: true, // 👈 This enables sending cookies
      persistAuthorization: true, // optional: keep auth info between page reloads
    },
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info { margin: 20px 0 }
      .swagger-ui .info .title { color: #3b82f6 }
      .swagger-ui .scheme-container { background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0 }
    `,
    customSiteTitle: "Cadet Labs API Documentation",
    customfavIcon: "/favicon.ico",
  };

  // Serve Swagger UI
  app.use("/api-docs", swaggerUi.serve);
  app.get("/api-docs", swaggerUi.setup(openApiDocument(), swaggerOptions));

  // Serve raw OpenAPI JSON
  app.get("/api-docs.json", (req, res) => {
    res.setHeader("Content-Type", "application/json");
    res.send(openApiDocument());
  });

  // Serve OpenAPI YAML
  app.get("/api-docs.yaml", (req, res) => {
    const yaml = require("js-yaml");
    res.setHeader("Content-Type", "text/yaml");
    res.send(yaml.dump(openApiDocument()));
  });

  logger.info("📚 Swagger UI available at: http://localhost:3005/api-docs");
  logger.info("📄 OpenAPI JSON available at: http://localhost:3005/api-docs.json");
}
