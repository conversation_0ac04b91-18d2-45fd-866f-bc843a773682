{"description": "User Activate Controller Examples", "endpoint": "PATCH /api/v1/user/{userId}/activate", "examples": {"activate_inactive_user": {"description": "Activate an inactive user", "url_params": {"userId": "user-123-456-789"}, "request": "No request body required"}, "activate_different_user": {"description": "Admin activating another user", "url_params": {"userId": "user-987-654-321"}, "request": "No request body required"}}, "validation_notes": {"userId": "Required UUID in URL path, must be valid user ID"}, "business_rules": ["Users cannot activate themselves", "Only inactive users can be activated", "User must exist in the system", "After activation, user status changes to 'PendingVerification'", "Verification email is sent to the user"], "error_scenarios": {"self_activation": {"description": "User trying to activate themselves", "url_params": {"userId": "current-user-id"}, "expected_error": {"code": "INVALID_REQUEST", "message": "You cannot activate yourself", "status": 400}}, "user_not_found": {"description": "Trying to activate non-existent or already active user", "url_params": {"userId": "non-existent-user-id"}, "expected_error": {"code": "USER_NOT_FOUND", "message": "user not found", "status": 404}}}, "response_example": {"success": true, "data": null, "message": "User activated successfully. Verification email sent.", "meta": {"requestId": "req-789", "timestamp": "2024-01-15T10:40:00Z"}}}