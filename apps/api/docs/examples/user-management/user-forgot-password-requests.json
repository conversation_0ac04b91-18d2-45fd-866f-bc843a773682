{"description": "Forgot Password Controller Examples", "endpoint": "POST /auth/user/password/reset/token", "examples": {"forgot_password_request": {"description": "Request password reset for user", "request": {"formFields": [{"id": "email", "value": "<EMAIL>"}]}}, "corporate_email": {"description": "Password reset for corporate email", "request": {"formFields": [{"id": "email", "value": "<EMAIL>"}]}}}, "validation_notes": {"formFields": "Required array with exactly 1 element", "formFields[0].id": "Must be exactly 'email'", "formFields[0].value": "Must be valid email format"}, "business_rules": ["Always returns success status to prevent email enumeration attacks", "Password reset email is only sent if user exists and email is verified", "If user doesn't exist or email not verified, silently fails but returns success", "Reset link contains token and is valid for limited time"], "security_notes": ["No error is returned if email doesn't exist (prevents enumeration)", "No error is returned if email is not verified", "Always returns 200 OK status"], "response_example": {"status": "OK", "token": "reset-token-here"}, "email_content": {"subject": "Password Reset Request", "contains": ["Password reset link", "Expiration time", "Security notice"]}}