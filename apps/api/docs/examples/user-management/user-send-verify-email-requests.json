{"description": "Send Verification Email Controller Examples", "endpoint": "POST /api/v1/user/send-verification-email/", "examples": {"send_verification_email": {"description": "Send verification email to pending user", "request": {"id": "user-123-456-789"}}, "resend_verification": {"description": "Resend verification email to user", "request": {"id": "user-987-654-321"}}}, "validation_notes": {"id": "Required UUID, must be valid user ID"}, "business_rules": ["User must exist in the system", "User status must be 'PendingVerification'", "Verification email is sent to the user's registered email", "Email contains verification link with token"], "error_scenarios": {"user_not_found": {"description": "Trying to send email to non-existent user or user not in pending verification status", "request": {"id": "non-existent-user-id"}, "expected_error": {"code": "USER_NOT_FOUND", "message": "User not found", "status": 404}}, "wrong_status": {"description": "User exists but not in PendingVerification status", "request": {"id": "active-user-id"}, "expected_error": {"code": "USER_NOT_FOUND", "message": "User not found", "status": 404}}}, "response_example": {"success": true, "data": null, "message": "Verification email sent successfully", "meta": {"requestId": "req-202", "timestamp": "2024-01-15T10:50:00Z"}}}