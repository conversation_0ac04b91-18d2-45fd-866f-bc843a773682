{"description": "User Profile Controller Examples", "endpoint": "GET /api/v1/user/me/", "examples": {"get_current_user_profile": {"description": "Get current authenticated user's profile", "request": {"method": "GET", "headers": {"Authorization": "Bearer <session-token>", "Cookie": "sAccessToken=<session-cookie>"}}}}, "validation_notes": {"authentication": "Requires valid session token in cookie or Authorization header", "no_body": "This is a GET request with no request body"}, "business_rules": ["Returns profile of currently authenticated user only", "User must be logged in with valid session", "Includes user details, role, and tenant information"], "error_scenarios": {"unauthenticated": {"description": "No valid session token provided", "expected_error": {"message": "Unauthorized", "status": 401}}, "user_not_found": {"description": "Valid session but user not found in database", "expected_error": {"code": "USER_NOT_FOUND", "message": "User not found", "status": 404}}}, "response_example": {"success": true, "data": {"id": "user-123-456-789", "name": "<PERSON>", "displayName": "<PERSON>", "email": "<EMAIL>", "role": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Active", "privileges": {"users": ["create", "read", "update", "delete"], "tenants": ["read", "update"]}, "tenant": {"id": "tenant-123", "name": "Acme Corp", "code": "ACME", "subscriptionType": "Premium", "type": "Customer", "status": "Active"}}, "meta": {"requestId": "req-303", "timestamp": "2024-01-15T10:55:00Z"}}}