{"description": "User List Controller Examples", "endpoint": "POST /api/v1/users/find/", "examples": {"basic_request": {"description": "Basic request with default pagination", "request": {"limit": 20}}, "filtered_request": {"description": "Search for active project handlers named <PERSON>", "request": {"filters": {"name": "john", "role": ["ProjectHandler"], "status": ["Active"]}, "limit": 10, "sort": {"field": "name", "direction": "asc"}}}, "email_search": {"description": "Find users with company email addresses", "request": {"filters": {"email": "company.com"}, "sort": {"field": "email", "direction": "asc"}}}, "pagination_request": {"description": "Get next page using cursor (cursor contains sort field value + id)", "request": {"limit": 20, "cursor": "eyJ2YWx1ZSI6IjIwMjQtMDEtMTVUMTA6MzA6MDBaIiwiaWQiOiJ1c2VyLTEyMyJ9", "sort": {"field": "createdAt", "direction": "desc"}}}, "exclude_inactive": {"description": "Get all active users only", "request": {"filters": {"status": ["Active"]}, "limit": 50}}}, "response_example": {"success": true, "data": {"users": [{"id": "user-123", "name": "<PERSON>", "displayName": "<PERSON>", "emailId": "<EMAIL>", "role": "ProjectHandler", "status": "Active"}, {"id": "user-456", "name": "<PERSON>", "displayName": "<PERSON>", "emailId": "<EMAIL>", "role": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Active"}], "nextCursor": "eyJpZCI6InVzZXItNDU2In0=", "hasMore": true}, "message": "Users retrieved successfully", "meta": {"requestId": "req-123", "timestamp": "2024-01-15T10:30:00Z"}}, "validation_notes": {"filters": "Optional object with search criteria", "sort": "Optional object with field and direction (asc/desc)", "limit": "Optional number (default: 20, max: 100)", "cursor": "Optional string for pagination (base64 encoded)"}, "filter_fields": {"name": "String - Substring search in user name", "email": "String - Substring search in email address", "role": "Array - Filter by user roles (<PERSON><PERSON><PERSON><PERSON><PERSON>, ProjectHandler)", "status": "Array - Filter by user status (Active, Inactive, PendingVerification)"}, "sort_fields": {"supported_fields": ["name", "email", "role", "status", "createdAt", "updatedAt"], "directions": ["asc", "desc"], "default": "createdAt desc"}}