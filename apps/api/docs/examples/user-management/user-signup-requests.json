{"description": "User Signup Controller Examples", "endpoint": "POST /api/v1/user/", "examples": {"tenant_admin_signup": {"description": "Create a new tenant admin user", "request": {"name": "<PERSON>", "displayName": "<PERSON>", "emailId": "<EMAIL>", "role": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "project_handler_signup": {"description": "Create a new project handler user", "request": {"name": "<PERSON>", "displayName": "<PERSON>", "emailId": "<EMAIL>", "role": "ProjectHandler"}}, "minimal_signup": {"description": "Minimal required fields for user signup", "request": {"name": "<PERSON>", "displayName": "<PERSON>", "emailId": "<EMAIL>", "role": "ProjectHandler"}}}, "validation_notes": {"name": "Required string, cannot be empty", "displayName": "Required string, cannot be empty", "emailId": "Required string, should be valid email format", "role": "Required enum, must be either 'TenantAdmin' or 'ProjectHandler'"}, "response_example": {"success": true, "data": null, "message": "User created successfully", "meta": {"requestId": "req-123", "timestamp": "2024-01-15T10:30:00Z"}}}