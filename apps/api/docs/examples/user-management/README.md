# API Request Examples

This directory contains comprehensive request examples for all API controllers in the user management system.

## 📁 File Structure

```
examples/request/
├── README.md                           # This file
├── user-signup-requests.json          # POST /api/v1/user/
├── user-update-requests.json          # PATCH /api/v1/user/
├── user-activate-requests.json        # PATCH /api/v1/user/activate/
├── user-deactivate-requests.json      # PATCH /api/v1/user/deactivate/
├── user-list-requests.json            # POST /api/v1/users/find/
├── user-send-verify-email-requests.json # POST /api/v1/user/send-verification-email/
├── user-profile-requests.json         # GET /api/v1/user/me/
├── user-forgot-password-requests.json # POST /auth/user/password/reset/token
└── user-verify-email-requests.json    # POST /auth/user/email/verify
```

## 🔐 Authentication

### Protected Routes (Require Authentication)

- All `/api/v1/*` endpoints require valid session authentication
- Include session cookie: `sAccessToken=<token>`
- Or Authorization header: `Bearer <token>`

### Unprotected Routes

- `/auth/*` endpoints are publicly accessible
- No authentication required

## 📋 Controller Overview

### 1. User Signup (`user-signup-requests.json`)

**Endpoint:** `POST /api/v1/user/`

- Create new users in the system
- Supports TenantAdmin and ProjectHandler roles
- Sends verification email after creation

### 2. User Update (`user-update-requests.json`)

**Endpoint:** `PATCH /api/v1/user/`

- Update user profile information
- Users cannot change their own role
- Supports partial updates

### 3. User Activate (`user-activate-requests.json`)

**Endpoint:** `PATCH /api/v1/user/{userId}/activate`

- Activate inactive users
- Users cannot activate themselves
- Sends verification email after activation

### 4. User Deactivate (`user-deactivate-requests.json`)

**Endpoint:** `PATCH /api/v1/user/{userId}/deactivate`

- Deactivate active users
- Users cannot deactivate themselves
- Logs user out of all sessions

### 5. User List (`user-list-requests.json`)

**Endpoint:** `POST /api/v1/users/find/`

- Advanced filtering, sorting, and pagination
- Cursor-based pagination for performance
- Supports text search and multi-select filters

### 6. Send Verification Email (`user-send-verify-email-requests.json`)

**Endpoint:** `POST /api/v1/user/send-verification-email/`

- Resend verification emails
- Only works for users in PendingVerification status

### 7. User Profile (`user-profile-requests.json`)

**Endpoint:** `GET /api/v1/user/me/`

- Get current user's profile
- Includes role privileges and tenant information

### 8. Forgot Password (`user-forgot-password-requests.json`)

**Endpoint:** `POST /auth/user/password/reset/token`

- Request password reset
- Always returns success (prevents email enumeration)

### 9. Verify Email (`user-verify-email-requests.json`)

**Endpoint:** `POST /auth/user/email/verify`

- Verify user email with token
- Activates user account
- Returns password setup token

## 🚀 Usage Examples

### Using cURL

```bash
# User Signup
curl -X POST http://localhost:3000/api/v1/user/ \
  -H "Content-Type: application/json" \
  -H "Cookie: sAccessToken=your-session-token" \
  -d '{
    "name": "John Doe",
    "displayName": "John D.",
    "emailId": "<EMAIL>",
    "role": "ProjectHandler"
  }'

# User List with Filters
curl -X POST http://localhost:3000/api/v1/users/find/ \
  -H "Content-Type: application/json" \
  -H "Cookie: sAccessToken=your-session-token" \
  -d '{
    "filters": {
      "name": "john",
      "role": ["ProjectHandler"],
      "status": ["Active"]
    },
    "limit": 20,
    "sort": { "field": "name", "direction": "asc" }
  }'

# Forgot Password
curl -X POST http://localhost:3000/auth/user/password/reset/token \
  -H "Content-Type: application/json" \
  -d '{
    "formFields": [
      {
        "id": "email",
        "value": "<EMAIL>"
      }
    ]
  }'
```

### Using JavaScript/Fetch

```javascript
// User Profile
const response = await fetch("/api/v1/user/me/", {
  method: "GET",
  credentials: "include", // Include cookies
  headers: {
    "Content-Type": "application/json",
  },
});

// User Update
const updateResponse = await fetch("/api/v1/user/", {
  method: "PATCH",
  credentials: "include",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    id: "user-123-456-789",
    name: "Updated Name",
    displayName: "Updated Display",
  }),
});
```

## 📊 Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "data": {
    /* response data */
  },
  "message": "Operation completed successfully",
  "error": null,
  "meta": {
    "requestId": "req-123",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## ⚠️ Error Handling

Common error responses:

```json
{
  "success": false,
  "data": null,
  "message": "Error message",
  "error": {
    "code": "ERROR_CODE",
    "message": "Detailed error message"
  },
  "meta": {
    "requestId": "req-123",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## 🔍 Testing

Each example file includes:

- ✅ Valid request examples
- ❌ Error scenarios
- 📝 Validation rules
- 🔒 Business rules
- 📤 Expected responses

Use these examples for:

- API testing
- Integration testing
- Documentation
- Client SDK development
- Postman collections

## 📚 Additional Resources

- [Swagger Documentation](http://localhost:3000/api-docs)
- [API Schema](http://localhost:3000/api-docs.json)
- [Controller Source Code](../features/user-management/controller/)
