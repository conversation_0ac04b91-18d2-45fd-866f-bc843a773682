{"description": "Email Verification Controller Examples", "endpoint": "POST /auth/user/email/verify", "examples": {"verify_email_token": {"description": "Verify user email with token", "request": {"method": "token", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}, "verify_from_email_link": {"description": "Verification from email link click", "request": {"method": "token", "token": "verification-token-from-email-link"}}}, "validation_notes": {"method": "Must be exactly 'token'", "token": "Required string, must be valid verification token"}, "business_rules": ["Token must be valid and not expired", "User status changes from 'PendingVerification' to 'Active'", "Returns password reset token for immediate password setup", "User can now log in to the system"], "error_scenarios": {"invalid_token": {"description": "Invalid or expired token", "request": {"method": "token", "token": "invalid-token"}, "expected_error": {"message": "Invalid or expired token", "status": 400}}, "already_verified": {"description": "User already verified", "request": {"method": "token", "token": "valid-token-for-verified-user"}, "expected_error": {"message": "Email already verified", "status": 400}}}, "response_example": {"status": "OK", "token": "password-reset-token-for-immediate-setup"}, "flow_notes": ["User receives verification email after signup/activation", "User clicks verification link in email", "This endpoint verifies the email", "User status becomes Active", "User can set password using returned token"]}