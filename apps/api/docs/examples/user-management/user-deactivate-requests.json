{"description": "User Deactivate Controller Examples", "endpoint": "PATCH /api/v1/user/{userId}/deactivate", "examples": {"deactivate_active_user": {"description": "Deactivate an active user", "url_params": {"userId": "user-123-456-789"}, "request": "No request body required"}, "admin_deactivate_user": {"description": "Admin deactivating another user", "url_params": {"userId": "user-987-654-321"}, "request": "No request body required"}}, "validation_notes": {"userId": "Required UUID in URL path, must be valid user ID"}, "business_rules": ["Users cannot deactivate themselves", "Only active users can be deactivated", "User must exist in the system", "After deactivation, user status changes to 'Inactive'", "User is logged out from all sessions", "Deactivation email is sent to the user"], "error_scenarios": {"self_deactivation": {"description": "User trying to deactivate themselves", "url_params": {"userId": "current-user-id"}, "expected_error": {"code": "INVALID_REQUEST", "message": "You cannot deactivate yourself", "status": 400}}, "user_not_found": {"description": "Trying to deactivate non-existent or already inactive user", "url_params": {"userId": "non-existent-user-id"}, "expected_error": {"code": "USER_NOT_FOUND", "message": "Active user not found", "status": 404}}}, "response_example": {"success": true, "data": null, "message": "User deactivated successfully", "meta": {"requestId": "req-101", "timestamp": "2024-01-15T10:45:00Z"}}}