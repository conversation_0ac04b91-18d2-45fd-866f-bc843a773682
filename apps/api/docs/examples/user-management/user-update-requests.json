{"description": "User Update Controller Examples", "endpoint": "PATCH /api/v1/user/", "examples": {"update_name_and_display": {"description": "Update user's name and display name", "request": {"id": "user-123-456-789", "name": "<PERSON>", "displayName": "<PERSON>"}}, "update_role_only": {"description": "Update user's role (admin operation)", "request": {"id": "user-123-456-789", "role": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "update_all_fields": {"description": "Update all possible fields", "request": {"id": "user-123-456-789", "name": "<PERSON>", "displayName": "<PERSON><PERSON>", "role": "ProjectHandler"}}, "self_update": {"description": "User updating their own profile (role cannot be changed)", "request": {"id": "current-user-id", "name": "Updated Name", "displayName": "Updated Display"}}}, "validation_notes": {"id": "Required UUID, must be valid user ID", "name": "Optional string, cannot be empty if provided", "displayName": "Optional string, cannot be empty if provided", "role": "Optional enum, must be either 'TenantAdmin' or 'ProjectHandler'. Cannot be changed by self-update"}, "business_rules": ["Users cannot change their own role", "At least one field (name, displayName, or role) must be provided for update", "Only admins can change other users' roles"], "response_example": {"success": true, "data": null, "message": "User updated successfully", "meta": {"requestId": "req-456", "timestamp": "2024-01-15T10:35:00Z"}}}