{"title": "API Request Examples Index", "description": "Complete collection of request examples for all API controllers", "version": "1.0.0", "base_url": {"development": "http://localhost:3000", "production": "https://api.cadetlabs.com"}, "controllers": {"user_management": {"description": "User management operations", "endpoints": [{"name": "User Signup", "method": "POST", "path": "/api/v1/user/", "auth_required": true, "example_file": "user-signup-requests.json", "description": "Create new users in the system"}, {"name": "User Update", "method": "PATCH", "path": "/api/v1/user/", "auth_required": true, "example_file": "user-update-requests.json", "description": "Update user profile information"}, {"name": "User List", "method": "POST", "path": "/api/v1/users/find/", "auth_required": true, "example_file": "user-list-requests.json", "description": "Advanced user search with filtering and pagination"}, {"name": "User Profile", "method": "GET", "path": "/api/v1/user/me/", "auth_required": true, "example_file": "user-profile-requests.json", "description": "Get current user's profile"}, {"name": "User Activate", "method": "PATCH", "path": "/api/v1/user/{userId}/activate", "auth_required": true, "example_file": "user-activate-requests.json", "description": "Activate inactive users"}, {"name": "User Deactivate", "method": "PATCH", "path": "/api/v1/user/{userId}/deactivate", "auth_required": true, "example_file": "user-deactivate-requests.json", "description": "Deactivate active users"}, {"name": "Send Verification Email", "method": "POST", "path": "/api/v1/user/send-verification-email/", "auth_required": true, "example_file": "user-send-verify-email-requests.json", "description": "Resend verification emails to users"}]}, "authentication": {"description": "Authentication and email verification operations", "endpoints": [{"name": "Forgot Password", "method": "POST", "path": "/auth/user/password/reset/token", "auth_required": false, "example_file": "user-forgot-password-requests.json", "description": "Request password reset token"}, {"name": "<PERSON><PERSON><PERSON>", "method": "POST", "path": "/auth/user/email/verify", "auth_required": false, "example_file": "user-verify-email-requests.json", "description": "Verify user email with token"}]}}, "common_patterns": {"authentication": {"session_cookie": "sAccessToken=<token>", "authorization_header": "Bearer <token>", "note": "Protected endpoints require valid session authentication"}, "request_format": {"content_type": "application/json", "validation": "All requests are validated using class-validator decorators"}, "response_format": {"success_response": {"success": true, "data": "Response data", "message": "Success message", "meta": {"requestId": "Unique request ID", "timestamp": "ISO timestamp"}}, "error_response": {"success": false, "data": null, "message": "Error message", "error": {"code": "ERROR_CODE", "message": "Detailed error"}, "meta": {"requestId": "Unique request ID", "timestamp": "ISO timestamp"}}}, "pagination": {"type": "cursor_based", "description": "Uses cursor-based pagination for performance", "parameters": {"limit": "Number of items (1-50, default: 20)", "cursor": "Base64 encoded cursor for next page"}}, "filtering": {"text_filters": "String values for substring search", "array_filters": "Array values for IN operator", "range_filters": "Min/max values for range queries"}}, "testing_tools": {"swagger_ui": "/api-docs", "openapi_json": "/api-docs.json", "openapi_yaml": "/api-docs.yaml", "postman": "Import OpenAPI spec into Postman", "curl": "Use cURL examples in README.md"}, "file_structure": ["README.md - Complete documentation", "index.json - This overview file", "user-signup-requests.json", "user-update-requests.json", "user-activate-requests.json", "user-deactivate-requests.json", "user-list-requests.json", "user-send-verify-email-requests.json", "user-profile-requests.json", "user-forgot-password-requests.json", "user-verify-email-requests.json"]}