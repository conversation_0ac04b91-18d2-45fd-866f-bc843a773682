# Tenant Management API Examples

This directory contains comprehensive request examples for all tenant management API endpoints.

## 📁 File Structure

```
examples/tenant-management/
├── README.md                           # This file
├── tenant-register-requests.json      # POST /api/v1/tenant/register/
├── tenant-update-requests.json        # PATCH /api/v1/tenant/
├── tenant-deactivate-requests.json    # PATCH /api/v1/tenant/{tenantId}/deactivate
├── tenant-list-requests.json          # POST /api/v1/tenants/find/
└── index.json                          # Quick overview and navigation
```

## 🔐 Authentication

### Protected Routes (Require Authentication)

- All tenant management endpoints require valid session authentication
- Include session cookie: `sAccessToken=<token>`
- Or Authorization header: `Bearer <token>`

## 📋 Controller Overview

### 1. Tenant Registration (`tenant-register-requests.json`)

**Endpoint:** `POST /api/v1/tenant/register/`

- Register new customer tenants in the system
- Creates both tenant record and admin user
- Sends verification email to admin
- Supports Trial, Standard, and Premium subscriptions

### 2. Tenant Update (`tenant-update-requests.json`)

**Endpoint:** `PATCH /api/v1/tenant/`

- Update tenant information and settings
- Validates user limits against current active users
- Supports partial updates (only provided fields are updated)
- Tenant must be Active to update

### 3. Tenant Activation (`tenant-activate-requests.json`)

**Endpoint:** `PATCH /api/v1/tenant/{tenantId}/activate`

- Activate inactive tenants
- Changes status from Inactive to Active
- No request body required (tenantId in URL)
- Allows tenant users to access system again

### 4. Tenant Deactivation (`tenant-deactivate-requests.json`)

**Endpoint:** `PATCH /api/v1/tenant/{tenantId}/deactivate`

- Deactivate active tenants
- Changes status from Active to Inactive
- No request body required (tenantId in URL)
- Prevents tenant users from accessing system

### 5. Tenant Listing (`tenant-list-requests.json`)

**Endpoint:** `POST /api/v1/tenants/find/`

- Advanced tenant search with filtering and pagination
- Supports substring search on text fields
- Includes current active user count per tenant
- Cursor-based pagination for large datasets

## 🎯 Key Features

### Smart Filtering System

- **String fields** (name, code, adminName, adminEmailId): Substring search (case-insensitive)
- **Array fields** (subscriptionType, status): IN operator for multiple values
- **Number fields** (adminContactNumber): Exact match

### Subscription Types

- **Trial**: Limited users, evaluation period
- **Standard**: Regular business subscription
- **Premium**: Enhanced features and higher user limits

### Tenant Status Management

- **Active**: Tenant can operate normally
- **Inactive**: Tenant is deactivated, users cannot access

### User Limit Validation

- System tracks active and pending verification users
- Updates to maxUsers must not be less than current user count
- Prevents data inconsistency and business rule violations

## 🚀 Usage Examples

### Register a New Tenant

```bash
curl -X POST 'http://localhost:3000/api/v1/tenant/register/' \
  -H 'Cookie: sAccessToken=your-session-token' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "Acme Corporation",
    "code": "ACME001",
    "adminName": "John Smith",
    "adminEmailId": "<EMAIL>",
    "adminContactNumber": "+919876543210",
    "subscriptionType": "Standard",
    "maxUsers": 25
  }'
```

### Search Tenants

```bash
curl -X POST 'http://localhost:3000/api/v1/tenants/find/' \
  -H 'Cookie: sAccessToken=your-session-token' \
  -H 'Content-Type: application/json' \
  -d '{
    "filters": {
      "name": "Corp",
      "subscriptionType": ["Premium"],
      "status": ["Active"]
    },
    "limit": 20,
    "sort": { "field": "name", "direction": "asc" }
  }'
```

### Update Tenant

```bash
curl -X PATCH 'http://localhost:3000/api/v1/tenant/' \
  -H 'Cookie: sAccessToken=your-session-token' \
  -H 'Content-Type: application/json' \
  -d '{
    "id": "tenant-123-uuid",
    "subscriptionType": "Premium",
    "maxUsers": 50
  }'
```

### Deactivate Tenant

```bash
curl -X PATCH 'http://localhost:3000/api/v1/tenant/tenant-123-uuid/deactivate' \
  -H 'Cookie: sAccessToken=your-session-token' \
  -H 'Content-Type: application/json'
```

## 📊 Response Format

All endpoints return responses in this format:

```json
{
  "success": true,
  "data": {
    /* endpoint-specific data */
  },
  "message": "Operation completed successfully"
}
```

Error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "statusCode": 400,
    "details": {
      /* additional error context */
    }
  }
}
```

## 🔍 Business Rules

### Tenant Registration

- Creates tenant with Customer type
- Admin user gets TenantAdmin role
- Verification email sent automatically
- Tenant code must be unique
- Admin email must be unique

### Tenant Updates

- Only Active tenants can be updated
- maxUsers cannot be less than current active + pending users
- Partial updates supported
- Phone numbers must be valid Indian format

### Tenant Deactivation

- Only Active tenants can be deactivated
- Users lose access but data is preserved
- Can be reactivated later (separate endpoint)

### User Count Tracking

- Counts Active and PendingVerification users only
- Excludes Inactive and deleted users
- Updated in real-time for accurate limits

## 🛠️ Integration

These examples work perfectly with:

- **Swagger UI** at `/api-docs`
- **Postman** collections
- **Integration tests**
- **Client SDK development**
- **API documentation**

## 📝 Notes

- All timestamps are in ISO 8601 format
- UUIDs are used for all entity identifiers
- Phone numbers must follow Indian format (+91...)
- Cursor pagination maintains sort order consistency
- Deleted tenants are automatically excluded from all queries

For more detailed examples and edge cases, check the individual JSON files in this directory.
