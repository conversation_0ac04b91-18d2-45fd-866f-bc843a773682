{"description": "Tenant Update Controller Examples", "endpoint": "PATCH /api/v1/tenant/", "examples": {"update_name_only": {"description": "Update only the tenant name", "request": {"id": "tenant-123-uuid", "name": "Acme Corporation Ltd"}}, "update_contact_info": {"description": "Update admin contact information", "request": {"id": "tenant-456-uuid", "adminContactNumber": "+************"}}, "upgrade_subscription": {"description": "Upgrade from Standard to Premium subscription", "request": {"id": "tenant-789-uuid", "subscriptionType": "Premium", "maxUsers": 75}}, "increase_user_limit": {"description": "Increase maximum users for existing subscription", "request": {"id": "tenant-101-uuid", "maxUsers": 50}}, "complete_update": {"description": "Update all possible fields", "request": {"id": "tenant-202-uuid", "name": "TechCorp Solutions Private Limited", "adminContactNumber": "+************", "subscriptionType": "Premium", "maxUsers": 100}}, "downgrade_subscription": {"description": "Downgrade from Premium to Standard (with user limit check)", "request": {"id": "tenant-303-uuid", "subscriptionType": "Standard", "maxUsers": 25}}}, "validation_rules": {"id": "Required UUID - Tenant identifier", "name": "Optional string - Updated tenant name", "adminContactNumber": "Optional Indian phone number - Updated contact", "subscriptionType": "Optional enum - Trial, Standard, or Premium", "maxUsers": "Optional number (1-100) - Updated user limit"}, "business_rules": {"tenant_status": "Tenant must be Active to update", "user_limit_validation": "New maxUsers cannot be less than current active + pending users", "partial_updates": "Only provided fields are updated", "authentication": "Requires valid session authentication"}, "response_example": {"success": true, "data": null, "message": "Tenant updated successfully", "meta": {"requestId": "req-123", "timestamp": "2024-01-15T10:30:00Z"}}, "error_examples": {"tenant_not_found": {"success": false, "error": {"code": "TENANT_NOT_FOUND", "message": "Tenant not found", "statusCode": 404}}, "tenant_inactive": {"success": false, "error": {"code": "TENANT_INACTIVE", "message": "Tenant must be active to update", "statusCode": 400}}, "max_users_exceeded": {"success": false, "error": {"code": "INVALID_MAX_USERS", "message": "Maximum users cannot be less than current active and pending users count (15)", "statusCode": 400, "details": {"currentUserCount": 15}}}, "invalid_phone": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Invalid Indian phone number format", "statusCode": 400}}}}