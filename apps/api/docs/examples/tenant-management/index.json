{"title": "Tenant Management API Examples", "description": "Complete collection of request examples for tenant management controllers", "version": "1.0.0", "base_url": {"development": "http://localhost:3000", "production": "https://api.cadetlabs.com"}, "controllers": {"tenant_management": {"description": "Tenant lifecycle and management operations", "endpoints": [{"name": "Tenant Registration", "method": "POST", "path": "/api/v1/tenant/register/", "auth_required": true, "example_file": "tenant-register-requests.json", "description": "Register new customer tenants with admin user creation"}, {"name": "Tenant Update", "method": "PATCH", "path": "/api/v1/tenant/", "auth_required": true, "example_file": "tenant-update-requests.json", "description": "Update tenant information and subscription settings"}, {"name": "Tenant Activation", "method": "PATCH", "path": "/api/v1/tenant/{tenantId}/activate", "auth_required": true, "example_file": "tenant-activate-requests.json", "description": "Activate inactive tenants to restore access"}, {"name": "Tenant Deactivation", "method": "PATCH", "path": "/api/v1/tenant/{tenantId}/deactivate", "auth_required": true, "example_file": "tenant-deactivate-requests.json", "description": "Deactivate active tenants to prevent access"}, {"name": "Tenant Listing", "method": "POST", "path": "/api/v1/tenants/find/", "auth_required": true, "example_file": "tenant-list-requests.json", "description": "Advanced tenant search with filtering and pagination"}]}}, "authentication": {"type": "Session-based", "methods": [{"name": "<PERSON><PERSON>", "header": "Cookie: sAccessToken=<session-token>"}, {"name": "<PERSON><PERSON>", "header": "Authorization: Bear<PERSON> <session-token>"}], "note": "All tenant management endpoints require valid authentication"}, "data_models": {"tenant_subscription_types": ["Trial", "Standard", "Premium"], "tenant_status": ["Active", "Inactive"], "tenant_type": ["Platform", "Customer"], "user_roles": ["SuperAdmin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ProjectHandler"], "user_status": ["PendingVerification", "Active", "Inactive"]}, "key_features": {"filtering": {"string_search": "Case-insensitive substring matching", "array_filters": "IN operator for multiple value selection", "number_filters": "Exact match for numeric fields"}, "pagination": {"type": "Cursor-based", "limit": "1-50 records per page", "default_limit": 20, "cursor_encoding": "Base64 encoded sort field + ID"}, "sorting": {"available_fields": ["name", "code"], "directions": ["asc", "desc"], "default": "name asc"}, "user_counting": {"included_status": ["Active", "PendingVerification"], "excluded_status": ["Inactive"], "real_time": true}}, "business_rules": {"tenant_registration": ["Creates both tenant and admin user records", "<PERSON><PERSON> gets TenantAdmin role automatically", "Verification email sent to admin", "Tenant code must be globally unique", "Admin email must be globally unique"], "tenant_updates": ["Only Active tenants can be updated", "maxUsers cannot be less than current active users", "Partial updates supported", "Phone numbers must be valid Indian format"], "tenant_deactivation": ["Only Active tenants can be deactivated", "Users lose system access immediately", "Data is preserved for potential reactivation", "Cannot deactivate already inactive tenants"], "tenant_listing": ["Deleted tenants automatically excluded", "Real-time user count calculation", "Case-insensitive text searches", "Efficient cursor-based pagination"]}, "validation_rules": {"tenant_code": "Required string, must be unique", "tenant_name": "Required string, 1-255 characters", "admin_email": "Required valid email, must be unique", "admin_phone": "Required Indian phone number (+91...)", "max_users": "Required number, 1-100 range", "subscription_type": "Required enum: Trial, Standard, Premium", "tenant_id": "Required UUID format for updates/operations"}, "error_codes": {"TENANT_NOT_FOUND": "Tenant does not exist or is deleted", "TENANT_INACTIVE": "Tenant must be active for operation", "TENANT_NOT_ACTIVE": "Tenant must be active to deactivate", "INVALID_MAX_USERS": "User limit less than current active users", "DUPLICATE_TENANT_CODE": "Tenant code already exists", "DUPLICATE_EMAIL": "Admin email already exists", "VALIDATION_ERROR": "Request validation failed", "UNAUTHORIZED": "Authentication required", "MAX_USERS_REACHED": "Tenant has reached maximum user limit"}, "integration_examples": {"postman": {"collection_url": "Available in Swagger UI export", "environment_variables": ["base_url", "session_token"]}, "swagger_ui": {"url": "/api-docs", "interactive_testing": true}, "curl_examples": "Available in individual example files", "javascript_examples": "Available in individual example files"}, "related_endpoints": {"user_management": "User operations within tenants", "authentication": "Session management and login", "authorization": "Role-based access control"}, "changelog": {"v1.0.0": {"date": "2024-01-15", "changes": ["Initial tenant management API", "Registration with admin user creation", "Update with user limit validation", "Deactivation functionality", "Advanced listing with filtering"]}}}