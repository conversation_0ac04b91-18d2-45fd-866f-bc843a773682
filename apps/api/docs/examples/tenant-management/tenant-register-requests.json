{"description": "Tenant Registration Controller Examples", "endpoint": "POST /api/v1/tenant/register/", "examples": {"basic_tenant_registration": {"description": "Register a new customer tenant with standard subscription", "request": {"name": "Acme Corporation", "adminName": "<PERSON>", "adminEmailId": "<EMAIL>", "subscriptionType": "Standard", "maxUsers": 25}}, "premium_tenant": {"description": "Register a premium tenant with higher user limit", "request": {"name": "TechCorp Solutions", "adminName": "<PERSON>", "adminEmailId": "<EMAIL>", "subscriptionType": "Premium", "maxUsers": 100}}, "trial_tenant": {"description": "Register a trial tenant with limited users", "request": {"name": "StartupXYZ", "adminName": "<PERSON>", "adminEmailId": "<EMAIL>", "subscriptionType": "Trial", "maxUsers": 5}}, "small_business": {"description": "Register a small business tenant", "request": {"name": "Local Services Ltd", "adminName": "<PERSON>", "adminEmailId": "<EMAIL>", "subscriptionType": "Standard", "maxUsers": 10}}}, "validation_rules": {"name": "Required string - Company/Organization name", "adminName": "Required string - Admin user's full name", "adminEmailId": "Required valid email - <PERSON><PERSON>'s email address", "subscriptionType": "Required enum - Trial, Standard, or Premium", "maxUsers": "Required number (1-100) - Maximum allowed users"}, "business_rules": {"tenant_creation": "Creates both tenant record and admin user in a transaction", "admin_user": "<PERSON><PERSON> gets TenantAdmin role automatically", "verification_email": "Verification email sent to admin after registration", "tenant_status": "New tenants are Active by default", "tenant_type": "All registered tenants are Customer type", "auth_integration": "Admin user is created in SuperTokens auth system"}, "response_example": {"success": true, "data": null, "message": "Tenant registered successfully", "meta": {"requestId": "req-123", "timestamp": "2024-01-15T10:30:00Z"}}, "error_examples": {"duplicate_email": {"success": false, "error": {"code": "DUPLICATE_EMAIL", "message": "Admin email already exists", "statusCode": 400}}, "validation_error": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Invalid subscription type", "statusCode": 400}}, "max_users_exceeded": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "maxUsers must be between 1 and 100", "statusCode": 400}}}}