{"examples": {"activate_tenant": {"description": "Activate an inactive tenant", "url_params": {"tenantId": "tenant-123-uuid"}, "request": "No request body required"}, "reactivate_suspended_tenant": {"description": "Reactivate a previously suspended tenant", "url_params": {"tenantId": "tenant-456-uuid"}, "request": "No request body required"}, "activate_trial_tenant": {"description": "Activate a trial tenant after payment", "url_params": {"tenantId": "tenant-789-uuid"}, "request": "No request body required"}}, "description": "Tenant Activate Controller Examples", "endpoint": "PATCH /api/v1/tenant/{tenantId}/activate", "endpoint_details": {"method": "PATCH", "path": "/api/v1/tenant/{tenantId}/activate", "auth_required": true, "request_body": "None - tenantId is passed as URL parameter"}, "validation_rules": {"tenantId": "Required UUID in URL path - Tenant to activate"}, "business_rules": {"tenant_status": "Tenant must be Inactive to activate", "tenant_existence": "Tenant must exist and not be deleted", "status_change": "Changes tenant status from Inactive to Active", "user_impact": "All tenant users regain access to system", "authentication": "Requires valid session authentication"}, "response_example": {"success": true, "data": null, "message": "Tenant activated successfully"}, "error_examples": {"tenant_not_found": {"success": false, "error": {"code": "TENANT_NOT_FOUND", "message": "Tenant not found", "statusCode": 404}}, "tenant_already_active": {"success": false, "error": {"code": "TENANT_ALREADY_ACTIVE", "message": "Tenant must be inactive to activate", "statusCode": 400}}, "invalid_tenant_id": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Invalid UUID format for tenantId", "statusCode": 400}}, "unauthorized": {"success": false, "error": {"code": "UNAUTHORIZED", "message": "Authentication required", "statusCode": 401}}}, "curl_examples": {"basic_activate": {"description": "Activate tenant using cURL", "command": "curl -X PATCH 'http://localhost:3000/api/v1/tenant/tenant-123-uuid/activate' \\\n  -H 'Cookie: sAccessToken=your-session-token' \\\n  -H 'Content-Type: application/json'"}, "with_auth_header": {"description": "Activate tenant using Authorization header", "command": "curl -X PATCH 'http://localhost:3000/api/v1/tenant/tenant-123-uuid/activate' \\\n  -H 'Authorization: Bearer your-session-token' \\\n  -H 'Content-Type: application/json'"}}, "javascript_example": {"description": "Activate tenant using JavaScript fetch", "code": "const response = await fetch('/api/v1/tenant/tenant-123-uuid/activate', {\n  method: 'PATCH',\n  headers: {\n    'Content-Type': 'application/json',\n    'Cookie': 'sAccessToken=your-session-token'\n  }\n});\n\nconst result = await response.json();\nconsole.log(result);"}}