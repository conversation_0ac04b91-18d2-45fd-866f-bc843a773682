{"description": "Tenant Deactivate Controller Examples", "endpoint": "PATCH /api/v1/tenant/{tenantId}/deactivate", "examples": {"deactivate_tenant": {"description": "Deactivate an active tenant", "url_params": {"tenantId": "tenant-123-uuid"}, "request": "No request body required"}, "deactivate_trial_tenant": {"description": "Deactivate a trial tenant that expired", "url_params": {"tenantId": "tenant-456-uuid"}, "request": "No request body required"}, "deactivate_non_paying_tenant": {"description": "Deactivate tenant for non-payment", "url_params": {"tenantId": "tenant-789-uuid"}, "request": "No request body required"}}, "endpoint_details": {"method": "PATCH", "path": "/api/v1/tenant/{tenantId}/deactivate", "auth_required": true, "request_body": "None - tenantId is passed as URL parameter"}, "validation_rules": {"tenantId": "Required UUID in URL path - Tenant to deactivate"}, "business_rules": {"tenant_status": "Tenant must be Active to deactivate", "tenant_existence": "Tenant must exist and not be deleted", "status_change": "Changes tenant status from Active to Inactive", "user_impact": "All tenant users remain but cannot access system", "authentication": "Requires valid session authentication"}, "response_example": {"success": true, "data": null, "message": "Tenant deactivated successfully"}, "error_examples": {"tenant_not_found": {"success": false, "error": {"code": "TENANT_NOT_FOUND", "message": "Tenant not found", "statusCode": 404}}, "tenant_not_active": {"success": false, "error": {"code": "TENANT_NOT_ACTIVE", "message": "Tenant must be active to deactivate", "statusCode": 400}}, "invalid_tenant_id": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Invalid UUID format for tenantId", "statusCode": 400}}, "unauthorized": {"success": false, "error": {"code": "UNAUTHORIZED", "message": "Authentication required", "statusCode": 401}}}, "curl_examples": {"basic_deactivate": {"description": "Deactivate tenant using cURL", "command": "curl -X PATCH 'http://localhost:3000/api/v1/tenant/tenant-123-uuid/deactivate' \\\n  -H 'Cookie: sAccessToken=your-session-token' \\\n  -H 'Content-Type: application/json'"}, "with_auth_header": {"description": "Deactivate tenant using Authorization header", "command": "curl -X PATCH 'http://localhost:3000/api/v1/tenant/tenant-123-uuid/deactivate' \\\n  -H 'Authorization: Bearer your-session-token' \\\n  -H 'Content-Type: application/json'"}}, "javascript_example": {"description": "Deactivate tenant using JavaScript fetch", "code": "const response = await fetch('/api/v1/tenant/tenant-123-uuid/deactivate', {\n  method: 'PATCH',\n  headers: {\n    'Content-Type': 'application/json',\n    'Cookie': 'sAccessToken=your-session-token'\n  }\n});\n\nconst result = await response.json();\nconsole.log(result);"}}