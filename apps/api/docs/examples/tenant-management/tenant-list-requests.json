{"description": "Tenant List Controller Examples", "endpoint": "POST /api/v1/tenants/find/", "examples": {"basic_request": {"description": "Basic request with default pagination", "request": {"limit": 20}}, "filtered_request": {"description": "Search for active premium tenants", "request": {"filters": {"subscriptionType": ["Premium"], "status": ["Active"]}, "limit": 10, "sort": {"field": "name", "direction": "asc"}}}, "name_search": {"description": "Find tenants with 'Corp' in their name", "request": {"filters": {"name": "Corp"}, "sort": {"field": "name", "direction": "asc"}}}, "admin_search": {"description": "Find tenants by admin name and email", "request": {"filters": {"adminName": "john", "adminEmailId": "company.com"}, "limit": 15}}, "code_search": {"description": "Search tenants by code pattern", "request": {"filters": {"code": "TECH"}, "sort": {"field": "code", "direction": "asc"}}}, "subscription_filter": {"description": "Get all trial and standard tenants", "request": {"filters": {"subscriptionType": ["Trial", "Standard"]}, "sort": {"field": "subscriptionType", "direction": "asc"}}}, "contact_search": {"description": "Search by admin contact number", "request": {"filters": {"adminContactNumber": 9876543210}}}, "pagination_request": {"description": "Get next page using cursor", "request": {"limit": 20, "cursor": "eyJ2YWx1ZSI6IjIwMjQtMDEtMTVUMTA6MzA6MDBaIiwiaWQiOiJ0ZW5hbnQtMTIzIn0=", "sort": {"field": "name", "direction": "desc"}}}, "sort_by_code": {"description": "Sort tenants by code (alphabetical)", "request": {"sort": {"field": "code", "direction": "asc"}, "limit": 25}}, "complex_filter": {"description": "Complex search with multiple filters", "request": {"filters": {"name": "Tech", "subscriptionType": ["Standard", "Premium"], "status": ["Active"], "adminEmailId": ".com"}, "sort": {"field": "name", "direction": "asc"}, "limit": 30}}}, "filter_types": {"string_filters": {"description": "String fields use substring search (case-insensitive)", "fields": ["name", "code", "admin<PERSON>ame", "adminEmailId"]}, "array_filters": {"description": "Array fields use IN operator", "fields": ["subscriptionType", "status"]}, "number_filters": {"description": "Number fields use exact match", "fields": ["adminContactNumber"]}}, "sorting_options": {"available_fields": ["name", "code"], "directions": ["asc", "desc"], "default": {"field": "name", "direction": "asc"}}, "pagination": {"limit": "Number of records per page (1-50, default: 20)", "cursor": "Base64 encoded cursor for next page", "cursor_format": "Contains sort field value + tenant ID for precise pagination"}, "response_example": {"success": true, "data": {"tenants": [{"id": "tenant-123", "name": "Acme Corporation", "code": "ACME001", "subscriptionType": "Premium", "maxActiveUsers": 50, "currentActiveUsers": 25, "status": "Active"}, {"id": "tenant-456", "name": "TechCorp Solutions", "code": "TECH001", "subscriptionType": "Standard", "maxActiveUsers": 25, "currentActiveUsers": 12, "status": "Active"}], "nextCursor": "eyJpZCI6InRlbmFudC00NTYifQ==", "hasMore": true}, "message": "OK", "meta": {"requestId": "req-123", "timestamp": "2024-01-15T10:30:00Z"}}, "validation_rules": {"filters": "Optional object with tenant filter criteria", "limit": "Optional number (1-50, default: 20)", "cursor": "Optional string for pagination", "sort": "Optional object with field and direction"}, "business_rules": {"deleted_tenants": "Deleted tenants are automatically excluded", "user_count": "currentActiveUsers includes Active and PendingVerification users only", "case_sensitivity": "String searches are case-insensitive", "authentication": "Requires valid session authentication"}}