generator client {
  provider = "prisma-client-js"
  output   = "./generated/prisma-client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["app"]
}

enum TenantType {
  Platform @map("platform")
  Customer @map("customer")

  @@schema("app")
}

enum UserRole {
  SuperAdmin     @map("super_admin")
  TenantAdmin    @map("tenant_admin")
  ProjectHandler @map("project_handler")

  @@schema("app")
}

enum UserStatus {
  Active              @map("active")
  Inactive            @map("inactive")
  PendingVerification @map("pending_verification")

  @@schema("app")
}

enum TenantStatus {
  Active   @map("active")
  Inactive @map("inactive")

  @@schema("app")
}

enum TenantSubscriptionType {
  Trial    @map("trial")
  Standard @map("standard")
  Premium  @map("premium")

  @@schema("app")
}

enum GlossaryType {
  Global @map("global")
  Self   @map("self")

  @@schema("app")
}

enum GlossaryStatus {
  Active   @map("active")
  Inactive @map("inactive")

  @@schema("app")
}

enum ProjectStatus {
  New       @map("new")
  OnGoing   @map("ongoing")
  Completed @map("completed")

  @@schema("app")
}

enum ProjectFileStatus {
  Uploaded    @map("uploaded")
  Classifying @map("classifying")
  Classified  @map("classified")
  Marking     @map("marking")
  Marked      @map("marked")
  Extracting  @map("extracting")
  Extracted   @map("extracted")
  Cancelled   @map("cancelled")

  @@schema("app")
}

enum FileState {
  Classification @map("classification")
  Mark           @map("mark")
  Extract        @map("extract")

  @@schema("app")
}

enum FileStateStatus {
  Queued     @map("queued")
  Processing @map("processing")
  Completed  @map("completed")
  Failed     @map("failed")

  @@schema("app")
}

enum ProjectFileSubStatus {
  InProgress @map("in_progress")
  Completed  @map("completed")
  Failed     @map("failed")

  @@schema("app")
}

model Tenant {
  id               String                 @id @default(uuid()) @db.Uuid
  code             String                 @unique @map("code") @db.VarChar(255)
  name             String                 @unique @map("name") @db.VarChar(255)
  subscriptionType TenantSubscriptionType @default(Standard) @map("subscription_type")
  maxActiveUsers   Int                    @map("max_active_users") @db.Integer
  type             TenantType             @default(Customer)
  status           TenantStatus           @default(Active)
  createdAt        DateTime               @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt        DateTime               @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  isDeleted        Boolean                @default(false) @map("is_deleted") @db.Boolean
  lastModifiedBy   String                 @map("last_modified_by") @db.Uuid

  // Indexes for tenant list queries and filtering
  @@index([isDeleted, status], name: "idx_tenant_active_filter")
  @@index([isDeleted, name], name: "idx_tenant_name_search")
  @@index([isDeleted, code], name: "idx_tenant_code_search")
  @@index([isDeleted, subscriptionType], name: "idx_tenant_subscription_filter")
  @@index([isDeleted, createdAt, id], name: "idx_tenant_pagination_created")
  @@index([isDeleted, updatedAt, id], name: "idx_tenant_pagination_updated")
  @@map("tenant")
  @@schema("app")
}

model User {
  id             String     @id @default(uuid()) @db.Uuid
  name           String     @map("name") @db.VarChar(255)
  displayName    String     @map("display_name") @db.VarChar(255)
  emailId        String     @unique @map("email_id") @db.VarChar(255)
  role           UserRole   @map("role")
  status         UserStatus @default(PendingVerification)
  tenantId       String     @map("tenant_id") @db.Uuid
  createdAt      DateTime   @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt      DateTime   @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  isDeleted      Boolean    @default(false) @map("is_deleted") @db.Boolean
  lastModifiedBy String     @map("last_modified_by") @db.Uuid

  projects ProjectUser[] @relation("ProjectUserToUser")

  // Indexes for user queries and tenant isolation
  @@index([tenantId, isDeleted], name: "idx_user_tenant_active")
  @@index([tenantId, isDeleted, status], name: "idx_user_tenant_status")
  @@index([tenantId, isDeleted, role], name: "idx_user_tenant_role")
  @@index([tenantId, isDeleted, name], name: "idx_user_tenant_name_search")
  @@index([tenantId, isDeleted, emailId], name: "idx_user_tenant_email_search")
  @@index([tenantId, isDeleted, createdAt, id], name: "idx_user_tenant_pagination_created")
  @@index([tenantId, isDeleted, updatedAt, id], name: "idx_user_tenant_pagination_updated")
  @@index([emailId, isDeleted], name: "idx_user_email_lookup")
  @@map("user")
  @@schema("app")
}

model Glossary {
  id             String         @id @default(uuid()) @db.Uuid
  name           String         @unique @map("name") @db.VarChar(255)
  type           GlossaryType   @map("type")
  remark         String?        @map("remark") @db.VarChar(255)
  status         GlossaryStatus @default(Active)
  tenantId       String         @map("tenant_id") @db.Uuid
  createdAt      DateTime       @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt      DateTime       @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  isDeleted      Boolean        @default(false) @map("is_deleted") @db.Boolean
  lastModifiedBy String         @map("last_modified_by") @db.Uuid

  items GlossaryItem[] @relation("GlossaryToGlossaryItem")

  // Indexes for glossary queries with Global/Self type access patterns
  @@index([isDeleted, tenantId], name: "idx_glossary_tenant_active")
  @@index([isDeleted, type], name: "idx_glossary_type_filter")
  @@index([isDeleted, status], name: "idx_glossary_status_filter")
  @@index([isDeleted, name], name: "idx_glossary_name_search")
  @@index([isDeleted, createdAt, id], name: "idx_glossary_pagination_created")
  @@index([isDeleted, updatedAt, id], name: "idx_glossary_pagination_updated")
  @@index([tenantId, isDeleted, type], name: "idx_glossary_tenant_type_access")
  @@map("glossary")
  @@schema("app")
}

model GlossaryItem {
  id             String         @id @default(uuid()) @db.Uuid
  glossaryId     String         @map("glossary_id") @db.Uuid
  code           String         @map("code") @db.VarChar(10)
  name           String         @map("name") @db.VarChar(255)
  referenceCode  String         @map("reference_code") @db.VarChar(255)
  type           GlossaryType   @map("type")
  status         GlossaryStatus @default(Active)
  tenantId       String         @map("tenant_id") @db.Uuid
  createdAt      DateTime       @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt      DateTime       @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  isDeleted      Boolean        @default(false) @map("is_deleted") @db.Boolean
  lastModifiedBy String         @map("last_modified_by") @db.Uuid

  glossary        Glossary          @relation("GlossaryToGlossaryItem", fields: [glossaryId], references: [id])
  projectCategory ProjectCategory[] @relation("ProjectCategoryToGlossaryItem")

  @@unique([glossaryId, id])
  // Indexes for glossary item queries and filtering
  @@index([glossaryId, isDeleted], name: "idx_glossary_item_glossary_active")
  @@index([glossaryId, isDeleted, status], name: "idx_glossary_item_status_filter")
  @@index([glossaryId, isDeleted, name], name: "idx_glossary_item_name_search")
  @@index([glossaryId, isDeleted, code], name: "idx_glossary_item_code_search")
  @@index([glossaryId, isDeleted, referenceCode], name: "idx_glossary_item_ref_code_search")
  @@index([glossaryId, isDeleted, createdAt, id], name: "idx_glossary_item_pagination_created")
  @@index([glossaryId, isDeleted, updatedAt, id], name: "idx_glossary_item_pagination_updated")
  @@index([tenantId, isDeleted], name: "idx_glossary_item_tenant_active")
  @@index([referenceCode, glossaryId, isDeleted], name: "idx_glossary_item_ref_code_unique")
  @@map("glossary_item")
  @@schema("app")
}

model Project {
  id                 String        @id @default(uuid()) @db.Uuid
  code               String        @unique @map("code") @db.VarChar(10)
  vessel             String        @map("vessel_name") @db.VarChar(100)
  hullNumber         String        @map("hull_number") @db.VarChar(50)
  imoNumber          String        @map("imo_number") @db.VarChar(7)
  vesselDeliveryDate DateTime?     @map("vessel_delivery_date") @db.Timestamp(6)
  status             ProjectStatus @default(New)
  tenantId           String        @map("tenant_id") @db.Uuid
  createdAt          DateTime      @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt          DateTime      @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  isDeleted          Boolean       @default(false) @map("is_deleted") @db.Boolean
  lastModifiedBy     String        @map("last_modified_by") @db.Uuid

  categories ProjectCategory[] @relation("ProjectToProjectCategory")
  users      ProjectUser[]     @relation("ProjectToProjectUser")
  files      ProjectFile[]     @relation("ProjectToFile")

  // Indexes for project list queries and filtering
  @@index([tenantId, isDeleted], name: "idx_project_tenant_active")
  @@index([tenantId, isDeleted, status], name: "idx_project_status_filter")
  @@index([tenantId, isDeleted, vessel], name: "idx_project_vessel_search")
  @@index([tenantId, isDeleted, code], name: "idx_project_code_search")
  @@index([tenantId, isDeleted, createdAt, id], name: "idx_project_pagination_created")
  @@index([tenantId, isDeleted, updatedAt, id], name: "idx_project_pagination_updated")
  @@map("project")
  @@schema("app")
}

model ProjectCategory {
  id                 String   @id @default(uuid()) @db.Uuid
  projectId          String   @map("project_id") @db.Uuid
  glossaryCategoryId String   @map("glossary_category_id") @db.Uuid
  glossaryItemId     String   @map("glossary_item_id") @db.Uuid
  tenantId           String   @map("tenant_id") @db.Uuid
  createdAt          DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt          DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  isDeleted          Boolean  @default(false) @map("is_deleted") @db.Boolean
  lastModifiedBy     String   @map("last_modified_by") @db.Uuid

  project  Project      @relation("ProjectToProjectCategory", fields: [projectId], references: [id])
  category GlossaryItem @relation("ProjectCategoryToGlossaryItem", fields: [glossaryCategoryId, glossaryItemId], references: [glossaryId, id])

  // Indexes for project category queries
  @@index([projectId, isDeleted], name: "idx_project_category_project_active")
  @@index([glossaryItemId, isDeleted], name: "idx_project_category_item_active")
  @@index([tenantId, isDeleted], name: "idx_project_category_tenant_active")
  @@map("project_category")
  @@schema("app")
}

model ProjectUser {
  id             String   @id @default(uuid()) @db.Uuid
  projectId      String   @map("project_id") @db.Uuid
  userId         String   @map("user_id") @db.Uuid
  tenantId       String   @map("tenant_id") @db.Uuid
  createdAt      DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt      DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  isDeleted      Boolean  @default(false) @map("is_deleted") @db.Boolean
  lastModifiedBy String   @map("last_modified_by") @db.Uuid

  project Project @relation("ProjectToProjectUser", fields: [projectId], references: [id])
  user    User    @relation("ProjectUserToUser", fields: [userId], references: [id])

  // Indexes for project user queries
  @@index([projectId, isDeleted], name: "idx_project_user_project_active")
  @@index([userId, isDeleted], name: "idx_project_user_user_active")
  @@index([tenantId, isDeleted], name: "idx_project_user_tenant_active")
  @@map("project_user")
  @@schema("app")
}

model ProjectFile {
  id                String               @id @db.Uuid
  projectId         String               @map("project_id") @db.Uuid
  name              String               @map("name") @db.VarChar(255)
  rootFolderPath    String               @map("root_folder_path") @db.Text
  originalFilePath  String?              @map("original_file_path") @db.Text
  objectStoragePath String               @map("object_storage_path") @db.Text
  contentType       String               @map("content_type") @db.VarChar(255)
  size              Int                  @map("size") @db.Integer
  batchNumber       Int                  @map("batch_number") @db.Integer
  status            ProjectFileStatus    @map("status")
  subStatus         ProjectFileSubStatus @map("sub_status")
  assetName         String?              @map("asset_name") @db.VarChar(255)
  manualsCategory   String?              @map("manuals_category") @db.VarChar(255)
  pageCount         Int?                 @map("page_count") @db.Integer
  tenantId          String               @map("tenant_id") @db.Uuid
  createdAt         DateTime             @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt         DateTime             @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  isDeleted         Boolean              @default(false) @map("is_deleted") @db.Boolean
  lastModifiedBy    String               @map("last_modified_by") @db.Uuid

  project    Project                   @relation("ProjectToFile", fields: [projectId], references: [id])
  fileStates ProjectFileStateHistory[] @relation("ProjectFileToProjectFileStateHistory")

  @@map("project_file")
  @@schema("app")
}

model ProjectFileStateHistory {
  id             String          @id @default(uuid()) @db.Uuid
  fileId         String          @map("file_id") @db.Uuid
  runId          Int             @map("run_id")
  state          FileState       @map("state")
  status         FileStateStatus @map("status")
  systemData     Json?           @map("system_data")
  userFeedback   Json?           @map("user_feedback")
  tenantId       String          @map("tenant_id") @db.Uuid
  createdAt      DateTime        @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt      DateTime        @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  isDeleted      Boolean         @default(false) @map("is_deleted") @db.Boolean
  lastModifiedBy String          @map("last_modified_by") @db.Uuid

  projectFile ProjectFile @relation("ProjectFileToProjectFileStateHistory", fields: [fileId], references: [id])

  @@unique([fileId, runId, state, isDeleted])
  @@map("project_file_state_history")
  @@schema("app")
}
