/*
  Warnings:

  - A unique constraint covering the columns `[glossary_id,id]` on the table `glossary_item` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `glossary_category_id` to the `project_category` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "app"."project_category" DROP CONSTRAINT "project_category_glossary_item_id_fkey";

-- AlterTable
ALTER TABLE "app"."project_category" ADD COLUMN     "glossary_category_id" UUID NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "glossary_item_glossary_id_id_key" ON "app"."glossary_item"("glossary_id", "id");

-- AddForeignKey
ALTER TABLE "app"."project_category" ADD CONSTRAINT "project_category_glossary_category_id_glossary_item_id_fkey" FOREIGN KEY ("glossary_category_id", "glossary_item_id") REFERENCES "app"."glossary_item"("glossary_id", "id") ON DELETE RESTRICT ON UPDATE CASCADE;
