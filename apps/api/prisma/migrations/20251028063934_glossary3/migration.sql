/*
  Warnings:

  - You are about to drop the `glossary-item` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "app"."glossary-item" DROP CONSTRAINT "glossary-item_glossary_id_fkey";

-- DropTable
DROP TABLE "app"."glossary-item";

-- CreateTable
CREATE TABLE "app"."glossary_item" (
    "id" UUID NOT NULL,
    "glossary_id" UUID NOT NULL,
    "code" VARCHAR(10) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "reference_code" VARCHAR(255) NOT NULL,
    "type" "app"."GlossaryType" NOT NULL,
    "status" "app"."GlossaryStatus" NOT NULL DEFAULT 'active',
    "tenant_id" UUID NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "last_modified_by" UUID NOT NULL,

    CONSTRAINT "glossary_item_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "app"."glossary_item" ADD CONSTRAINT "glossary_item_glossary_id_fkey" FOREIGN KEY ("glossary_id") REFERENCES "app"."glossary"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
