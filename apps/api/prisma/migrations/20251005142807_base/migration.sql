-- CreateTable
CREATE TABLE "app"."tenant" (
    "id" UUID NOT NULL,
    "tenant_id" VARCHAR(255) NOT NULL,
    "company_name" VARCHAR(255) NOT NULL,
    "admin_name" VARCHAR(255) NOT NULL,
    "email_id" VARCHAR(255) NOT NULL,
    "subscription_type" VARCHAR(50) NOT NULL,
    "contact_no" VARCHAR(15) NOT NULL,
    "cap_on_users" INTEGER NOT NULL,
    "address" TEXT NOT NULL,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "first_time_user" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "tenant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "app"."user" (
    "id" UUID NOT NULL,
    "first_name" VARCHAR(100) NOT NULL,
    "last_name" VARCHAR(100),
    "display_name" VARCHAR(200) NOT NULL,
    "email_id" VARCHAR(255) NOT NULL,
    "role" VARCHAR(50) NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "tenant_id" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_login_at" TIMESTAMP,
    "first_time_user" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "user_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tenant_tenant_id_key" ON "app"."tenant"("tenant_id");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_email_id_key" ON "app"."tenant"("email_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_email_id_key" ON "app"."user"("email_id");

-- AddForeignKey
ALTER TABLE "app"."user" ADD CONSTRAINT "user_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "app"."tenant"("tenant_id") ON DELETE RESTRICT ON UPDATE CASCADE;
