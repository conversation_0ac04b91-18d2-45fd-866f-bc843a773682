-- CreateEnum
CREATE TYPE "app"."ProjectStatus" AS ENUM ('new', 'ongoing', 'completed');

-- CreateTable
CREATE TABLE "app"."project" (
    "id" UUID NOT NULL,
    "code" VARCHAR(10) NOT NULL,
    "vessel_name" VARCHAR(100) NOT NULL,
    "hull_number" VARCHAR(50) NOT NULL,
    "imo_number" VARCHAR(7) NOT NULL,
    "vessel_delivery_date" TIMESTAMP(6),
    "status" "app"."ProjectStatus" NOT NULL DEFAULT 'new',
    "tenant_id" UUID NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "last_modified_by" UUID NOT NULL,

    CONSTRAINT "project_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "app"."project_category" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "glossary_item_id" UUID NOT NULL,
    "tenant_id" UUID NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "last_modified_by" UUID NOT NULL,

    CONSTRAINT "project_category_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "app"."project_user" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "tenant_id" UUID NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "last_modified_by" UUID NOT NULL,

    CONSTRAINT "project_user_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "project_code_key" ON "app"."project"("code");

-- CreateIndex
CREATE INDEX "idx_glossary_tenant_active" ON "app"."glossary"("is_deleted", "tenant_id");

-- CreateIndex
CREATE INDEX "idx_glossary_type_filter" ON "app"."glossary"("is_deleted", "type");

-- CreateIndex
CREATE INDEX "idx_glossary_status_filter" ON "app"."glossary"("is_deleted", "status");

-- CreateIndex
CREATE INDEX "idx_glossary_name_search" ON "app"."glossary"("is_deleted", "name");

-- CreateIndex
CREATE INDEX "idx_glossary_pagination_created" ON "app"."glossary"("is_deleted", "created_at", "id");

-- CreateIndex
CREATE INDEX "idx_glossary_pagination_updated" ON "app"."glossary"("is_deleted", "updated_at", "id");

-- CreateIndex
CREATE INDEX "idx_glossary_tenant_type_access" ON "app"."glossary"("tenant_id", "is_deleted", "type");

-- CreateIndex
CREATE INDEX "idx_glossary_item_glossary_active" ON "app"."glossary_item"("glossary_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_glossary_item_status_filter" ON "app"."glossary_item"("glossary_id", "is_deleted", "status");

-- CreateIndex
CREATE INDEX "idx_glossary_item_name_search" ON "app"."glossary_item"("glossary_id", "is_deleted", "name");

-- CreateIndex
CREATE INDEX "idx_glossary_item_code_search" ON "app"."glossary_item"("glossary_id", "is_deleted", "code");

-- CreateIndex
CREATE INDEX "idx_glossary_item_ref_code_search" ON "app"."glossary_item"("glossary_id", "is_deleted", "reference_code");

-- CreateIndex
CREATE INDEX "idx_glossary_item_pagination_created" ON "app"."glossary_item"("glossary_id", "is_deleted", "created_at", "id");

-- CreateIndex
CREATE INDEX "idx_glossary_item_pagination_updated" ON "app"."glossary_item"("glossary_id", "is_deleted", "updated_at", "id");

-- CreateIndex
CREATE INDEX "idx_glossary_item_tenant_active" ON "app"."glossary_item"("tenant_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_glossary_item_ref_code_unique" ON "app"."glossary_item"("reference_code", "glossary_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_tenant_active_filter" ON "app"."tenant"("is_deleted", "status");

-- CreateIndex
CREATE INDEX "idx_tenant_name_search" ON "app"."tenant"("is_deleted", "name");

-- CreateIndex
CREATE INDEX "idx_tenant_code_search" ON "app"."tenant"("is_deleted", "code");

-- CreateIndex
CREATE INDEX "idx_tenant_subscription_filter" ON "app"."tenant"("is_deleted", "subscription_type");

-- CreateIndex
CREATE INDEX "idx_tenant_pagination_created" ON "app"."tenant"("is_deleted", "created_at", "id");

-- CreateIndex
CREATE INDEX "idx_tenant_pagination_updated" ON "app"."tenant"("is_deleted", "updated_at", "id");

-- CreateIndex
CREATE INDEX "idx_user_tenant_active" ON "app"."user"("tenant_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_user_tenant_status" ON "app"."user"("tenant_id", "is_deleted", "status");

-- CreateIndex
CREATE INDEX "idx_user_tenant_role" ON "app"."user"("tenant_id", "is_deleted", "role");

-- CreateIndex
CREATE INDEX "idx_user_tenant_name_search" ON "app"."user"("tenant_id", "is_deleted", "name");

-- CreateIndex
CREATE INDEX "idx_user_tenant_email_search" ON "app"."user"("tenant_id", "is_deleted", "email_id");

-- CreateIndex
CREATE INDEX "idx_user_tenant_pagination_created" ON "app"."user"("tenant_id", "is_deleted", "created_at", "id");

-- CreateIndex
CREATE INDEX "idx_user_tenant_pagination_updated" ON "app"."user"("tenant_id", "is_deleted", "updated_at", "id");

-- CreateIndex
CREATE INDEX "idx_user_email_lookup" ON "app"."user"("email_id", "is_deleted");

-- AddForeignKey
ALTER TABLE "app"."project_category" ADD CONSTRAINT "project_category_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "app"."project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "app"."project_category" ADD CONSTRAINT "project_category_glossary_item_id_fkey" FOREIGN KEY ("glossary_item_id") REFERENCES "app"."glossary_item"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "app"."project_user" ADD CONSTRAINT "project_user_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "app"."project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "app"."project_user" ADD CONSTRAINT "project_user_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "app"."user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
