/*
  Warnings:

  - The values [pending_verification] on the enum `TenantStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "app"."TenantStatus_new" AS ENUM ('active', 'inactive');
ALTER TABLE "app"."tenant" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "app"."tenant" ALTER COLUMN "status" TYPE "app"."TenantStatus_new" USING ("status"::text::"app"."TenantStatus_new");
ALTER TYPE "app"."TenantStatus" RENAME TO "TenantStatus_old";
ALTER TYPE "app"."TenantStatus_new" RENAME TO "TenantStatus";
DROP TYPE "app"."TenantStatus_old";
ALTER TABLE "app"."tenant" ALTER COLUMN "status" SET DEFAULT 'active';
COMMIT;

-- AlterTable
ALTER TABLE "app"."tenant" ALTER COLUMN "status" SET DEFAULT 'active';
