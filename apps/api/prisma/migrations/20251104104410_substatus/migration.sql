/*
  Warnings:

  - Added the required column `sub_status` to the `project_file` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "app"."ProjectFileSubStatus" AS ENUM ('in_progress', 'completed', 'failed');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "app"."ProjectFileStatus" ADD VALUE 'classifying';
ALTER TYPE "app"."ProjectFileStatus" ADD VALUE 'marking';
ALTER TYPE "app"."ProjectFileStatus" ADD VALUE 'extracting';

-- AlterTable
ALTER TABLE "app"."project_file" ADD COLUMN     "sub_status" "app"."ProjectFileSubStatus" NOT NULL,
ALTER COLUMN "status" DROP DEFAULT;
