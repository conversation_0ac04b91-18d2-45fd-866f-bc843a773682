-- CreateEnum
CREATE TYPE "app"."PorjectFileStatus" AS ENUM ('uploaded', 'classified', 'marked', 'extracted');

-- CreateTable
CREATE TABLE "app"."project_file" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "root_path" TEXT NOT NULL,
    "url" TEXT,
    "object_path" TEXT NOT NULL,
    "size" INTEGER NOT NULL,
    "batch_number" INTEGER NOT NULL,
    "status" "app"."PorjectFileStatus" NOT NULL DEFAULT 'uploaded',
    "content_type" VARCHAR(255) NOT NULL,
    "tenant_id" UUID NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "last_modified_by" UUID NOT NULL,

    CONSTRAINT "project_file_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_project_file_project_active" ON "app"."project_file"("project_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_project_file_tenant_active" ON "app"."project_file"("tenant_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_project_tenant_active" ON "app"."project"("tenant_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_project_status_filter" ON "app"."project"("tenant_id", "is_deleted", "status");

-- CreateIndex
CREATE INDEX "idx_project_vessel_search" ON "app"."project"("tenant_id", "is_deleted", "vessel_name");

-- CreateIndex
CREATE INDEX "idx_project_code_search" ON "app"."project"("tenant_id", "is_deleted", "code");

-- CreateIndex
CREATE INDEX "idx_project_pagination_created" ON "app"."project"("tenant_id", "is_deleted", "created_at", "id");

-- CreateIndex
CREATE INDEX "idx_project_pagination_updated" ON "app"."project"("tenant_id", "is_deleted", "updated_at", "id");

-- CreateIndex
CREATE INDEX "idx_project_category_project_active" ON "app"."project_category"("project_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_project_category_item_active" ON "app"."project_category"("glossary_item_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_project_category_tenant_active" ON "app"."project_category"("tenant_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_project_user_project_active" ON "app"."project_user"("project_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_project_user_user_active" ON "app"."project_user"("user_id", "is_deleted");

-- CreateIndex
CREATE INDEX "idx_project_user_tenant_active" ON "app"."project_user"("tenant_id", "is_deleted");

-- AddForeignKey
ALTER TABLE "app"."project_file" ADD CONSTRAINT "project_file_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "app"."project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
