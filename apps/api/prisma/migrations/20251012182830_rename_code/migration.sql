/*
  Warnings:

  - You are about to drop the column `tenant_id` on the `tenant` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[code]` on the table `tenant` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `code` to the `tenant` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "app"."tenant_tenant_id_key";

-- AlterTable
ALTER TABLE "app"."tenant" DROP COLUMN "tenant_id",
ADD COLUMN     "code" VARCHAR(255) NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "tenant_code_key" ON "app"."tenant"("code");
