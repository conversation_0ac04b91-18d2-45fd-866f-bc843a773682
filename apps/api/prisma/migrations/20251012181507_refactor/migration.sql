/*
  Warnings:

  - The values [normal] on the enum `TenantType` will be removed. If these variants are still used in the database, this will fail.
  - The values [superadmin,admin] on the enum `UserRole` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `address` on the `tenant` table. All the data in the column will be lost.
  - You are about to drop the column `cap_on_users` on the `tenant` table. All the data in the column will be lost.
  - You are about to drop the column `company_name` on the `tenant` table. All the data in the column will be lost.
  - You are about to drop the column `contact_no` on the `tenant` table. All the data in the column will be lost.
  - You are about to drop the column `email_id` on the `tenant` table. All the data in the column will be lost.
  - You are about to drop the column `first_time_user` on the `tenant` table. All the data in the column will be lost.
  - The `subscription_type` column on the `tenant` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `first_name` on the `user` table. All the data in the column will be lost.
  - You are about to drop the column `first_time_user` on the `user` table. All the data in the column will be lost.
  - You are about to drop the column `last_login_at` on the `user` table. All the data in the column will be lost.
  - You are about to drop the column `last_name` on the `user` table. All the data in the column will be lost.
  - You are about to drop the `user_lookup` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[admin_mail_id]` on the table `tenant` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[admin_contact_number]` on the table `tenant` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `admin_contact_number` to the `tenant` table without a default value. This is not possible if the table is not empty.
  - Added the required column `admin_mail_id` to the `tenant` table without a default value. This is not possible if the table is not empty.
  - Added the required column `last_modified_by` to the `tenant` table without a default value. This is not possible if the table is not empty.
  - Added the required column `max_active_users` to the `tenant` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name` to the `tenant` table without a default value. This is not possible if the table is not empty.
  - Added the required column `last_modified_by` to the `user` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name` to the `user` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `tenant_id` on the `user` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "app"."TenantSubscriptionType" AS ENUM ('trial', 'standard', 'premium');

-- AlterEnum
ALTER TYPE "app"."TenantStatus" ADD VALUE 'pending_verification';

-- AlterEnum
BEGIN;
CREATE TYPE "app"."TenantType_new" AS ENUM ('platform', 'customer');
ALTER TABLE "app"."tenant" ALTER COLUMN "type" DROP DEFAULT;
ALTER TABLE "app"."tenant" ALTER COLUMN "type" TYPE "app"."TenantType_new" USING ("type"::text::"app"."TenantType_new");
ALTER TYPE "app"."TenantType" RENAME TO "TenantType_old";
ALTER TYPE "app"."TenantType_new" RENAME TO "TenantType";
DROP TYPE "app"."TenantType_old";
ALTER TABLE "app"."tenant" ALTER COLUMN "type" SET DEFAULT 'customer';
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "app"."UserRole_new" AS ENUM ('super_admin', 'tenant_admin', 'project_handler');
ALTER TABLE "app"."user" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "app"."user" ALTER COLUMN "role" TYPE "app"."UserRole_new" USING ("role"::text::"app"."UserRole_new");
ALTER TYPE "app"."UserRole" RENAME TO "UserRole_old";
ALTER TYPE "app"."UserRole_new" RENAME TO "UserRole";
DROP TYPE "app"."UserRole_old";
COMMIT;

-- DropForeignKey
ALTER TABLE "app"."user" DROP CONSTRAINT "user_tenant_id_fkey";

-- DropForeignKey
ALTER TABLE "app"."user_lookup" DROP CONSTRAINT "user_lookup_tenant_id_fkey";

-- DropIndex
DROP INDEX "app"."tenant_email_id_key";

-- AlterTable
ALTER TABLE "app"."tenant" DROP COLUMN "address",
DROP COLUMN "cap_on_users",
DROP COLUMN "company_name",
DROP COLUMN "contact_no",
DROP COLUMN "email_id",
DROP COLUMN "first_time_user",
ADD COLUMN     "admin_contact_number" VARCHAR(15) NOT NULL,
ADD COLUMN     "admin_mail_id" VARCHAR(255) NOT NULL,
ADD COLUMN     "is_deleted" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "last_modified_by" UUID NOT NULL,
ADD COLUMN     "max_active_users" INTEGER NOT NULL,
ADD COLUMN     "name" VARCHAR(255) NOT NULL,
DROP COLUMN "subscription_type",
ADD COLUMN     "subscription_type" "app"."TenantSubscriptionType" NOT NULL DEFAULT 'standard',
ALTER COLUMN "type" SET DEFAULT 'customer',
ALTER COLUMN "status" SET DEFAULT 'pending_verification';

-- AlterTable
ALTER TABLE "app"."user" DROP COLUMN "first_name",
DROP COLUMN "first_time_user",
DROP COLUMN "last_login_at",
DROP COLUMN "last_name",
ADD COLUMN     "is_deleted" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "last_modified_by" UUID NOT NULL,
ADD COLUMN     "name" VARCHAR(255) NOT NULL,
ALTER COLUMN "display_name" SET DATA TYPE VARCHAR(255),
DROP COLUMN "tenant_id",
ADD COLUMN     "tenant_id" UUID NOT NULL,
ALTER COLUMN "role" DROP DEFAULT;

-- DropTable
DROP TABLE "app"."user_lookup";

-- CreateIndex
CREATE UNIQUE INDEX "tenant_admin_mail_id_key" ON "app"."tenant"("admin_mail_id");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_admin_contact_number_key" ON "app"."tenant"("admin_contact_number");
