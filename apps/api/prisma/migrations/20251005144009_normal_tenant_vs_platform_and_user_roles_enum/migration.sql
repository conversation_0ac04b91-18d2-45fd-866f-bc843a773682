/*
  Warnings:

  - The `role` column on the `user` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "app"."TenantType" AS ENUM ('platform', 'normal');

-- CreateEnum
CREATE TYPE "app"."UserRole" AS ENUM ('superadmin', 'admin', 'project_handler');

-- AlterTable
ALTER TABLE "app"."tenant" ADD COLUMN     "type" "app"."TenantType" NOT NULL DEFAULT 'normal';

-- AlterTable
ALTER TABLE "app"."user" DROP COLUMN "role",
ADD COLUMN     "role" "app"."UserRole" NOT NULL DEFAULT 'project_handler';
