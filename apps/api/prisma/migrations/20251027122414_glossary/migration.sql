-- CreateEnum
CREATE TYPE "app"."GlossaryType" AS ENUM ('global', 'self');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "app"."GlossaryStatus" AS ENUM ('active', 'inactive');

-- CreateTable
CREATE TABLE "app"."glossary" (
    "id" UUID NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "type" "app"."GlossaryType" NOT NULL,
    "reamrk" VARCHAR(255) NOT NULL,
    "status" "app"."GlossaryStatus" NOT NULL DEFAULT 'active',
    "tenant_id" UUID NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "last_modified_by" UUID NOT NULL,

    CONSTRAINT "glossary_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "app"."glossary-item" (
    "id" UUID NOT NULL,
    "glossary_id" UUID NOT NULL,
    "code" VARCHAR(10) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "reference_code" VARCHAR(255) NOT NULL,
    "status" "app"."GlossaryStatus" NOT NULL DEFAULT 'active',
    "tenant_id" UUID NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "last_modified_by" UUID NOT NULL,

    CONSTRAINT "glossary-item_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "glossary_name_key" ON "app"."glossary"("name");

-- AddForeignKey
ALTER TABLE "app"."glossary-item" ADD CONSTRAINT "glossary-item_glossary_id_fkey" FOREIGN KEY ("glossary_id") REFERENCES "app"."glossary"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
