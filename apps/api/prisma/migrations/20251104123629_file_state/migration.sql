-- CreateEnum
CREATE TYPE "app"."FileState" AS ENUM ('classification', 'mark', 'extract');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "app"."FileStateStatus" AS ENUM ('queued', 'processing', 'completed', 'failed');

-- AlterEnum
ALTER TYPE "app"."ProjectFileStatus" ADD VALUE 'cancelled';

-- DropIndex
DROP INDEX "app"."idx_project_file_project_active";

-- DropIndex
DROP INDEX "app"."idx_project_file_tenant_active";

-- AlterTable
ALTER TABLE "app"."project_file" ADD COLUMN     "asset_name" VARCHAR(255),
ADD COLUMN     "manuals_category" VARCHAR(255),
ADD COLUMN     "page_count" INTEGER;

-- CreateTable
CREATE TABLE "app"."project_file_state_history" (
    "id" UUID NOT NULL,
    "file_id" UUID NOT NULL,
    "run_id" INTEGER NOT NULL,
    "state" "app"."FileState" NOT NULL,
    "status" "app"."FileStateStatus" NOT NULL,
    "system_data" JSONB,
    "user_feedback" JSONB,
    "tenant_id" UUID NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "last_modified_by" UUID NOT NULL,

    CONSTRAINT "project_file_state_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "project_file_state_history_file_id_run_id_state_is_deleted_key" ON "app"."project_file_state_history"("file_id", "run_id", "state", "is_deleted");

-- AddForeignKey
ALTER TABLE "app"."project_file_state_history" ADD CONSTRAINT "project_file_state_history_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "app"."project_file"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
