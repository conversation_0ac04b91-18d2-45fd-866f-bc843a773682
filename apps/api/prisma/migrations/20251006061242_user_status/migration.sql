/*
  Warnings:

  - You are about to drop the column `is_active` on the `user` table. All the data in the column will be lost.

*/
-- <PERSON><PERSON><PERSON>num
CREATE TYPE "app"."UserStatus" AS ENUM ('pending_verification', 'active', 'inactive');

-- CreateEnum
CREATE TYPE "app"."TenantStatus" AS ENUM ('active', 'inactive');

-- AlterTable
ALTER TABLE "app"."tenant" ADD COLUMN     "status" "app"."TenantStatus" NOT NULL DEFAULT 'active';

-- AlterTable
ALTER TABLE "app"."user" DROP COLUMN "is_active",
ADD COLUMN     "status" "app"."UserStatus" NOT NULL DEFAULT 'pending_verification';
