import { defineConfig, type Options } from "tsup";
import fs from "fs-extra";

export default defineConfig((options: Options) => ({
  entry: ["src/**/*.ts"],
  clean: true,
  sourcemap: true,
  format: ["cjs"],
  ...options,
  // onSuccess: async () => {
  //   // copy templates into dist so runtime can still find them
  //   await fs.copy("src/config/email/templates", "dist/config/email/templates");
  // },
}));
