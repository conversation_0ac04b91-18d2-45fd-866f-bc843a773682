{"name": "api", "version": "0.0.0", "type": "module", "private": true, "scripts": {"start": "node dist/index.cjs", "dev": "prisma generate && tsup --watch --onSuccess \"node dist/index.cjs\"", "build": "tsup", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "check-types": "tsc --noEmit", "build:types": "tsc -p tsconfig.types.json", "bundle:types": "api-extractor run --local --verbose", "generate:types": "pnpm run build:types && pnpm run bundle:types", "lint": "eslint src/ --max-warnings 0", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jest": {"preset": "@repo/jest-presets/node"}, "dependencies": {"@azure/communication-email": "^1.0.0", "@azure/storage-blob": "^12.29.1", "@prisma/client": "^6.17.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "busboy": "^1.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "4.21.2", "fs-extra": "^11.3.2", "handlebars": "^4.7.8", "http-status-codes": "^2.3.0", "js-yaml": "^4.1.0", "openapi3-ts": "^4.5.0", "pino": "^10.0.0", "pino-pretty": "^13.1.2", "prisma": "^6.17.0", "reflect-metadata": "^0.2.2", "routing-controllers": "^0.11.3", "routing-controllers-openapi": "^5.0.0", "source-map-support": "^0.5.21", "supertokens-node": "^21.0.2", "swagger-ui-express": "^5.0.1", "uuid": "^13.0.0"}, "devDependencies": {"@anatine/esbuild-decorators": "^0.2.19", "@jest/globals": "^29.7.0", "@microsoft/api-extractor": "^7.53.1", "@repo/eslint-config": "*", "@repo/jest-presets": "*", "@repo/typescript-config": "*", "@swc/core": "^1.13.5", "@types/body-parser": "^1.19.5", "@types/busboy": "^1.5.4", "@types/cors": "^2.8.17", "@types/express": "4.17.21", "@types/fs-extra": "^11.0.4", "@types/handlebars": "^4.1.0", "@types/js-yaml": "^4.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.3", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.8", "class-validator-jsonschema": "^5.1.0", "eslint": "^9.31.0", "jest": "^29.7.0", "supertest": "^7.1.0", "tsup": "^8.5.0", "typescript": "5.8.2"}}