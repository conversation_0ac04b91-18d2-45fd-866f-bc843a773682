# Marine Document Classification API

A FastAPI-based serverless application that classifies marine PDF documents using Mistral OCR and OpenAI's ChatGPT.

## ✅ Implementation Status

**COMPLETED**: The Mistral OCR + ChatGPT classification pipeline is fully implemented and working!

## Features

- **Mistral OCR Integration**: High-quality text extraction from PDF documents using Mistral's Pixtral vision model
- **Marine Document Classification**: PMS Manual vs Non-PMS Manual classification
- **Automatic Machinery Identification**: Identifies main machinery for PMS manuals
- **Dual Pipeline**: Mistral OCR for text extraction + ChatGPT for classification
- **Multiple Endpoints**: Separate OCR and classification endpoints
- **FastAPI**: Automatic API documentation with Swagger UI
- **Environment Configuration**: Easy setup with environment variables

## Setup

1. **Install dependencies:**

   ```bash
   cd apps/serverless
   pip install -r requirements.txt
   ```

2. **Configure environment variables:**
   Create a `.env` file in the `apps/serverless` directory:

   ```
   OPENAI_API_KEY=your_openai_api_key_here
   MISTRAL_API_KEY=your_mistral_api_key_here
   ```

3. **Start the server:**

   ```bash
   # Development mode with auto-reload
   npm run dev
   # or
   python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000

   # Production mode
   npm start
   # or
   python -m uvicorn main:app --host 0.0.0.0 --port 8000
   ```

## Usage

### API Endpoints

- **POST /classify** - Upload and classify a PDF document (uses Mistral OCR + ChatGPT)
- **POST /ocr** - Extract text from PDF using Mistral OCR only
- **GET /** - API information and available endpoints
- **GET /docs** - Interactive API documentation (Swagger UI)

### Testing

1. **Test server connectivity:**

   ```bash
   python test_server.py
   ```

2. **Test Mistral OCR + Classification pipeline:**

   ```bash
   python test_mistral_ocr.py
   ```

3. **Test marine document classification:**

   ```bash
   python test_classification.py
   ```

4. **Use the interactive docs:**
   Open http://localhost:8000/docs in your browser and test the endpoints

5. **Manual API calls:**

   **Classification:**

   ```bash
   curl -X POST "http://localhost:8000/classify" \
        -H "Content-Type: multipart/form-data" \
        -F "file=@6N165L-EW Parts Catalog1-7.pdf"
   ```

   **OCR Only:**

   ```bash
   curl -X POST "http://localhost:8000/ocr" \
        -H "Content-Type: multipart/form-data" \
        -F "file=@6N165L-EW Parts Catalog1-7.pdf"
   ```

### Expected Response Format

```json
{
  "classification": "PMS",
  "main_machinery": "Engine Control System"
}
```

or

```json
{
  "classification": "Non-PMS",
  "main_machinery": null
}
```

### Response Format

```json
{
  "category": "Maintenance Manual",
  "confidence": 0.95,
  "reasoning": "Document contains technical procedures and maintenance instructions"
}
```

## Project Structure

```
apps/serverless/
├── main.py                    # FastAPI application entry point
├── functions/
│   ├── classify_doc.py        # Document classification handler (Mistral OCR + ChatGPT)
│   └── mistral_ocr.py         # Mistral OCR text extraction
├── schemas/
│   └── document.py            # Pydantic response models
├── requirements.txt           # Python dependencies
├── package.json              # NPM scripts
├── .env                      # Environment variables (create this)
├── test_server.py            # Server connectivity test
├── test_mistral_ocr.py       # Mistral OCR + Classification pipeline test
├── test_classification.py    # Classification test
├── demo.py                   # Demo script
└── README.md                 # This file
```

## Troubleshooting

### Environment Variables Not Loading

- Ensure `.env` file is in the `apps/serverless` directory
- Check that `OPENAI_API_KEY` is properly set in the `.env` file
- The application loads environment variables automatically on startup

### Server Won't Start

- Check if port 8000 is already in use
- Verify all dependencies are installed: `pip install -r requirements.txt`
- Check the terminal output for specific error messages

### OpenAI API Errors

- Verify your OpenAI API key is valid and has sufficient credits
- Check that the API key has access to GPT-4o-mini model

### Mistral API Errors

- Verify your Mistral API key is valid and has sufficient credits
- Check that the API key has access to Pixtral-12b-2409 vision model
- Ensure both `OPENAI_API_KEY` and `MISTRAL_API_KEY` are set in your `.env` file
