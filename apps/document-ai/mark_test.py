import asyncio
import json
from utils.mark_util import pdf_to_base64_images
from features.mark_feature import handler

async def detect_spares_and_schedule_pages_using_images(pdf_path: str, max_concurrency: int = 6, zoom: float = 2.5):
    images = pdf_to_base64_images(pdf_path, zoom=zoom)
    sem = asyncio.Semaphore(max_concurrency)

    async def process_image(image_entry):
        async with sem:
            result = await handler(image_entry)
            try:
                data = json.loads(result.get("result", "{}"))
                data["page_number"] = image_entry["page_number"]
                return data
            except Exception:
                return None

    tasks = [asyncio.create_task(process_image(img)) for img in images]
    details = [r for r in await asyncio.gather(*tasks) if r]

    pages_with_spares = [d["page_number"] for d in details if d.get("has_spares")]
    pages_with_schedule = [d["page_number"] for d in details if d.get("has_schedule")]

    return pages_with_spares, pages_with_schedule, details

import os
import pandas as pd
from pathlib import Path
from datetime import datetime

def append_results_to_excel(excel_path: str, pdf_path: str, pages_spares: list[int], pages_schedule: list[int]):
    pdf_name = str(Path(pdf_path).name)
    set_spares, set_sched = set(pages_spares or []), set(pages_schedule or [])
    pages_both = sorted(list(set_spares & set_sched))

    row = {
        "pdf_file": pdf_name,
        "pages_spares": ",".join(map(str, sorted(set_spares))) if set_spares else "",
        "pages_schedule": ",".join(map(str, sorted(set_sched))) if set_sched else "",
        "pages_both": ",".join(map(str, pages_both)) if pages_both else "",
        "count_spares": len(set_spares),
        "count_schedule": len(set_sched),
        "count_both": len(pages_both),
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    }

    if os.path.exists(excel_path):
        try:
            df = pd.read_excel(excel_path)
        except Exception:
            df = pd.DataFrame(columns=row.keys())
        df = pd.concat([df, pd.DataFrame([row])], ignore_index=True)
    else:
        df = pd.DataFrame([row])

    df.to_excel(excel_path, index=False)

import asyncio
import json
from pathlib import Path

if __name__ == "__main__":
    root_dir = "pdf"
    excel_out = "spares_schedule_results.xlsx"

    pdf_files = sorted([str(p) for p in Path(root_dir).glob("*.pdf")])

    if not pdf_files:
        print(f"⚠️  No PDFs found in: {root_dir}")
        raise SystemExit(0)

    print(f"🔍 Found {len(pdf_files)} PDF(s) in: {root_dir}")

    for pdf_file in pdf_files:
        print(f"\n📘 Scanning PDF: {pdf_file}")
        try:
            pages_spares, pages_schedule, details = asyncio.run(
                detect_spares_and_schedule_pages_using_images(pdf_file)
            )

            out_json = str(Path(pdf_file).with_suffix("")) + "_details.json"
            with open(out_json, "w", encoding="utf-8") as f:
                json.dump(details, f, indent=2, ensure_ascii=False)
            print(f"💾 Saved: {out_json}")

            append_results_to_excel(excel_out, pdf_file, pages_spares, pages_schedule)
            print(f"📊 Updated: {excel_out}")

        except Exception as e:
            print(f"❌ Failed on {pdf_file}: {e}")
