from pydantic import BaseModel, Field
from typing import Literal, Union, Annotated, Optional
from datetime import datetime
from enum import Enum


class EventType(str, Enum):
    classify = "classify"
    mark = "mark"
    extract = "extract"


# --- Payload types ---
class BasePayload(BaseModel):
    requestId: str
    tenantId: str
    userId: str
    projectId: str
    fileId: str
    runId: int
    pathPrefix: str
    objectStoragePath: str


class ClassifyPayload(BasePayload):
    pass


class MarkPayload(BasePayload):
    sessionId: str


class ExtractPayload(BasePayload):
    pass


# --- Event types ---
class BaseEvent(BaseModel):
    eventId: str
    eventAt: datetime
    eventVersion: int
    retryCount: int
    maxRetryCount: int


class ClassifyEvent(BaseEvent):
    eventType: Literal[EventType.classify]
    payload: ClassifyPayload


class MarkEvent(BaseEvent):
    eventType: Literal[EventType.mark]
    payload: MarkPayload


class ExtractEvent(BaseEvent):
    eventType: Literal[EventType.extract]
    payload: ExtractPayload


EventQueueRequest = Annotated[
    Union[ClassifyEvent, MarkEvent, ExtractEvent],
    <PERSON>(discriminator="eventType"),
]
