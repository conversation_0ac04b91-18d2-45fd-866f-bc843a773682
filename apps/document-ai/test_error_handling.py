#!/usr/bin/env python3
"""
Test script for error handling improvements in document-ai.
This script tests various error scenarios to ensure proper error codes and queue messages.
"""

import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from core.constants import STATUS, ERROR_CODES
from handlers.queue_handler import queue_classification_event, queue_marking_event
from handlers.classify_handler import handle_classify_event
from handlers.mark_handler import handle_mark_event
from models.events import ClassifyEvent, ClassifyPayload, MarkEvent, MarkPayload
from datetime import datetime


async def test_queue_error_structure():
    """Test that queue messages have the correct error structure."""
    print("Testing queue error structure...")
    
    # Mock the queue client
    with patch('core.config.get_queue_client') as mock_get_client:
        mock_client = AsyncMock()
        mock_get_client.return_value = mock_client
        
        # Mock context functions
        with patch('handlers.queue_handler.get_request_id', return_value='test-request-123'), \
             patch('handlers.queue_handler.get_context_dict', return_value={'test': 'context'}):
            
            # Test classification error with retry
            await queue_classification_event(
                file_id="test-file-123",
                status=STATUS.FAILED,
                error_code=ERROR_CODES.OPENAI_RATE_LIMIT_ERROR,
                error_reason="Rate limit exceeded",
                retry_after=60
            )
            
            # Verify the message structure
            mock_client.send_message.assert_called_once()
            sent_message = mock_client.send_message.call_args[0][0]
            message_data = json.loads(sent_message)
            
            # Check error structure
            assert "error" in message_data
            assert message_data["error"]["code"] == "OPENAI_RATE_LIMIT_ERROR"
            assert message_data["error"]["cause"] == "Rate limit exceeded"
            assert message_data["error"]["retryAfter"] == 60
            
            print("✅ Queue error structure test passed")


async def test_classification_agent_errors():
    """Test classification agent error handling."""
    print("Testing classification agent error handling...")
    
    # Create mock event
    mock_event = ClassifyEvent(
        eventType="classify",
        eventId="test-classify-123",
        eventAt=datetime.now(),
        eventVersion=1,
        retryCount=0,
        maxRetryCount=3,
        payload=ClassifyPayload(
            requestId="req-123",
            tenantId="tenant-123",
            userId="user-123",
            fileId="file-123",
            objectStoragePath="test/path/file.pdf"
        )
    )
    
    # Test rate limit error
    with patch('handlers.classify_handler.store_file_locally', return_value="/tmp/test.pdf"), \
         patch('builtins.open', create=True), \
         patch('base64.b64encode', return_value=b'test'), \
         patch('handlers.classify_handler.classification_agent') as mock_agent, \
         patch('handlers.classify_handler.queue_classification_event') as mock_queue:
        
        # Mock agent returning rate limit error
        mock_agent.return_value = {
            "error_code": "RATE_LIMIT_ERROR",
            "error": "OpenAI rate limit exceeded",
            "retryAfter": 120
        }
        
        try:
            await handle_classify_event(mock_event)
        except Exception:
            pass  # Expected to raise
        
        # Verify correct error code and retry_after were sent
        mock_queue.assert_called_with(
            file_id="file-123",
            status=STATUS.FAILED,
            error_code=ERROR_CODES.OPENAI_RATE_LIMIT_ERROR,
            error_reason="OpenAI rate limit exceeded",
            retry_after=120
        )
        
        print("✅ Classification agent error handling test passed")


async def test_mark_agent_errors():
    """Test mark agent error handling."""
    print("Testing mark agent error handling...")
    
    # Create mock event
    mock_event = MarkEvent(
        eventType="mark",
        eventId="test-mark-123",
        eventAt=datetime.now(),
        eventVersion=1,
        retryCount=0,
        maxRetryCount=3,
        payload=MarkPayload(
            requestId="req-123",
            tenantId="tenant-123",
            userId="user-123",
            fileId="file-123",
            objectStoragePath="test/path/file.pdf",
            sessionId="session-123"
        )
    )
    
    # Test critical OpenAI API error
    with patch('handlers.mark_handler.check_existing_results', return_value=None), \
         patch('handlers.mark_handler.check_images_exist', return_value=True), \
         patch('handlers.mark_handler.download_images_from_storage', return_value=[
             {"page_number": 1, "image_base64": "test"}
         ]), \
         patch('tempfile.mkdtemp', return_value="/tmp/test"), \
         patch('handlers.mark_handler.mark_agent') as mock_agent, \
         patch('handlers.mark_handler.queue_marking_event') as mock_queue:
        
        # Mock agent returning critical API error
        mock_agent.return_value = {
            "error_code": "OPENAI_API_ERROR",
            "error": "OpenAI API connection failed"
        }
        
        try:
            await handle_mark_event(mock_event)
        except Exception:
            pass  # Expected to raise
        
        # Verify correct error code was sent
        mock_queue.assert_called_with(
            file_id="file-123",
            status=STATUS.FAILED,
            error_code=ERROR_CODES.OPENAI_API_ERROR,
            error_reason="OpenAI API connection failed"
        )
        
        print("✅ Mark agent error handling test passed")


async def test_storage_errors():
    """Test storage error handling."""
    print("Testing storage error handling...")
    
    # Create mock event
    mock_event = ClassifyEvent(
        eventType="classify",
        eventId="test-classify-123",
        eventAt=datetime.now(),
        eventVersion=1,
        retryCount=0,
        maxRetryCount=3,
        payload=ClassifyPayload(
            requestId="req-123",
            tenantId="tenant-123",
            userId="user-123",
            fileId="file-123",
            objectStoragePath="test/path/file.pdf"
        )
    )
    
    # Test file download error
    with patch('handlers.classify_handler.store_file_locally', side_effect=Exception("Blob not found")), \
         patch('handlers.classify_handler.queue_classification_event') as mock_queue:
        
        try:
            await handle_classify_event(mock_event)
        except Exception:
            pass  # Expected to raise
        
        # Verify correct error code was sent
        mock_queue.assert_called_with(
            file_id="file-123",
            status=STATUS.FAILED,
            error_code=ERROR_CODES.FILE_DOWNLOAD_ERROR,
            error_reason="Failed to download file from storage: Blob not found"
        )
        
        print("✅ Storage error handling test passed")


async def main():
    """Run all error handling tests."""
    print("Starting error handling tests...\n")
    
    try:
        await test_queue_error_structure()
        await test_classification_agent_errors()
        await test_mark_agent_errors()
        await test_storage_errors()
        
        print("\n🎉 All error handling tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
