
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException
from dotenv import load_dotenv

from core.exceptions import http_exception_handler, general_exception_handler
from core.logger import logger
from api.classify_router import router as classify_route
from api.mark_router import router as mark_route
from middleware.context_middleware import EventContextMiddleware

# Load environment variables from .env file
load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    from core.config import init_azure_queues
    await init_azure_queues()

    yield

    # Shutdown
    from core.config import cleanup_azure_queues
    await cleanup_azure_queues()


# --- FASTAPI APP SETUP ---
app = FastAPI(title="File Event Processor", version="1.0.0", lifespan=lifespan)

# --- MIDDLEWARE ---
app.add_middleware(EventContextMiddleware)

# --- API ENDPOINTS ---

app.include_router(classify_route)
app.include_router(mark_route)

@app.get("/health", status_code=200)
async def health_check():
    """
    Health check endpoint to ensure the service is up and running.
    """
    logger.debug("Health check pinged.")
    return {"status": "healthy"}


app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)


