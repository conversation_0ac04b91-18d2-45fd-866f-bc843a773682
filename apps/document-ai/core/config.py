import os
from azure.storage.queue.aio import QueueClient
from core.logger import logger

AZURE_CONN_STR = os.getenv("AZURE_CONN_STR")
CLASSIFICATION_QUEUE_NAME = os.getenv("CLASSIFICATION_QUEUE_NAME")
MARKING_QUEUE_NAME = os.getenv("MARKING_QUEUE_NAME")
EXTRACTION_QUEUE_NAME = os.getenv("EXTRACTION_QUEUE_NAME")

# Three separate queue clients for different operations
classification_queue_client: QueueClient | None = None
marking_queue_client: QueueClient | None = None
extraction_queue_client: QueueClient | None = None

async def init_azure_queues():
    """Initialize all three Azure Queue clients."""
    global classification_queue_client, marking_queue_client, extraction_queue_client

    if not AZURE_CONN_STR:
        raise RuntimeError("Azure connection string missing - AZURE_CONN_STR not set")

    # Initialize classification queue
    if CLASSIFICATION_QUEUE_NAME:
        classification_queue_client = QueueClient.from_connection_string(AZURE_CONN_STR, CLASSIFICATION_QUEUE_NAME)
        logger.info(f"Classification queue client initialized: {CLASSIFICATION_QUEUE_NAME}")
    else:
        logger.warning("CLASSIFICATION_QUEUE_NAME not set")

    # Initialize marking queue
    if MARKING_QUEUE_NAME:
        marking_queue_client = QueueClient.from_connection_string(AZURE_CONN_STR, MARKING_QUEUE_NAME)
        logger.info(f"Marking queue client initialized: {MARKING_QUEUE_NAME}")
    else:
        logger.warning("MARKING_QUEUE_NAME not set")

    # Initialize extraction queue
    if EXTRACTION_QUEUE_NAME:
        extraction_queue_client = QueueClient.from_connection_string(AZURE_CONN_STR, EXTRACTION_QUEUE_NAME)
        logger.info(f"Extraction queue client initialized: {EXTRACTION_QUEUE_NAME}")
    else:
        logger.warning("EXTRACTION_QUEUE_NAME not set")

    logger.info("Azure Queue clients initialized successfully")

async def cleanup_azure_queues():
    """Properly close all Azure Queue clients to avoid unclosed session warnings."""
    global classification_queue_client, marking_queue_client, extraction_queue_client

    clients = [
        ("Classification", classification_queue_client),
        ("Marking", marking_queue_client),
        ("Extraction", extraction_queue_client)
    ]

    for name, client in clients:
        if client is not None:
            try:
                await client.close()
                logger.info(f"{name} queue client closed successfully")
            except Exception as e:
                logger.warning(f"Error closing {name} queue client: {e}")

    # Reset all clients to None
    classification_queue_client = None
    marking_queue_client = None
    extraction_queue_client = None

def get_queue_client(operation_type: str) -> QueueClient:
    """Get the appropriate queue client for the operation type."""
    if operation_type == "classification":
        if classification_queue_client is None:
            raise RuntimeError("Classification queue client not initialized")
        return classification_queue_client
    elif operation_type == "marking":
        if marking_queue_client is None:
            raise RuntimeError("Marking queue client not initialized")
        return marking_queue_client
    elif operation_type == "extraction":
        if extraction_queue_client is None:
            raise RuntimeError("Extraction queue client not initialized")
        return extraction_queue_client
    else:
        raise ValueError(f"Unknown operation type: {operation_type}")
