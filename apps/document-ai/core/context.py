"""
Request context management for storing event data throughout the request lifecycle.
"""
from contextvars import Context<PERSON><PERSON>
from typing import Optional, Dict, Any
from models.events import EventQueueRequest

# Context variables for storing request-specific data
_event_context: ContextVar[Optional[EventQueueRequest]] = ContextVar('event_context', default=None)
_request_id_context: ContextVar[Optional[str]] = ContextVar('request_id_context', default=None)


def set_event_context(event: EventQueueRequest) -> None:
    """Set the current event context for the request."""
    _event_context.set(event)
    _request_id_context.set(event.eventId)


def get_event_context() -> Optional[EventQueueRequest]:
    """Get the current event context."""
    return _event_context.get()


def get_request_id() -> Optional[str]:
    """Get the current request ID (eventId)."""
    return _request_id_context.get()


def get_file_id() -> Optional[str]:
    """Get the current file ID from context."""
    event = get_event_context()
    return event.payload.fileId if event else None


def get_tenant_id() -> Optional[str]:
    """Get the current tenant ID from context."""
    event = get_event_context()
    return event.payload.tenantId if event else None


def get_project_id() -> Optional[str]:
    """Get the current project ID from context."""
    event = get_event_context()
    return event.payload.projectId if event else None


def get_user_id() -> Optional[str]:
    """Get the current user ID from context."""
    event = get_event_context()
    return event.payload.userId if event else None


def get_context_dict() -> Dict[str, Any]:
    """Get all context data as a dictionary for logging."""
    event = get_event_context()
    if not event:
        return {}
    
    return {
        "eventId": event.eventId,
        "eventType": event.eventType.value if hasattr(event.eventType, 'value') else event.eventType,
        "fileId": event.payload.fileId,
        "tenantId": event.payload.tenantId,
        "projectId": event.payload.projectId,
        "userId": event.payload.userId,
        "requestId": event.payload.requestId,
        "retryCount": event.retryCount,
    }


def clear_context() -> None:
    """Clear the current context."""
    _event_context.set(None)
    _request_id_context.set(None)
