import aiofiles
from azure.storage.blob.aio import BlobClient
from core.logger import logger
from pathlib import Path
import tempfile
import os
import uuid

BLOB_STORAGE_CONNECTION_STRING = os.getenv("BLOB_STORAGE_CONNECTION_STRING")
BLOB_STORAGE_CONTAINER_NAME = os.getenv("BLOB_STORAGE_CONTAINER_NAME")


async def store_file_locally(object_storage_path: str) -> str:
    """
    Downloads a blob from Azure and stores it in a unique temporary local file.
    Streamed download to avoid memory spikes.
    """
    blob_client = BlobClient.from_connection_string(
        BLOB_STORAGE_CONNECTION_STRING,
        container_name=BLOB_STORAGE_CONTAINER_NAME,
        blob_name=object_storage_path,
    )

    unique_dir = Path(tempfile.mkdtemp(prefix="pdf_"))
    file_name = f"{uuid.uuid4()}_{Path(object_storage_path).name}"
    temp_path = unique_dir / file_name

    try:
        logger.debug(f"⬇️ Downloading {object_storage_path} to {temp_path}")

        stream = await blob_client.download_blob()
        async with aiofiles.open(temp_path, "wb") as f:
            async for chunk in stream.chunks():
                await f.write(chunk)

        logger.debug(f"✅ File stored temporarily at {temp_path}")
        return str(temp_path)

    except Exception as e:
        logger.exception(f"❌ Failed to store blob locally: {e}")
        raise

    finally:
        await blob_client.close()
