from enum import Enum


class STATUS(str, Enum):
    ACCEPTED="ACCEPTED"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

class ERROR_CODES(str, Enum):
    # General errors
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    INVALID_EVENT = "INVALID_EVENT"

    # File and storage errors
    FILE_DOWNLOAD_ERROR = "FILE_DOWNLOAD_ERROR"
    FILE_UPLOAD_ERROR = "FILE_UPLOAD_ERROR"
    FILE_PROCESSING_ERROR = "FILE_PROCESSING_ERROR"
    STORAGE_CONNECTION_ERROR = "STORAGE_CONNECTION_ERROR"

    # Classification specific errors
    CLASSIFICATION_ERROR = "CLASSIFICATION_ERROR"
    PDF_TRUNCATION_ERROR = "PDF_TRUNCATION_ERROR"
    PDF_ENCODING_ERROR = "PDF_ENCODING_ERROR"
    CLASSIFICATION_VALIDATION_ERROR = "CLASSIFICATION_VALIDATION_ERROR"

    # Marking specific errors
    MARKING_ERROR = "MARKING_ERROR"
    IMAGE_GENERATION_ERROR = "IMAGE_GENERATION_ERROR"
    IMAGE_UPLOAD_ERROR = "IMAGE_UPLOAD_ERROR"
    IMAGE_DOWNLOAD_ERROR = "IMAGE_DOWNLOAD_ERROR"
    MARK_AGENT_ERROR = "MARK_AGENT_ERROR"
    RESULT_STORAGE_ERROR = "RESULT_STORAGE_ERROR"

    # Extraction specific errors
    EXTRACTION_ERROR = "EXTRACTION_ERROR"

    # OpenAI API errors
    OPENAI_RATE_LIMIT_ERROR = "OPENAI_RATE_LIMIT_ERROR"
    OPENAI_API_ERROR = "OPENAI_API_ERROR"
    OPENAI_VALIDATION_ERROR = "OPENAI_VALIDATION_ERROR"

    # Queue errors
    QUEUE_CONNECTION_ERROR = "QUEUE_CONNECTION_ERROR"
    QUEUE_SEND_ERROR = "QUEUE_SEND_ERROR"
