import logging
import j<PERSON>
from typing import Any, Dict, Optional


class ContextAwareLogger:
    """Logger that automatically includes context information in all log messages."""

    def __init__(self, name: str = "app"):
        self._logger = logging.getLogger(name)
        self._logger.setLevel(logging.INFO)

        if not self._logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "[%(asctime)s] [%(levelname)s] %(name)s - %(message)s"
            )
            handler.setFormatter(formatter)
            self._logger.addHandler(handler)

    def _get_context_prefix(self) -> str:
        """Get context prefix for log messages."""
        try:
            from core.context import get_context_dict
            context = get_context_dict()
            if context:
                # Create a compact context string
                parts = []
                if context.get("requestId"):
                    parts.append(f"x-request-id={context['requestId']}")
                if context.get("eventId"):
                    parts.append(f"x-event-id={context['eventId']}")
                if context.get("fileId"):
                    parts.append(f"x-file-id={context['fileId']}")
                if context.get("eventType"):
                    parts.append(f"x-event-type={context['eventType']}")

                if parts:
                    return f"[{' | '.join(parts)}] "
        except ImportError:
            pass
        return ""

    def _format_message(self, message: str, extra: Optional[Dict[str, Any]] = None) -> str:
        """Format message with context and extra data."""
        context_prefix = self._get_context_prefix()
        formatted_message = f"{context_prefix}{message}"

        if extra:
            extra_str = json.dumps(extra, default=str, separators=(',', ':'))
            formatted_message += f" | {extra_str}"

        return formatted_message

    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        self._logger.debug(self._format_message(message, extra))

    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        self._logger.info(self._format_message(message, extra))

    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        self._logger.warning(self._format_message(message, extra))

    def error(self, message: str, extra: Optional[Dict[str, Any]] = None):
        self._logger.error(self._format_message(message, extra))

    def exception(self, message: str, extra: Optional[Dict[str, Any]] = None):
        self._logger.exception(self._format_message(message, extra))

    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None):
        self._logger.critical(self._format_message(message, extra))


# Create the global logger instance
logger = ContextAwareLogger("app")
