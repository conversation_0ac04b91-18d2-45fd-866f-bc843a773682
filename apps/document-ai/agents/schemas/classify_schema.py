from pydantic import BaseModel, Field
from typing import Optional, Literal

class ClassificationResponse(BaseModel):
    classification: Literal["PMS", "Non-PMS","Uncertain"] = Field(..., description="Document classification: PMS Manual or Non-PMS Manual or Uncertain")
    main_machinery: Optional[str] = Field(None, description="Main machinery identified if it's a PMS Manual")
    reasoning: Optional[str] = Field(None, description="Reasoning for the classification in 1-2 lines")
