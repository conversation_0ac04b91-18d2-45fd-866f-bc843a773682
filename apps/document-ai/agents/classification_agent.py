import os
import base64
from openai import OpenAI, RateLimitError
from dotenv import load_dotenv
from utils.classify_util import truncate_pdf_with_sampling
from agents.schemas.classify_schema import ClassificationResponse
import tempfile
import re
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception
from core.logger import logger

load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))


def should_retry(exception):
    """
    Retry for all exceptions except OpenAI RateLimitError (429).
    """
    if isinstance(exception, RateLimitError):
        return False
    return True


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception(should_retry),
)
def call_openai_with_retry(messages):
    """
    Call OpenAI API with retry logic for all errors except 429 (RateLimitError).
    """
    response = client.responses.create(
        model="gpt-5-mini",
        input=messages,
    )
    return response

async def handler(event: dict):
    """
    Marine document classifier using direct PDF attach (no file_id upload)
    Input: event = { "file_base64": "...", "filename": "..." }
    Output: ClassificationResponse schema
    """
    try:
        # Decode base64 PDF
        pdf_bytes = base64.b64decode(event["file_base64"])

        # Save temporarily to truncate if needed
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_in:
            tmp_in.write(pdf_bytes)
            tmp_in_path = tmp_in.name

        # Smart truncation
        try:
            truncated_path = truncate_pdf_with_sampling(
                tmp_in_path,
                max_pages=50,
                size_limit_mb=32.0
            )
        except Exception as e:
            logger.exception(f"PDF truncation failed: {e}")
            return {
                "error_code": "PDF_TRUNCATION_ERROR",
                "error": f"PDF truncation failed: {str(e)}"
            }

        # Read truncated PDF into memory
        try:
            with open(truncated_path, "rb") as f:
                truncated_bytes = f.read()

            # Encode to Base64
            base64_pdf = base64.b64encode(truncated_bytes).decode("utf-8")
        except Exception as e:
            logger.exception(f"Failed to read/encode truncated PDF: {e}")
            return {
                "error_code": "PDF_ENCODING_ERROR",
                "error": f"Failed to read or encode truncated PDF: {str(e)}"
            }

        prompt = """You are a maritime document classifier analyzing a scanned PDF segment from the file.

Your task is to determine whether this document belongs to a PMS (Planned Maintenance System) manual, a Non-PMS document, or is Uncertain.

========================================================
### PMS CLASSIFICATION CRITERIA

Assign a reasoning score from 0 to 10 reflecting how strongly the document matches PMS characteristics.

**PMS (Planned Maintenance System) manuals** are OEM or Shipyard-issued technical documents describing the operation, maintenance, and spare parts of specific machinery. They often include:
- Machinery particulars (model, capacity, RPM, power, pressure, etc.)
- Structured parts lists or exploded diagrams with columns like "Pos No", "Item Code", "Description", "Material", "Qty", "Remarks"
- Maintenance schedules (e.g., "Every 1000 hours", "Annually")
- Procedures or instructions for inspection, disassembly, lubrication, or reassembly
- Manufacturer or OEM identifiers (MAN, Yanmar, Wärtsilä, Mitsubishi, Daihatsu, Hyundai, etc.)
- Titles such as "Instruction Manual", "Maintenance Manual", "Operation Manual", "Spare Parts List", "Code Book", "Yard Delivery Manual"
- Sectioned layouts (e.g., "Section 3 – Lubrication System", "Part List", "Procedure 4.2") or labeled diagrams with position numbers

**Non-PMS documents** typically include:
- Design drawings or plans ("General Arrangement", "Piping Diagram", "Wiring Diagram", "System Layout")
- Reports or certificates ("Inspection Report", "Pressure Test Certificate", "Shop Trial Report")
- Booklets or calculations ("Stability Booklet", "NOx Technical File", "Loading Manual")
- Pages dominated by schematics, signatures, or test measurements rather than procedural or parts information

Additionally, treat as PMS any OEM or shipyard-issued system manuals describing machinery control, automation, or governor systems — even if they lack explicit maintenance schedules or parts lists. 
Examples include: "Remote Control System", "Governor System", "Alarm Monitoring System", "Control System Manual".

========================================================
### ANALYSIS STEPS

1. Identify the document's primary intent — maintenance/operation (PMS) vs. design/report (Non-PMS).
2. Check for PMS-specific keywords: "Instruction", "Maintenance", "Operation", "Part List", "Overhaul", "Lubrication", "Inspection".
3. Detect OEM or shipyard mentions (e.g., "Mitsubishi Heavy Industries", "Yard Delivery Manual").
4. Evaluate tables: PMS tables describe **parts**, Non-PMS tables show **measurements/tests**.
5. Examine layout structure: PMS = sectioned procedures or parts listings; Non-PMS = engineering diagrams or approvals.
6. If it's a Table of Contents or Index clearly from a PMS manual, classify as PMS.
7. If the page is ambiguous, incomplete, or contains only a title or partial system layout, classify as "Uncertain".

========================================================
### OUTPUT FORMAT

Return a **single JSON object** strictly matching this schema:

{
  "classification": "PMS" | "Non-PMS" | "Uncertain",
  "main_machinery": "exact name from text or null",
  "reasoning": "1-2 line explanation for the classification"
}

Where:
- `"classification"` is:
  - `"PMS"` if the document fits PMS criteria (score ≥ 6),
  - `"Non-PMS"` if it clearly does not (score ≤ 3),
  - `"Uncertain"` if it falls between 4 and 5 or if the content is ambiguous.
- `"main_machinery"` is the machinery name exactly as written in the text (do not infer or reword).
- `"reasoning"` briefly states why it was classified as such.

Do not include explanations or reasoning outside this JSON.
"""

        # Prepare the request payload
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "input_file",
                        "filename": event.get("filename", "document.pdf"),
                        "file_data": f"data:application/pdf;base64,{base64_pdf}",
                    },
                    {
                        "type": "input_text",
                        "text": prompt,
                    },
                ],
            }
        ]
        
        

        # Call the API with retry logic
        try:
            response = call_openai_with_retry(messages)
        except Exception as e:
            logger.exception(f"OpenAI API call failed: {e}")
            return {
                "error_code": "OPENAI_API_ERROR",
                "error": f"OpenAI API call failed: {str(e)}"
            }

        try:
            text_output = response.output_text.strip()

            # Strip ```json ... ``` or ``` ... ```
            text_output = re.sub(r"^```(?:json)?\s*|\s*```$", "", text_output, flags=re.IGNORECASE).strip()

            validated = ClassificationResponse.model_validate_json(text_output)
            return validated.model_dump()
        except Exception as e:
            logger.exception(f"Failed to parse/validate OpenAI response: {e}")
            return {
                "error_code": "OPENAI_VALIDATION_ERROR",
                "error": f"Failed to parse or validate OpenAI response: {str(e)}"
            }
    
    except RateLimitError as e:
        retry_after = None
        if hasattr(e, "response") and hasattr(e.response, "headers"):
            retry_after = e.response.headers.get("Retry-After")

        logger.warning(f"Rate limited: retry_after={retry_after}")
        return {
            "error_code": "RATE_LIMIT_ERROR",
            "error": "RateLimitError: OpenAI API rate limit exceeded",
            "retryAfter": retry_after or 60,  # Default 60s if not provided
        }

    except Exception as e:
        logger.exception("Classification handler error:")
        return {"error": str(e)}


    finally:
        try:
            os.remove(tmp_in_path)
            os.remove(truncated_path)
        except Exception:
            pass


#Anything other than 429 do tenacity
#explore retry after from openai header