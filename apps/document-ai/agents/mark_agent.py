import re
import json
from agents.schemas.mark_schema import PageResult
import os
from dotenv import load_dotenv
from openai import OpenAI
from openai import RateLimitError
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception
from core.logger import logger

load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))


def should_retry(exception):
    """
    Retry for all exceptions except OpenAI RateLimitError (429).
    """
    if isinstance(exception, RateLimitError):
        return False
    return True


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception(should_retry),
)
def call_openai_with_retry(messages):
    """
    Call OpenAI API with retry logic for all errors except 429 (RateLimitError).
    """
    response = client.responses.create(model="gpt-5-mini", input=messages)
    return response


async def handler(event: dict):
    """Classifies one image page via OpenAI Responses API."""
    try:
        img_data = event["image_base64"]
        if not img_data.startswith("data:"):
            img_data = f"data:image/png;base64,{img_data}"

        input_file_entry = {
            "type": "input_image",
            "image_url": img_data,
        }

        prompt = """
        You are a marine equipment maintenance data CLASSIFIER for PMS (Planned Maintenance System) integration.

        Task: For THIS SINGLE PDF PAGE, make TWO INDEPENDENT yes/no decisions:
        1) has_spares — does this page contain a SPARE PARTS LIST?
        2) has_schedule — does this page contain a MAINTENANCE SCHEDULE?

        Definitions (decide strictly from this page only):
        • Spare parts list = structured list/table/catalog/BOM of parts (e.g., columns like Pos/Item/Part No/Description/Qty). Mentions of “spares” in running text without a list/table ≠ spare parts list.
        • Maintenance schedule = time/usage-based intervals for maintenance (e.g., “every 1000 hours”, “weekly”, “monthly”, “annually”, “overhaul every X hours”, tables of intervals with tasks). Procedures without timing ≠ schedule.

        Exclusions (do NOT count as positive):
        • Installation/operation descriptions, theory, safety notes, general troubleshooting without intervals
        • Exploded drawings without a parts table
        • Generic references like “see maintenance section” without intervals
        • A few part names embedded in prose without a list/table
        • Cleaning/inspection steps without any frequency/interval

        Output RULES (very important):
        • Do NOT list any parts or schedules. Do NOT extract text. Do NOT add extra fields.
        • If unsure, return false with confidence ≤ 0.50.
        • Base your decision ONLY on visible content in THIS page.
        • Return STRICT JSON ONLY in EXACT schema below (no code fences, no prose):

        {
        "has_spares": true | false,
        "has_schedule": true | false,
        "confidence": 0.0-1.0,
        "reason": "short one-sentence explanation of what was found (or why neither was found)"
        }
        """

        messages = [{
            "role": "user",
            "content": [
                input_file_entry,
                {"type": "input_text", "text": prompt},
            ],
        }]

        response = call_openai_with_retry(messages)
        raw = (response.output_text or "").strip()

        raw = re.sub(r"^```(?:json)?\s*|\s*```$", "", raw, flags=re.IGNORECASE).strip()
        if not raw.startswith("{"):
            m = re.search(r"\{[\s\S]*\}\s*$", raw)
            if m:
                raw = m.group(0)

        data = json.loads(raw)
        return {"result": PageResult(**data).model_dump_json()}
    
    except RateLimitError as e:
        retry_after = None
        if hasattr(e, "response") and hasattr(e.response, "headers"):
            retry_after = e.response.headers.get("Retry-After")

        logger.warning(f"Rate limited: retry_after={retry_after}")
        return {
            "error_code": "RATE_LIMIT_ERROR",
            "error": "RateLimitError: OpenAI API rate limit exceeded",
            "retryAfter": retry_after or 60,  # Default 60s if not provided
        }
    except Exception as e:
        return {"error": str(e)}
