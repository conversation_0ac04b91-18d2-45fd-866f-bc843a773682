version: '3.8'

services:
  document-ai:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      # Add your environment variables here
      - PYTHONPATH=/app
      # Example environment variables (uncomment and modify as needed):
      # - OPENAI_API_KEY=${OPENAI_API_KEY}
      # - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      # - AZURE_STORAGE_CONNECTION_STRING=${AZURE_STORAGE_CONNECTION_STRING}
      # - AZURE_QUEUE_NAME=${AZURE_QUEUE_NAME}
    volumes:
      # Mount for development (comment out for production)
      - .:/app
      # Mount for logs
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a database service if needed
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: document_ai
  #     POSTGRES_USER: postgres
  #     POSTGRES_PASSWORD: password
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   restart: unless-stopped

# volumes:
#   postgres_data:
