from fastapi import APIRouter, HTTPException, Request
from models.events import EventQueueRequest
from core.logger import logger

from slowapi.util import get_remote_address
from slowapi import Limiter
from slowapi.errors import RateLimitExceeded
from core.constants import STATUS

router = APIRouter()
limiter = Limiter(key_func=get_remote_address)

@router.post("/api/v1/mark")
@limiter.limit("10/minute")  # rate limit
async def handle_event(request: Request, data: EventQueueRequest):
    current_retry = data.retryCount
    max_retry = data.maxRetryCount
    object_path = data.payload.objectStoragePath
    if current_retry > max_retry:
        raise HTTPException(status_code=500, detail="retry count reached")


    logger.info(f"Received marking event: object_path={object_path}, retryCount={current_retry}")
    logger.info("Processing Marking Event")

    # Keep your existing background task
    import asyncio
    from handlers.event_processor import process_event
    asyncio.create_task(process_event(data, event_type="mark"))

    return {"status": STATUS.ACCEPTED}
