# Document AI Error Handling Improvements

## Overview
This document outlines the comprehensive error handling improvements made to the document-ai service to ensure all failures are properly handled and sent to queues with appropriate error codes and retry logic.

## Key Improvements

### 1. Enhanced ERROR_CODES Enum (`core/constants.py`)
Added comprehensive error codes for different failure scenarios:

#### General Errors
- `UNKNOWN_ERROR` - Catch-all for unexpected errors
- `INVALID_EVENT` - Invalid event type received

#### File and Storage Errors
- `FILE_DOWNLOAD_ERROR` - Failed to download file from storage
- `FILE_UPLOAD_ERROR` - Failed to upload file to storage
- `FILE_PROCESSING_ERROR` - General file processing error
- `STORAGE_CONNECTION_ERROR` - Azure storage connection issues

#### Classification Specific Errors
- `CLASSIFICATION_ERROR` - General classification failure
- `PDF_TRUNCATION_ERROR` - Failed to truncate PDF for processing
- `PDF_ENCODING_ERROR` - Failed to encode PDF to base64
- `CLASSIFICATION_VALIDATION_ERROR` - Failed to validate classification response

#### Marking Specific Errors
- `MARKING_ERROR` - General marking failure
- `IMAGE_GENERATION_ERROR` - Failed to generate images from PDF
- `IMAGE_UPLOAD_ERROR` - Failed to upload images to storage
- `IMAGE_DOWNLOAD_ERROR` - Failed to download images from storage
- `MARK_AGENT_ERROR` - Mark agent processing error
- `RESULT_STORAGE_ERROR` - Failed to store marking results

#### OpenAI API Errors
- `OPENAI_RATE_LIMIT_ERROR` - OpenAI rate limit exceeded (429)
- `OPENAI_API_ERROR` - OpenAI API connection/request failed
- `OPENAI_VALIDATION_ERROR` - Failed to parse/validate OpenAI response

#### Queue Errors
- `QUEUE_CONNECTION_ERROR` - Queue connection failed
- `QUEUE_SEND_ERROR` - Failed to send message to queue

### 2. Updated Queue Handler (`handlers/queue_handler.py`)
- Added `retry_after` parameter to all queue functions
- Updated error structure to match requirements:
  ```json
  {
    "error": {
      "code": "ERROR_CODE",
      "cause": "Error description",
      "retryAfter": 60  // Optional, only for rate limits
    }
  }
  ```

### 3. Enhanced Classification Handler (`handlers/classify_handler.py`)
- **File Download Errors**: Catches storage download failures with `FILE_DOWNLOAD_ERROR`
- **PDF Encoding Errors**: Handles file read/encoding failures with `PDF_ENCODING_ERROR`
- **Agent Error Mapping**: Maps agent-specific errors to appropriate queue error codes
- **Rate Limit Handling**: Properly forwards `retryAfter` from OpenAI rate limits
- **Metadata Errors**: Graceful handling of PDF metadata extraction failures
- **Response Validation**: Catches classification response formatting errors

### 4. Enhanced Mark Handler (`handlers/mark_handler.py`)
- **Storage Connection Errors**: Handles failures in checking existing results/images
- **Image Processing Errors**: Separate error codes for download, generation, and upload failures
- **Agent Error Categorization**: 
  - Rate limit errors propagated with `retryAfter`
  - Critical API errors fail the entire job
  - Other errors allow partial processing to continue
- **Result Storage Errors**: Handles failures in uploading final JSON results
- **Partial Result Saving**: Continues to save partial results even when individual pages fail

### 5. Enhanced Agents (`agents/classification_agent.py`, `agents/mark_agent.py`)
- **PDF Truncation Errors**: Handles failures in PDF processing with `PDF_TRUNCATION_ERROR`
- **OpenAI API Errors**: Separates API connection failures from validation failures
- **Rate Limit Detection**: Properly extracts `Retry-After` header from OpenAI responses
- **Response Validation**: Handles JSON parsing and schema validation errors

### 6. Enhanced Utilities (`utils/classify_util.py`, `core/storage.py`)
- **PDF Processing**: Added error handling for PDF truncation operations
- **Storage Operations**: Enhanced blob download with specific error messages
- **Cleanup**: Proper cleanup of temporary files on errors

### 7. Updated Event Processor (`handlers/event_processor.py`)
- **Duplicate Error Prevention**: Prevents sending duplicate error messages to queue
- **Error Message Detection**: Identifies when handlers have already sent error notifications

## Error Flow Examples

### OpenAI Rate Limit Error
1. Agent detects `RateLimitError` from OpenAI
2. Extracts `Retry-After` header (defaults to 60s if missing)
3. Returns error with `RATE_LIMIT_ERROR` code
4. Handler maps to `OPENAI_RATE_LIMIT_ERROR` 
5. Queue message sent with `retryAfter` field

### File Download Error
1. Storage client fails to download blob
2. Handler catches exception
3. Sends `FILE_DOWNLOAD_ERROR` to queue immediately
4. Processing stops to avoid cascading failures

### Partial Mark Processing
1. Some pages fail due to non-critical errors
2. Processing continues for remaining pages
3. Partial results saved every 5 pages
4. Final results include all successful pages
5. Only critical errors (rate limits, API failures) stop entire job

## Testing
- Created verification script (`verify_error_handling.py`) to validate all improvements
- All error codes, queue structures, and handler patterns verified
- Error handling patterns tested across all components

## Benefits
1. **Granular Error Reporting**: Specific error codes help identify root causes
2. **Proper Retry Logic**: Rate limit errors include `retryAfter` for intelligent retries
3. **Fault Tolerance**: Partial processing continues when possible
4. **Consistent Structure**: All error messages follow the same format
5. **Debugging Support**: Detailed error messages aid in troubleshooting
6. **Queue Integration**: All failures properly notify downstream systems

## Queue Message Format
All failed status messages now follow this structure:
```json
{
  "status": "FAILED",
  "eventId": "request-id",
  "fileId": "file-id",
  "timestamp": "2025-01-01T00:00:00Z",
  "data": {},
  "context": {},
  "error": {
    "code": "SPECIFIC_ERROR_CODE",
    "cause": "Detailed error description",
    "retryAfter": 60  // Only present for rate limit errors
  }
}
```

This comprehensive error handling ensures that all failures in the document-ai pipeline are properly categorized, reported, and can be handled appropriately by downstream systems.
