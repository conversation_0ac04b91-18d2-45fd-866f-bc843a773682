#!/usr/bin/env python3
"""
Test script for the mark handler implementation.
This script tests the mark handler functionality without requiring a full server setup.
"""

import asyncio
import tempfile
import json
from pathlib import Path
from models.events import MarkEvent, MarkPayload
from handlers.mark_handler import handle_mark_event
from datetime import datetime


async def test_mark_handler():
    """Test the mark handler with a mock event."""
    
    # Create a mock event
    mock_event = MarkEvent(
        eventType="mark",
        eventId="test-mark-123",
        eventAt=datetime.now(),
        eventVersion=1,
        retryCount=0,
        maxRetryCount=3,
        payload=MarkPayload(
            requestId="req-123",
            tenantId="tenant-123", 
            userId="user-123",
            projectId="project-123",
            fileId="test-mark-file-123",
            runId=1,
            pathPrefix="test-prefix",
            objectStoragePath="83c57e66-cf38-4181-8d17-82c953e63ea1/projects/47731668-0aec-4de7-8571-302cdc070186/files/bc9d0a3f-e542-4c83-9a7e-29dd0bbbc473/test1234/Freshwater_Generator.pdf"
        )
    )
    
    print("🧪 Testing mark handler...")
    print(f"📄 File ID: {mock_event.payload.fileId}")
    print(f"📁 Object Storage Path: {mock_event.payload.objectStoragePath}")
    
    try:
        # Test the handler (this will download the actual PDF and process it)
        result = await handle_mark_event(mock_event, None)
        
        print("✅ Mark handler completed successfully!")
        print(f"📊 Results summary:")
        if result and "metadata" in result:
            metadata = result["metadata"]
            print(f"   - Total pages: {metadata.get('totalPages', 'N/A')}")
            print(f"   - Processed pages: {metadata.get('processedPages', 'N/A')}")
            print(f"   - Pages with spares: {metadata.get('pagesWithSpares', 'N/A')}")
            print(f"   - Pages with jobs: {metadata.get('pagesWithJobs', 'N/A')}")
        
        # Save results to local file for inspection
        if result:
            output_file = "test_mark_results.json"
            with open(output_file, "w") as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"💾 Results saved to: {output_file}")
        
        return result
        
    except Exception as e:
        print(f"❌ Mark handler failed: {e}")
        raise


async def test_mark_utilities():
    """Test individual mark utilities."""
    from utils.mark_util import check_images_exist, check_existing_results
    
    print("\n🔧 Testing mark utilities...")
    
    object_storage_path = "83c57e66-cf38-4181-8d17-82c953e63ea1/projects/47731668-0aec-4de7-8571-302cdc070186/files/bc9d0a3f-e542-4c83-9a7e-29dd0bbbc473/test1234/Freshwater_Generator.pdf"
    
    # Test if images exist
    images_exist = await check_images_exist(object_storage_path)
    print(f"📸 Images exist: {images_exist}")
    
    # Test if results exist
    existing_results = await check_existing_results(object_storage_path)
    print(f"📋 Existing results: {'Yes' if existing_results else 'No'}")
    
    if existing_results:
        print(f"   - Pages processed: {len(existing_results.get('pages', {}))}")


if __name__ == "__main__":
    print("🚀 Starting mark handler tests...")
    
    # Test utilities first
    asyncio.run(test_mark_utilities())
    
    # Then test the full handler (commented out to avoid long processing time)
    # Uncomment the line below to test the full handler
    # asyncio.run(test_mark_handler())
    
    print("\n✨ Tests completed!")
