#!/usr/bin/env python3
"""
Verification script for error handling improvements.
This script verifies that the error handling code structure is correct.
"""

import sys
import importlib.util
from pathlib import Path


def verify_constants():
    """Verify that ERROR_CODES enum has all required error codes."""
    print("Verifying ERROR_CODES...")
    
    # Load constants module
    constants_path = Path("core/constants.py")
    spec = importlib.util.spec_from_file_location("constants", constants_path)
    constants = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(constants)
    
    # Check that all expected error codes exist
    expected_codes = [
        "UNKNOWN_ERROR", "INVALID_EVENT",
        "FILE_DOWNLOAD_ERROR", "FILE_UPLOAD_ERROR", "FILE_PROCESSING_ERROR", "STORAGE_CONNECTION_ERROR",
        "CLASSIFICATION_ERROR", "PDF_TRUNCATION_ERROR", "PDF_ENCODING_ERROR", "CLASSIFICATION_VALIDATION_ERROR",
        "MARKING_ERROR", "IMAGE_GENERATION_ERROR", "IMAGE_UPLOAD_ERROR", "IMAGE_DOWNLOAD_ERROR", 
        "MARK_AGENT_ERROR", "RESULT_STORAGE_ERROR",
        "EXTRACTION_ERROR",
        "OPENAI_RATE_LIMIT_ERROR", "OPENAI_API_ERROR", "OPENAI_VALIDATION_ERROR",
        "QUEUE_CONNECTION_ERROR", "QUEUE_SEND_ERROR"
    ]
    
    for code in expected_codes:
        if not hasattr(constants.ERROR_CODES, code):
            print(f"❌ Missing error code: {code}")
            return False
        print(f"✅ Found error code: {code}")
    
    print("✅ All error codes verified")
    return True


def verify_queue_handler():
    """Verify that queue handler functions have retry_after parameter."""
    print("\nVerifying queue handler functions...")
    
    # Read queue handler file
    with open("handlers/queue_handler.py", "r") as f:
        content = f.read()
    
    # Check that all queue functions have retry_after parameter
    queue_functions = [
        "queue_classification_event",
        "queue_marking_event", 
        "queue_extraction_event",
        "_queue_event"
    ]
    
    for func in queue_functions:
        if f"retry_after: int | None = None" not in content:
            print(f"❌ Function {func} missing retry_after parameter")
            return False
        print(f"✅ Function {func} has retry_after parameter")
    
    # Check that error structure is correct
    if '"error": error_obj' not in content:
        print("❌ Queue handler missing proper error structure")
        return False
    
    if 'error_obj["retryAfter"] = retry_after' not in content:
        print("❌ Queue handler missing retryAfter handling")
        return False
    
    print("✅ Queue handler structure verified")
    return True


def verify_classification_handler():
    """Verify classification handler error handling."""
    print("\nVerifying classification handler...")
    
    with open("handlers/classify_handler.py", "r") as f:
        content = f.read()
    
    # Check for specific error handling patterns
    error_patterns = [
        "FILE_DOWNLOAD_ERROR",
        "PDF_ENCODING_ERROR", 
        "OPENAI_RATE_LIMIT_ERROR",
        "CLASSIFICATION_VALIDATION_ERROR",
        "retry_after=retry_after"
    ]
    
    for pattern in error_patterns:
        if pattern not in content:
            print(f"❌ Classification handler missing: {pattern}")
            return False
        print(f"✅ Found pattern: {pattern}")
    
    print("✅ Classification handler verified")
    return True


def verify_mark_handler():
    """Verify mark handler error handling."""
    print("\nVerifying mark handler...")
    
    with open("handlers/mark_handler.py", "r") as f:
        content = f.read()
    
    # Check for specific error handling patterns
    error_patterns = [
        "STORAGE_CONNECTION_ERROR",
        "IMAGE_DOWNLOAD_ERROR",
        "FILE_DOWNLOAD_ERROR",
        "IMAGE_GENERATION_ERROR",
        "IMAGE_UPLOAD_ERROR",
        "OPENAI_RATE_LIMIT_ERROR",
        "OPENAI_API_ERROR",
        "RESULT_STORAGE_ERROR",
        "retry_after=result.get"
    ]
    
    for pattern in error_patterns:
        if pattern not in content:
            print(f"❌ Mark handler missing: {pattern}")
            return False
        print(f"✅ Found pattern: {pattern}")
    
    print("✅ Mark handler verified")
    return True


def verify_agents():
    """Verify agent error handling."""
    print("\nVerifying agents...")
    
    # Check classification agent
    with open("agents/classification_agent.py", "r") as f:
        content = f.read()
    
    agent_patterns = [
        "PDF_TRUNCATION_ERROR",
        "PDF_ENCODING_ERROR",
        "OPENAI_API_ERROR",
        "OPENAI_VALIDATION_ERROR"
    ]
    
    for pattern in agent_patterns:
        if pattern not in content:
            print(f"❌ Classification agent missing: {pattern}")
            return False
        print(f"✅ Classification agent has: {pattern}")
    
    # Check mark agent
    with open("agents/mark_agent.py", "r") as f:
        content = f.read()
    
    for pattern in ["OPENAI_API_ERROR", "OPENAI_VALIDATION_ERROR"]:
        if pattern not in content:
            print(f"❌ Mark agent missing: {pattern}")
            return False
        print(f"✅ Mark agent has: {pattern}")
    
    print("✅ Agents verified")
    return True


def verify_utilities():
    """Verify utility error handling."""
    print("\nVerifying utilities...")
    
    # Check classify_util
    with open("utils/classify_util.py", "r") as f:
        content = f.read()
    
    if "PDF truncation failed" not in content:
        print("❌ classify_util missing error handling")
        return False
    
    print("✅ classify_util error handling verified")
    
    # Check storage
    with open("core/storage.py", "r") as f:
        content = f.read()
    
    if "Failed to initiate blob download" not in content:
        print("❌ storage missing error handling")
        return False
    
    print("✅ storage error handling verified")
    return True


def main():
    """Run all verification checks."""
    print("Starting error handling verification...\n")
    
    checks = [
        verify_constants,
        verify_queue_handler,
        verify_classification_handler,
        verify_mark_handler,
        verify_agents,
        verify_utilities
    ]
    
    all_passed = True
    for check in checks:
        try:
            if not check():
                all_passed = False
        except Exception as e:
            print(f"❌ Check failed with error: {e}")
            all_passed = False
    
    if all_passed:
        print("\n🎉 All error handling verifications passed!")
        print("\nSummary of improvements:")
        print("✅ Added comprehensive ERROR_CODES enum with specific error types")
        print("✅ Updated queue handlers to support retryAfter parameter")
        print("✅ Enhanced classification handler with granular error handling")
        print("✅ Enhanced mark handler with granular error handling")
        print("✅ Added error handling to agents with specific error codes")
        print("✅ Added error handling to utility functions")
        print("✅ Proper error structure: {code, cause, retryAfter?}")
        return 0
    else:
        print("\n❌ Some verifications failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
