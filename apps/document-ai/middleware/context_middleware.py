from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import <PERSON><PERSON><PERSON>pter, ValidationError
from core.context import set_event_context, clear_context
from core.logger import logger
from models.events import EventQueueRequest

import json


class EventContextMiddleware(BaseHTTPMiddleware):
    """
    Middleware that extracts and sets EventQueueRequest context
    from incoming POST requests to event endpoints.
    """

    EVENT_ENDPOINTS = {"/api/v1/classify", "/api/v1/mark", "/api/v1/extract"}

    async def dispatch(self, request: Request, call_next):
        try:
            if request.method == "POST" and request.url.path in self.EVENT_ENDPOINTS:
                body_bytes = await request.body()

                if body_bytes:
                    try:
                        body_dict = json.loads(body_bytes.decode("utf-8"))
                        event = TypeAdapter(EventQueueRequest).validate_python(body_dict)
                        set_event_context(event)
                      
                        logger.debug(f"[{event.eventId}] Event context set for {event.eventType}")
                    except ValidationError as e:
                        logger.warning(
                            f"Failed to parse EventQueueRequest for {request.url.path}: {e.errors()}"
                        )
                        raise HTTPException(status_code=400, detail="Invalid EventQueueRequest")
                    except json.JSONDecodeError as e:
                        logger.warning(f"Invalid JSON in request body: {e}")
                        raise HTTPException(status_code=400, detail="Malformed JSON body")
                    except Exception as e:
                        logger.warning(f"Unexpected error parsing EventQueueRequest: {e}")
                        raise HTTPException(status_code=400, detail="Invalid EventQueueRequest")

                # Re-inject the body so FastAPI can read it later
                async def receive():
                    return {"type": "http.request", "body": body_bytes, "more_body": False}

                request._receive = receive

            # Continue down the middleware stack
            response = await call_next(request)
            return response

        except HTTPException:
            raise
        except Exception as e:
            logger.exception(f"Unhandled error in EventContextMiddleware: {e}")
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error in context middleware"},
            )

        finally:
            clear_context()
          
