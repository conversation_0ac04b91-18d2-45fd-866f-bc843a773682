import tempfile
import fitz  # PyMuPDF
import os
import random      


# -------------------------------
# PDF truncation & sampling logic
# -------------------------------
def truncate_pdf_with_sampling(input_path: str, max_pages: int = 50, size_limit_mb: float = 32.0) -> str:
    """
    Smartly truncates a PDF for upload with dual constraints:
      - Page limit: max_pages (default 50)
      - Size limit: size_limit_mb (default 32 MB)

    Strategy:
      1. Always keeps first 15 pages
      2. Randomly samples 10-page windows from remaining pages to reach max_pages
      3. If still over size limit, progressively reduces quality/pages until under limit

    Returns: Path to truncated PDF
    Raises: Exception if PDF processing fails
    """
    truncated_path = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf").name

    try:
        with fitz.open(input_path) as doc:
        total_pages = doc.page_count
        file_size_mb = os.path.getsize(input_path) / (1024 * 1024)

        # If document already meets both constraints, return as-is
        if total_pages <= max_pages and file_size_mb <= size_limit_mb:
            return input_path

        new_doc = fitz.open()

        # Step 1: Always include first 15 pages (or all if fewer)
        base_pages = min(total_pages, 15)
        for i in range(base_pages):
            new_doc.insert_pdf(doc, from_page=i, to_page=i)

        # Step 2: Sample additional pages if needed
        if total_pages > base_pages and max_pages > base_pages:
            remaining_pages = list(range(base_pages, total_pages))
            available_slots = max_pages - base_pages
            
            sampled_ranges = []
            
            while available_slots > 0 and remaining_pages:
                # Pick a random starting page from remaining pool
                start = random.choice(remaining_pages)
                # Create a 10-page window (or less if near end)
                end = min(start + 9, total_pages - 1)
                
                sampled_ranges.append((start, end))
                
                # Remove sampled pages from pool to avoid duplicates
                remaining_pages = [p for p in remaining_pages if p < start or p > end]
                
                # Update available slots
                pages_added = end - start + 1
                available_slots -= pages_added
                
                if available_slots <= 0:
                    break
            
            # Sort ranges by start page for cleaner output
            sampled_ranges.sort()
            
            # Insert all sampled windows
            for start, end in sampled_ranges:
                new_doc.insert_pdf(doc, from_page=start, to_page=end)

        # Save initial truncated version
        new_doc.save(truncated_path)
        new_doc.close()

        # Step 3: Handle size constraint - progressively reduce pages if still too large
        iteration = 0
        max_iterations = 10
        
        while os.path.getsize(truncated_path) / (1024 * 1024) > size_limit_mb and iteration < max_iterations:
            with fitz.open(truncated_path) as temp_doc:
                current_pages = temp_doc.page_count
                
                # If we're down to minimum pages, can't reduce further
                if current_pages <= 15:
                    print(f"Warning: Cannot reduce below 15 pages. Final size: {os.path.getsize(truncated_path) / (1024 * 1024):.2f} MB")
                    break
                
                # Remove last 5 pages (or fewer if close to minimum)
                pages_to_remove = min(5, current_pages - 15)
                
                # Create new doc without last pages
                reduced_doc = fitz.open()
                for i in range(current_pages - pages_to_remove):
                    reduced_doc.insert_pdf(temp_doc, from_page=i, to_page=i)
                
                reduced_doc.save(truncated_path)
                reduced_doc.close()
            
            iteration += 1

        final_size_mb = os.path.getsize(truncated_path) / (1024 * 1024)
        with fitz.open(truncated_path) as final_doc:
            final_pages = final_doc.page_count
        print(f"PDF truncated: {total_pages} pages -> {final_pages} pages, {file_size_mb:.2f} MB -> {final_size_mb:.2f} MB")

        return truncated_path

    except Exception as e:
        # Clean up truncated file if it was created
        try:
            if os.path.exists(truncated_path):
                os.remove(truncated_path)
        except:
            pass
        raise Exception(f"PDF truncation failed: {str(e)}")


