import base64
import tempfile
import fitz  # PyMuPDF
import json
import aiofiles
from datetime import datetime
from pathlib import Path
from azure.storage.blob.aio import BlobClient
from core.logger import logger
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

BLOB_STORAGE_CONNECTION_STRING = os.getenv("BLOB_STORAGE_CONNECTION_STRING")
BLOB_STORAGE_CONTAINER_NAME = os.getenv("BLOB_STORAGE_CONTAINER_NAME")


def pdf_to_base64_images(pdf_path: str, zoom: float = 2.0, dpi: int = 200) -> list[dict]:
    """
    Convert PDF pages to base64-encoded PNG images.

    Args:
        pdf_path: Path to the PDF file
        zoom: Zoom factor for image quality (default 2.0)
        dpi: DPI for image generation (default 200)

    Returns:
        List of dicts with 'page_number' and 'image_base64' keys (page_number starts from 1)
    """
    images = []

    try:
        with fitz.open(pdf_path) as doc:
            for page_index in range(doc.page_count):
                page = doc[page_index]

                # Create transformation matrix for desired DPI
                mat = fitz.Matrix(zoom, zoom)

                # Render page to pixmap
                pix = page.get_pixmap(matrix=mat)

                # Convert to PNG bytes
                png_data = pix.tobytes("png")

                # Encode to base64
                base64_str = base64.b64encode(png_data).decode('utf-8')

                # Page number starts from 1, not 0
                page_number = page_index + 1

                images.append({
                    "page_number": page_number,
                    "image_base64": base64_str
                })

                logger.debug(f"Generated image for page {page_number}")

    except Exception as e:
        logger.error(f"Failed to convert PDF to images: {e}")
        raise

    return images


async def upload_images_to_storage(images: list[dict], object_storage_path: str) -> list[str]:
    """
    Upload generated images to Azure Blob Storage in an 'images' folder at the same level as PDF.

    Args:
        images: List of image dicts from pdf_to_base64_images
        object_storage_path: Full path to the PDF file

    Returns:
        List of uploaded blob paths
    """
    uploaded_paths = []

    try:
        # Upload image metadata first
        await upload_image_metadata(len(images), object_storage_path)

        # Get the directory containing the PDF file
        pdf_dir = "/".join(object_storage_path.split("/")[:-1])

        for image_data in images:
            page_num = image_data["page_number"]
            image_base64 = image_data["image_base64"]

            # Create blob path for this image - simple naming: 1.png, 2.png, etc.
            image_blob_path = f"{pdf_dir}/images/{page_num}.png"

            # Decode base64 to bytes
            image_bytes = base64.b64decode(image_base64)

            # Upload to blob storage
            blob_client = BlobClient.from_connection_string(
                BLOB_STORAGE_CONNECTION_STRING,
                container_name=BLOB_STORAGE_CONTAINER_NAME,
                blob_name=image_blob_path,
            )

            try:
                await blob_client.upload_blob(image_bytes, overwrite=True)
                uploaded_paths.append(image_blob_path)
                logger.debug(f"Uploaded image for page {page_num} to {image_blob_path}")
            finally:
                await blob_client.close()

        # Update metadata status to completed
        await update_image_metadata_status(object_storage_path, "completed")

    except Exception as e:
        logger.error(f"Failed to upload images to storage: {e}")
        # Update metadata status to failed
        try:
            await update_image_metadata_status(object_storage_path, "failed")
        except:
            pass
        raise

    return uploaded_paths


async def download_images_from_storage(object_storage_path: str, temp_dir: str) -> list[dict]:
    """
    Download images from Azure Blob Storage 'images' folder to local temp directory.

    Args:
        object_storage_path: Full path to the PDF file
        temp_dir: Local temporary directory path

    Returns:
        List of dicts with 'page_number' and 'image_base64' keys
    """
    images = []

    try:
        # List all blobs in the images folder
        from azure.storage.blob.aio import ContainerClient

        container_client = ContainerClient.from_connection_string(
            BLOB_STORAGE_CONNECTION_STRING,
            container_name=BLOB_STORAGE_CONTAINER_NAME
        )

        try:
            # Get the directory containing the PDF file
            pdf_dir = "/".join(object_storage_path.split("/")[:-1])
            images_prefix = f"{pdf_dir}/images/"

            async for blob in container_client.list_blobs(name_starts_with=images_prefix):
                if blob.name.endswith('.png'):
                    # Extract page number from filename (simple naming: 1.png, 2.png, etc.)
                    filename = Path(blob.name).name
                    if filename.replace('.png', '').isdigit():
                        try:
                            page_num = int(filename.replace('.png', ''))
                        except ValueError:
                            continue

                        # Download blob
                        blob_client = container_client.get_blob_client(blob.name)
                        try:
                            stream = await blob_client.download_blob()
                            image_bytes = await stream.readall()

                            # Convert to base64
                            base64_str = base64.b64encode(image_bytes).decode('utf-8')

                            images.append({
                                "page_number": page_num,
                                "image_base64": base64_str
                            })

                            logger.debug(f"Downloaded image for page {page_num}")

                        finally:
                            await blob_client.close()

        finally:
            await container_client.close()

        # Sort by page number
        images.sort(key=lambda x: x["page_number"])

    except Exception as e:
        logger.error(f"Failed to download images from storage: {e}")
        raise

    return images


async def check_images_exist(object_storage_path: str) -> bool:
    """
    Check if images exist and are complete based on metadata validation.

    Args:
        object_storage_path: Full path to the PDF file

    Returns:
        True if all expected images exist, False otherwise
    """
    try:
        from azure.storage.blob.aio import ContainerClient

        container_client = ContainerClient.from_connection_string(
            BLOB_STORAGE_CONNECTION_STRING,
            container_name=BLOB_STORAGE_CONTAINER_NAME
        )

        try:
            # Get the directory containing the PDF file
            pdf_dir = "/".join(object_storage_path.split("/")[:-1])

            # Check metadata first
            metadata_blob_path = f"{pdf_dir}/image_metadata.json"
            blob_client = container_client.get_blob_client(metadata_blob_path)

            if not await blob_client.exists():
                return False

            # Download metadata
            stream = await blob_client.download_blob()
            json_bytes = await stream.readall()
            metadata = json.loads(json_bytes.decode('utf-8'))
            await blob_client.close()

            # Check if upload was completed
            if metadata.get('status') != 'completed':
                return False

            expected_pages = metadata.get('total_pages', 0)
            if expected_pages <= 0:
                return False

            # Count actual images
            images_prefix = f"{pdf_dir}/images/"
            actual_images = 0

            async for blob in container_client.list_blobs(name_starts_with=images_prefix):
                if blob.name.endswith('.png'):
                    filename = Path(blob.name).name
                    if filename.replace('.png', '').isdigit():
                        actual_images += 1

            # Validate count matches expected
            images_complete = actual_images == expected_pages
            logger.debug(f"Images check: expected={expected_pages}, actual={actual_images}, complete={images_complete}")

            return images_complete

        finally:
            await container_client.close()

    except Exception as e:
        logger.warning(f"Failed to check if images exist: {e}")
        return False


async def upload_json_result(result_data: dict, object_storage_path: str, session_id: str) -> str:
    """
    Upload marking results JSON to Azure Blob Storage in mark folder.

    Args:
        result_data: Dictionary containing the marking results
        object_storage_path: Full path to the PDF file
        session_id: Session ID for this mark process

    Returns:
        Uploaded blob path
    """
    try:
        # Get the directory containing the PDF file
        pdf_dir = "/".join(object_storage_path.split("/")[:-1])

        # Create blob path for results JSON in mark folder
        json_blob_path = f"{pdf_dir}/mark/mark_{session_id}.json"

        # Add session metadata to results
        result_data["session_metadata"] = {
            "session_id": session_id,
            "created_at": datetime.now().isoformat()
        }

        # Convert to JSON string
        json_str = json.dumps(result_data, indent=2, ensure_ascii=False)
        json_bytes = json_str.encode('utf-8')

        # Upload to blob storage
        blob_client = BlobClient.from_connection_string(
            BLOB_STORAGE_CONNECTION_STRING,
            container_name=BLOB_STORAGE_CONTAINER_NAME,
            blob_name=json_blob_path,
        )

        try:
            await blob_client.upload_blob(json_bytes, overwrite=True)
            logger.info(f"Uploaded marking results to {json_blob_path}")
            return json_blob_path
        finally:
            await blob_client.close()

    except Exception as e:
        logger.error(f"Failed to upload JSON result: {e}")
        raise


async def find_latest_mark_result(object_storage_path: str) -> tuple[dict | None, str | None]:
    """
    Find the latest mark result and return it with session ID.

    Args:
        object_storage_path: Full path to the PDF file

    Returns:
        Tuple of (existing results dict if found, session_id)
    """
    try:
        from azure.storage.blob.aio import ContainerClient

        container_client = ContainerClient.from_connection_string(
            BLOB_STORAGE_CONNECTION_STRING,
            container_name=BLOB_STORAGE_CONTAINER_NAME
        )

        try:
            pdf_dir = "/".join(object_storage_path.split("/")[:-1])
            mark_prefix = f"{pdf_dir}/mark/"

            latest_result = None
            latest_session_id = None
            latest_timestamp = None

            async for blob in container_client.list_blobs(name_starts_with=mark_prefix):
                if blob.name.endswith('.json') and '/mark_' in blob.name:
                    # Extract session ID from filename: mark_{session_id}.json
                    filename = Path(blob.name).name
                    if filename.startswith('mark_') and filename.endswith('.json'):
                        session_id = filename[5:-5]  # Remove 'mark_' prefix and '.json' suffix

                        # Download and check result
                        blob_client = container_client.get_blob_client(blob.name)
                        try:
                            stream = await blob_client.download_blob()
                            json_bytes = await stream.readall()
                            result = json.loads(json_bytes.decode('utf-8'))

                            # Get timestamp from session metadata or file metadata
                            created_at = None
                            if 'session_metadata' in result:
                                created_at = result['session_metadata'].get('created_at')
                            if not created_at and 'metadata' in result:
                                created_at = result['metadata'].get('processedAt')

                            if created_at and (latest_timestamp is None or created_at > latest_timestamp):
                                latest_timestamp = created_at
                                latest_result = result
                                latest_session_id = session_id
                        finally:
                            await blob_client.close()

        finally:
            await container_client.close()

        return latest_result, latest_session_id

    except Exception as e:
        logger.warning(f"Failed to find latest mark result: {e}")
        return None, None


async def check_existing_results(object_storage_path: str, session_id: str) -> dict | None:
    """
    Check if marking results JSON already exists and return it.

    Args:
        object_storage_path: Full path to the PDF file
        session_id: Session ID to check for existing results

    Returns:
        Existing results dict if found, None otherwise
    """
    try:
        # Check specific session
        pdf_dir = "/".join(object_storage_path.split("/")[:-1])
        json_blob_path = f"{pdf_dir}/mark/mark_{session_id}.json"

        blob_client = BlobClient.from_connection_string(
            BLOB_STORAGE_CONNECTION_STRING,
            container_name=BLOB_STORAGE_CONTAINER_NAME,
            blob_name=json_blob_path,
        )

        try:
            if await blob_client.exists():
                stream = await blob_client.download_blob()
                json_bytes = await stream.readall()
                json_str = json_bytes.decode('utf-8')
                return json.loads(json_str)
        except Exception as e:
            logger.warning(f"Failed to read existing results: {e}")
            return None
        finally:
            await blob_client.close()

        return None

    except Exception as e:
        logger.warning(f"Failed to check existing results: {e}")
        return None





async def upload_image_metadata(total_pages: int, object_storage_path: str) -> str:
    """
    Upload image metadata JSON to track expected number of images.

    Args:
        total_pages: Total number of pages/images expected
        object_storage_path: Full path to the PDF file

    Returns:
        Uploaded blob path for metadata
    """
    try:
        # Get the directory containing the PDF file
        pdf_dir = "/".join(object_storage_path.split("/")[:-1])
        metadata_blob_path = f"{pdf_dir}/image_metadata.json"

        metadata = {
            "total_pages": total_pages,
            "created_at": datetime.now().isoformat(),
            "status": "uploading"
        }

        # Convert to JSON string
        json_str = json.dumps(metadata, indent=2, ensure_ascii=False)
        json_bytes = json_str.encode('utf-8')

        # Upload to blob storage
        blob_client = BlobClient.from_connection_string(
            BLOB_STORAGE_CONNECTION_STRING,
            container_name=BLOB_STORAGE_CONTAINER_NAME,
            blob_name=metadata_blob_path,
        )

        try:
            await blob_client.upload_blob(json_bytes, overwrite=True)
            logger.info(f"Uploaded image metadata to {metadata_blob_path}")
            return metadata_blob_path
        finally:
            await blob_client.close()

    except Exception as e:
        logger.error(f"Failed to upload image metadata: {e}")
        raise


async def update_image_metadata_status(object_storage_path: str, status: str) -> None:
    """
    Update the status in image metadata.

    Args:
        object_storage_path: Full path to the PDF file
        status: New status (e.g., 'completed', 'failed')
    """
    try:
        pdf_dir = "/".join(object_storage_path.split("/")[:-1])
        metadata_blob_path = f"{pdf_dir}/image_metadata.json"

        blob_client = BlobClient.from_connection_string(
            BLOB_STORAGE_CONNECTION_STRING,
            container_name=BLOB_STORAGE_CONTAINER_NAME,
            blob_name=metadata_blob_path,
        )

        try:
            # Download existing metadata
            if await blob_client.exists():
                stream = await blob_client.download_blob()
                json_bytes = await stream.readall()
                metadata = json.loads(json_bytes.decode('utf-8'))

                # Update status
                metadata["status"] = status
                metadata["updated_at"] = datetime.now().isoformat()

                # Upload updated metadata
                json_str = json.dumps(metadata, indent=2, ensure_ascii=False)
                json_bytes = json_str.encode('utf-8')
                await blob_client.upload_blob(json_bytes, overwrite=True)

                logger.debug(f"Updated image metadata status to {status}")
        finally:
            await blob_client.close()

    except Exception as e:
        logger.warning(f"Failed to update image metadata status: {e}")
