# Docker Setup for Document AI FastAPI Application

This document provides instructions for running the Document AI FastAPI application using Docker.

## Files Overview

- `Dockerfile` - Multi-stage Docker build for optimized production images
- `docker-compose.yml` - Development environment setup
- `docker-compose.prod.yml` - Production environment setup
- `nginx.conf` - Nginx reverse proxy configuration
- `.dockerignore` - Files to exclude from Docker build context

## Quick Start

### Development

1. **Build and run with docker-compose:**
   ```bash
   docker-compose up --build
   ```

2. **Run in detached mode:**
   ```bash
   docker-compose up -d --build
   ```

3. **View logs:**
   ```bash
   docker-compose logs -f document-ai
   ```

4. **Stop services:**
   ```bash
   docker-compose down
   ```

### Production

1. **Create environment file:**
   ```bash
   cp .env.example .env
   # Edit .env with your production values
   ```

2. **Build and run production setup:**
   ```bash
   docker-compose -f docker-compose.prod.yml up --build -d
   ```

## Docker Commands

### Building the Image

```bash
# Build development image
docker build -t document-ai:dev .

# Build production image
docker build -t document-ai:prod --target production .
```

### Running the Container

```bash
# Run development container
docker run -p 8000:8000 document-ai:dev

# Run production container with environment variables
docker run -p 8000:8000 \
  -e OPENAI_API_KEY=your_key \
  -e MISTRAL_API_KEY=your_key \
  document-ai:prod
```

## Environment Variables

Create a `.env` file in the same directory with the following variables:

```env
# API Keys
OPENAI_API_KEY=your_openai_api_key
MISTRAL_API_KEY=your_mistral_api_key

# Azure Storage
AZURE_STORAGE_CONNECTION_STRING=your_connection_string
AZURE_QUEUE_NAME=your_queue_name

# Application Settings
PYTHONPATH=/app
```

## Health Checks

The application includes health checks accessible at:
- `http://localhost:8000/health`

## Nginx Reverse Proxy

The production setup includes an optional Nginx reverse proxy with:
- Rate limiting (10 requests/second)
- Security headers
- Load balancing
- Request/response logging

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Check what's using port 8000
   lsof -i :8000
   # Kill the process or use a different port
   ```

2. **Permission denied errors:**
   ```bash
   # Ensure proper file permissions
   chmod +x your_script.py
   ```

3. **Build failures:**
   ```bash
   # Clean Docker cache
   docker system prune -a
   # Rebuild without cache
   docker-compose build --no-cache
   ```

### Viewing Logs

```bash
# Docker compose logs
docker-compose logs -f

# Container logs
docker logs <container_id>

# Follow logs in real-time
docker logs -f <container_id>
```

## Performance Optimization

The Dockerfile includes several optimizations:
- Multi-stage build to reduce image size
- Non-root user for security
- Proper Python environment variables
- Minimal runtime dependencies
- Health checks for container orchestration

## Security Considerations

- Application runs as non-root user
- Minimal base image (python:3.11-slim)
- Security headers in Nginx configuration
- Rate limiting enabled
- No sensitive data in Docker images
