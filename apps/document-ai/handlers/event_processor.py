from pathlib import Path
from core.logger import logger
from handlers.classify_handler import handle_classify_event
from handlers.mark_handler import handle_mark_event
from handlers.queue_handler import queue_classification_event, queue_marking_event, queue_extraction_event
from core.constants import STATUS, ERROR_CODES
from core.storage import store_file_locally
from models.events import EventType


def get_queue_function(event_type: str):
    """Get the appropriate queue function based on event type."""
    queue_functions = {
        "classify": queue_classification_event,
        "mark": queue_marking_event,
        "extract": queue_extraction_event,
    }
    
    return queue_functions.get(event_type)  # Default to classification


# --- Event handler registry ---
EVENT_HANDLERS = {
    "classify": handle_classify_event,
    "mark": handle_mark_event,
    # "extract": handle_extract_event,
}


# --- MAIN EVENT PROCESSOR ---
async def process_event(data, event_type: EventType | None = None):
    """
    Generic event processor:
    1. Validates event type
    
    2. Invokes the appropriate handler
    3. Cleans up temporary files
    """

    file_id = data.payload.fileId
    event_type = event_type

    logger.info(f"Received event type '{event_type}'")

    handler = EVENT_HANDLERS.get(event_type)
    if handler is None:
        error_message = f"Invalid event type '{event_type}'"
        logger.error(error_message)
        queue_func = get_queue_function(event_type)
        await queue_func(
            file_id=file_id,
            status=STATUS.FAILED,
            error_code=ERROR_CODES.INVALID_EVENT,
            error_reason=error_message,
        )
        return

    local_file_path = None

    try:
       

        # --- STEP 1: Run handler ---
        result = await handler(data)
        logger.info(f"Handler '{event_type}' completed successfully")

        return result

    except Exception as exc:
        logger.exception(f"Error processing event '{event_type}': {exc}")
        queue_func = get_queue_function(event_type)
        await queue_func(
            file_id=file_id,
            status=STATUS.FAILED,
            error_code=ERROR_CODES.UNKNOWN_ERROR,
            error_reason=str(exc),
        )

    finally:
        await _cleanup_temp(local_file_path, file_id)


# --- INTERNAL CLEANUP UTILITY ---
async def _cleanup_temp(local_file_path: str | None, file_id: str = None):
    """Safely remove temporary local files."""
    try:
        if local_file_path:
            path = Path(local_file_path)
            if path.exists():
                path.unlink()
                logger.debug(f"Temp file '{local_file_path}' deleted")
    except Exception as cleanup_error:
        logger.warning(f"Cleanup failed: {cleanup_error}")
