import json
import arrow
from core.logger import logger
from core.constants import STATUS, ERROR_CODES
from core.context import get_request_id, get_context_dict
import core.config as config


async def queue_classification_event(
    file_id: str,
    status: STATUS,
    data: dict | None = None,
    error_code: ERROR_CODES | None = None,
    error_reason: str = "",
):
    """
    Pushes a classification event message to the classification queue.
    """
    return await _queue_event("classification", file_id, status, data, error_code, error_reason)


async def queue_marking_event(
    file_id: str,
    status: STATUS,
    data: dict | None = None,
    error_code: ERROR_CODES | None = None,
    error_reason: str = "",
):
    """
    Pushes a marking event message to the marking queue.
    """
    return await _queue_event("marking", file_id, status, data, error_code, error_reason)


async def queue_extraction_event(
    file_id: str,
    status: STATUS,
    data: dict | None = None,
    error_code: ERROR_CODES | None = None,
    error_reason: str = "",
):
    """
    Pushes an extraction event message to the extraction queue.
    """
    return await _queue_event("extraction", file_id, status, data, error_code, error_reason)


async def _queue_event(
    operation_type: str,
    file_id: str,
    status: STATUS,
    data: dict | None = None,
    error_code: ERROR_CODES | None = None,
    error_reason: str = "",
):
    """
    Internal function to push an event message to the appropriate Azure Storage Queue.
    """
    try:
        queue_client = config.get_queue_client(operation_type)
    except (RuntimeError, ValueError) as e:
        logger.error(f"Queue client error for {operation_type}: {e}")
        raise

    # Get context information
    event_id = get_request_id()
    context = get_context_dict()

    queue_payload = {
        "status": status.value,
        "eventId": event_id,
        "fileId": file_id,
        "timestamp": arrow.now().isoformat(),
        "data": data or {},
        "context": context,
        "errors": {
            "code": error_code.value if error_code else None,
            "reason": error_reason or "",
        },
    }

    try:
        message_str = json.dumps(queue_payload, default=str)
        await queue_client.send_message(message_str)

        logger.info(f"✅ {operation_type.title()} event sent to queue ({status.value})")

    except Exception:
        logger.exception(f"❌ Failed to send {operation_type} event to queue ({status.value})")
        raise

    return {"status": "success", "status": status.value, "operation": operation_type}
