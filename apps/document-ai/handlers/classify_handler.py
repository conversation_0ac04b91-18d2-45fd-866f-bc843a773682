import base64
from io import Bytes<PERSON>
from datetime import datetime
import PyPDF2
from agents.classification_agent import handler as classification_agent
from models.events import EventQueueRequest
from handlers.queue_handler import queue_classification_event
from core.logger import logger
from core.constants import STATUS, ERROR_CODES
from core.storage import store_file_locally


async def handle_classify_event(data: EventQueueRequest):
    """
    Unified classification handler:
    1. Sends IN_PROGRESS event
    2. Runs classification via LLM agent
    3. Adds metadata + standardizes response
    4. Sends COMPLETED or FAILED event
    """
    file_id = data.payload.fileId
    object_storage_path = data.payload.objectStoragePath
    file_name = object_storage_path.split("/")[-1]

    try:
        # --- STEP 1: Download & save locally ---
        logger.info("Fetching blob and storing temporarily...")
        local_file_path = await store_file_locally(object_storage_path)
        logger.info(f"File saved locally at {local_file_path}")

        logger.info(f"Starting classification for {file_name}")

        # --- STEP 2: Read and encode file ---
        with open(local_file_path, "rb") as file:
            file_content = file.read()
        file_base64 = base64.b64encode(file_content).decode("utf-8")

        # --- STEP 3: Call classification agent ---
        event = {"file_base64": file_base64, "filename": file_name}
        result = await classification_agent(event)

        # --- STEP 4: Compute metadata ---
        total_pages = get_pdf_total_pages(file_content)
        metadata = {
            "processedAt": datetime.now().isoformat(),
            "totalPages": total_pages,
        }

        # --- STEP 5: Build unified result ---
        response_data = format_classification_response(result, metadata)

        logger.info("Classification completed successfully")

        # --- STEP 6: Send COMPLETED event ---
        await queue_classification_event(
            file_id=file_id,
            status=STATUS.SUCCESS,
            data=response_data,
        )

        return response_data

    except Exception as e:
        logger.exception(f"Classification failed: {e}")

        # --- STEP 7: Send FAILED event ---
        await queue_classification_event(
            file_id=file_id,
            status=STATUS.FAILED,
            error_code=ERROR_CODES.CLASSIFICATION_ERROR,
            error_reason=ERROR_CODES.CLASSIFICATION_ERROR,
        )
        raise


# === Utility Functions === #

def get_pdf_total_pages(pdf_content: bytes) -> int:
    """Extract the total number of pages from a PDF file."""
    try:
        pdf_reader = PyPDF2.PdfReader(BytesIO(pdf_content))
        return len(pdf_reader.pages)
    except Exception as e:
        logger.warning(f"Page count extraction failed: {e}")
        return 0


def format_classification_response(raw_result: dict, metadata: dict) -> dict:
    """
    Normalizes LLM/agent output into a unified classification system schema.
    """
    return {
        "main_machinery": raw_result.get("main_machinery"),
        "classification": raw_result.get("classification"),
        "reasoning": raw_result.get("reasoning"),
        "metadata": metadata,
    }
