import base64
from io import Bytes<PERSON>
from datetime import datetime
import PyPDF2
from agents.classification_agent import handler as classification_agent
from models.events import EventQueueRequest
from handlers.queue_handler import queue_classification_event
from core.logger import logger
from core.constants import STATUS, ERROR_CODES
from core.storage import store_file_locally


async def handle_classify_event(data: EventQueueRequest):
    """
    Unified classification handler:
    1. Sends IN_PROGRESS event
    2. Runs classification via LLM agent
    3. Adds metadata + standardizes response
    4. Sends COMPLETED or FAILED event
    """
    file_id = data.payload.fileId
    object_storage_path = data.payload.objectStoragePath
    file_name = object_storage_path.split("/")[-1]

    try:
        # --- STEP 1: Download & save locally ---
        logger.info("Fetching blob and storing temporarily...")
        try:
            local_file_path = await store_file_locally(object_storage_path)
            logger.info(f"File saved locally at {local_file_path}")
        except Exception as e:
            logger.exception(f"Failed to download file: {e}")
            await queue_classification_event(
                file_id=file_id,
                status=STATUS.FAILED,
                error_code=ERROR_CODES.FILE_DOWNLOAD_ERROR,
                error_reason=f"Failed to download file from storage: {str(e)}",
            )
            raise

        logger.info(f"Starting classification for {file_name}")

        # --- STEP 2: Read and encode file ---
        try:
            with open(local_file_path, "rb") as file:
                file_content = file.read()
            file_base64 = base64.b64encode(file_content).decode("utf-8")
        except Exception as e:
            logger.exception(f"Failed to read/encode file: {e}")
            await queue_classification_event(
                file_id=file_id,
                status=STATUS.FAILED,
                error_code=ERROR_CODES.PDF_ENCODING_ERROR,
                error_reason=f"Failed to read or encode PDF file: {str(e)}",
            )
            raise

        # --- STEP 3: Call classification agent ---
        event = {"file_base64": file_base64, "filename": file_name}
        result = await classification_agent(event)

        # Check if agent returned an error
        if "error" in result:
            error_code = ERROR_CODES.CLASSIFICATION_ERROR
            retry_after = None

            # Handle specific error types from agent
            agent_error_code = result.get("error_code")
            if agent_error_code == "RATE_LIMIT_ERROR":
                error_code = ERROR_CODES.OPENAI_RATE_LIMIT_ERROR
                retry_after = result.get("retryAfter")
            elif agent_error_code == "PDF_TRUNCATION_ERROR":
                error_code = ERROR_CODES.PDF_TRUNCATION_ERROR
            elif agent_error_code == "PDF_ENCODING_ERROR":
                error_code = ERROR_CODES.PDF_ENCODING_ERROR
            elif agent_error_code == "OPENAI_API_ERROR":
                error_code = ERROR_CODES.OPENAI_API_ERROR
            elif agent_error_code == "OPENAI_VALIDATION_ERROR":
                error_code = ERROR_CODES.OPENAI_VALIDATION_ERROR

            await queue_classification_event(
                file_id=file_id,
                status=STATUS.FAILED,
                error_code=error_code,
                error_reason=result.get("error", "Classification agent error"),
                retry_after=retry_after,
            )
            raise Exception(f"Classification agent error: {result.get('error')}")

        # --- STEP 4: Compute metadata ---
        try:
            total_pages = get_pdf_total_pages(file_content)
            metadata = {
                "processedAt": datetime.now().isoformat(),
                "totalPages": total_pages,
            }
        except Exception as e:
            logger.warning(f"Failed to extract metadata: {e}")
            metadata = {
                "processedAt": datetime.now().isoformat(),
                "totalPages": 0,
            }

        # --- STEP 5: Build unified result ---
        try:
            response_data = format_classification_response(result, metadata)
        except Exception as e:
            logger.exception(f"Failed to format classification response: {e}")
            await queue_classification_event(
                file_id=file_id,
                status=STATUS.FAILED,
                error_code=ERROR_CODES.CLASSIFICATION_VALIDATION_ERROR,
                error_reason=f"Failed to format classification response: {str(e)}",
            )
            raise

        logger.info("Classification completed successfully")

        # --- STEP 6: Send COMPLETED event ---
        await queue_classification_event(
            file_id=file_id,
            status=STATUS.SUCCESS,
            data=response_data,
        )

        return response_data

    except Exception as e:
        # Only send error if we haven't already sent one above
        if "Classification agent error" not in str(e) and "Failed to download file" not in str(e) and "Failed to read or encode" not in str(e) and "Failed to format classification" not in str(e):
            logger.exception(f"Unexpected classification error: {e}")
            await queue_classification_event(
                file_id=file_id,
                status=STATUS.FAILED,
                error_code=ERROR_CODES.CLASSIFICATION_ERROR,
                error_reason=str(e),
            )
        raise


# === Utility Functions === #

def get_pdf_total_pages(pdf_content: bytes) -> int:
    """Extract the total number of pages from a PDF file."""
    try:
        pdf_reader = PyPDF2.PdfReader(BytesIO(pdf_content))
        return len(pdf_reader.pages)
    except Exception as e:
        logger.warning(f"Page count extraction failed: {e}")
        return 0


def format_classification_response(raw_result: dict, metadata: dict) -> dict:
    """
    Normalizes LLM/agent output into a unified classification system schema.
    """
    return {
        "main_machinery": raw_result.get("main_machinery"),
        "classification": raw_result.get("classification"),
        "reasoning": raw_result.get("reasoning"),
        "metadata": metadata,
    }
