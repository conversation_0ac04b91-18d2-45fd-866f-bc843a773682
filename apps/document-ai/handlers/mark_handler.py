import asyncio
import tempfile
from pathlib import Path
from datetime import datetime
from models.events import EventQueueRequest
from handlers.queue_handler import queue_marking_event
from core.logger import logger
from core.constants import STATUS, ERROR_CODES
from core.storage import store_file_locally
from utils.mark_util import (
    pdf_to_base64_images,
    upload_images_to_storage,
    download_images_from_storage,
    check_images_exist,
    upload_json_result,
    check_existing_results
)
from agents.mark_agent import handler as mark_agent


async def handle_mark_event(data: EventQueueRequest):
    """
    Unified marking handler:
    1. Sends IN_PROGRESS event
    2. Checks for existing images/results and resumes if needed
    3. Generates PDF images at 200 DPI if not exists
    4. Runs mark agent on each page
    5. Stores results in JSON format
    6. Sends COMPLETED or FAILED event
    """
    file_id = data.payload.fileId
    object_storage_path = data.payload.objectStoragePath
    file_name = object_storage_path.split("/")[-1]

    # Get session ID from payload (now mandatory)
    session_id = data.payload.sessionId

    try:
        logger.info(f"Starting marking process for {file_name} with session ID: {session_id}")

        # --- STEP 1: Check for existing results and determine resume point ---
        try:
            existing_results = await check_existing_results(object_storage_path, session_id)
            images_exist = await check_images_exist(object_storage_path)
        except Exception as e:
            logger.exception(f"Failed to check existing results/images: {e}")
            await queue_marking_event(
                file_id=file_id,
                status=STATUS.FAILED,
                error_code=ERROR_CODES.STORAGE_CONNECTION_ERROR,
                error_reason=f"Failed to check existing results or images: {str(e)}",
            )
            raise

        # Initialize results structure
        if existing_results:
            results = existing_results
            logger.info(f"Found existing results for session {session_id}, resuming")
        else:
            results = {"pages": {}}
            logger.info(f"Starting new mark process with session {session_id}")

        # --- STEP 3: Generate or download images ---
        temp_dir = tempfile.mkdtemp(suffix="pdf_images")

        if images_exist:
            logger.info("Images folder exists, downloading images")
            try:
                images = await download_images_from_storage(object_storage_path, temp_dir)
            except Exception as e:
                logger.exception(f"Failed to download images: {e}")
                await queue_marking_event(
                    file_id=file_id,
                    status=STATUS.FAILED,
                    error_code=ERROR_CODES.IMAGE_DOWNLOAD_ERROR,
                    error_reason=f"Failed to download images from storage: {str(e)}",
                )
                raise
        else:
            logger.info("Generating images from PDF at 200 DPI")

            # ---  Download & save locally ---
            try:
                logger.info("Fetching blob and storing temporarily...")
                local_file_path = await store_file_locally(object_storage_path)
                logger.info(f"File saved locally at {local_file_path}")
            except Exception as e:
                logger.exception(f"Failed to download PDF file: {e}")
                await queue_marking_event(
                    file_id=file_id,
                    status=STATUS.FAILED,
                    error_code=ERROR_CODES.FILE_DOWNLOAD_ERROR,
                    error_reason=f"Failed to download PDF file: {str(e)}",
                )
                raise

            # Use local file path if provided, otherwise download from storage
            pdf_path = local_file_path
            if not pdf_path or not Path(pdf_path).exists():
                try:
                    pdf_path = await store_file_locally(object_storage_path)
                except Exception as e:
                    logger.exception(f"Failed to re-download PDF file: {e}")
                    await queue_marking_event(
                        file_id=file_id,
                        status=STATUS.FAILED,
                        error_code=ERROR_CODES.FILE_DOWNLOAD_ERROR,
                        error_reason=f"Failed to re-download PDF file: {str(e)}",
                    )
                    raise

            # Generate images from PDF
            try:
                images = pdf_to_base64_images(pdf_path, zoom=2.5, dpi=200)
            except Exception as e:
                logger.exception(f"Failed to generate images from PDF: {e}")
                await queue_marking_event(
                    file_id=file_id,
                    status=STATUS.FAILED,
                    error_code=ERROR_CODES.IMAGE_GENERATION_ERROR,
                    error_reason=f"Failed to generate images from PDF: {str(e)}",
                )
                raise

            # Upload images to storage
            try:
                await upload_images_to_storage(images, object_storage_path)
                logger.info(f"Generated and uploaded {len(images)} images")
            except Exception as e:
                logger.exception(f"Failed to upload images: {e}")
                await queue_marking_event(
                    file_id=file_id,
                    status=STATUS.FAILED,
                    error_code=ERROR_CODES.IMAGE_UPLOAD_ERROR,
                    error_reason=f"Failed to upload images to storage: {str(e)}",
                )
                raise

        # --- STEP 4: Process pages with mark agent ---
        total_pages = len(images)
        processed_count = 0

        # Determine which pages need processing
        pages_to_process = []
        for image_data in images:
            page_num = image_data["page_number"]
            if str(page_num) not in results["pages"]:
                pages_to_process.append(image_data)

        if pages_to_process:
            logger.info(f"Processing {len(pages_to_process)} pages (resuming from existing results)")

            # Process pages with concurrency control and save partial results
            semaphore = asyncio.Semaphore(3)  # Limit concurrent processing

            async def process_page(image_data):
                async with semaphore:
                    page_num = image_data["page_number"]
                    try:
                        logger.debug(f"[{file_id}] Processing page {page_num}")

                        # Call mark agent
                        result = await mark_agent(image_data)

                        if "error" in result:
                            # Check if it's a rate limit error that should be propagated
                            agent_error_code = result.get("error_code")
                            if agent_error_code == "RATE_LIMIT_ERROR":
                                logger.warning(f"[{file_id}] Rate limit error for page {page_num}, propagating to queue")
                                await queue_marking_event(
                                    file_id=file_id,
                                    status=STATUS.FAILED,
                                    error_code=ERROR_CODES.OPENAI_RATE_LIMIT_ERROR,
                                    error_reason=result.get("error", "OpenAI rate limit exceeded"),
                                    retry_after=result.get("retryAfter"),
                                )
                                raise Exception(f"Rate limit error: {result.get('error')}")
                            elif agent_error_code in ["OPENAI_API_ERROR", "OPENAI_VALIDATION_ERROR"]:
                                # These are serious errors that should fail the entire job
                                logger.error(f"[{file_id}] Critical mark agent error for page {page_num}, failing job")
                                error_code = ERROR_CODES.OPENAI_API_ERROR if agent_error_code == "OPENAI_API_ERROR" else ERROR_CODES.OPENAI_VALIDATION_ERROR
                                await queue_marking_event(
                                    file_id=file_id,
                                    status=STATUS.FAILED,
                                    error_code=error_code,
                                    error_reason=result.get("error", "Mark agent critical error"),
                                )
                                raise Exception(f"Critical mark agent error: {result.get('error')}")
                            else:
                                logger.warning(f"[{file_id}] Mark agent error for page {page_num}: {result['error']}")
                                return page_num, None

                        # Parse result
                        import json
                        result_data = json.loads(result.get("result", "{}"))

                        # Format according to required schema
                        page_result = None
                        if result_data.get("has_spares") or result_data.get("has_schedule"):
                            types = []
                            if result_data.get("has_spares"):
                                types.append("spares")
                            if result_data.get("has_schedule"):
                                types.append("jobs")

                            page_result = {
                                "type": types,
                                "reasoning": result_data.get("reason", ""),
                                "confidence": result_data.get("confidence", 0.0)
                            }

                        return page_num, page_result

                    except Exception as e:
                        logger.error(f"[{file_id}] Failed to process page {page_num}: {e}")
                        return page_num, None

            # Process pages one by one and save partial results
            for image_data in pages_to_process:
                try:
                    page_num, page_result = await process_page(image_data)

                    # Update results immediately
                    results["pages"][str(page_num)] = page_result
                    processed_count += 1

                    # Save partial results every 5 pages or on error
                    if processed_count % 5 == 0:
                        try:
                            # Add temporary metadata for partial save
                            temp_results = results.copy()
                            temp_results["metadata"] = {
                                "fileName": file_name,
                                "filePath": object_storage_path,
                                "totalPages": total_pages,
                                "processedAt": datetime.now().isoformat(),
                                "status": "partial"
                            }
                            await upload_json_result(temp_results, object_storage_path, session_id)
                            logger.info(f"[{file_id}] Saved partial results ({processed_count}/{len(pages_to_process)} pages)")
                        except Exception as save_error:
                            logger.warning(f"[{file_id}] Failed to save partial results: {save_error}")

                    logger.info(f"[{file_id}] Processed page {page_num} ({processed_count}/{len(pages_to_process)})")

                except Exception as e:
                    logger.error(f"[{file_id}] Failed to process page {image_data['page_number']}: {e}")
                    # Save partial results on error
                    try:
                        temp_results = results.copy()
                        temp_results["metadata"] = {
                            "fileName": file_name,
                            "filePath": object_storage_path,
                            "totalPages": total_pages,
                            "processedAt": datetime.now().isoformat(),
                            "status": "partial_with_error",
                            "lastError": str(e)
                        }
                        await upload_json_result(temp_results, object_storage_path, session_id)
                        logger.info(f"[{file_id}] Saved partial results after error")
                    except Exception as save_error:
                        logger.warning(f"[{file_id}] Failed to save partial results after error: {save_error}")
                    continue

        else:
            logger.info(f"[{file_id}] All pages already processed")

        # --- STEP 5: Add final metadata and upload results ---
        results["metadata"] = {
            "fileName": file_name,
            "filePath": object_storage_path,
            "totalPages": total_pages,
            "processedAt": datetime.now().isoformat(),
            "status": "completed"
        }

        # Upload results JSON
        try:
            json_blob_path = await upload_json_result(results, object_storage_path, session_id)
        except Exception as e:
            logger.exception(f"Failed to upload final results: {e}")
            await queue_marking_event(
                file_id=file_id,
                status=STATUS.FAILED,
                error_code=ERROR_CODES.RESULT_STORAGE_ERROR,
                error_reason=f"Failed to upload final results: {str(e)}",
            )
            raise

        logger.info("Marking completed successfully")

        # --- STEP 6: Send COMPLETED event ---
        await queue_marking_event(
            file_id=file_id,
            status=STATUS.SUCCESS,
            data={
                "path": json_blob_path,
            },
        )

        return results

    except Exception as e:
        # Only send error if we haven't already sent one above
        error_message = str(e)
        if not any(phrase in error_message for phrase in [
            "Failed to check existing", "Failed to download images", "Failed to download PDF",
            "Failed to generate images", "Failed to upload images", "Rate limit error",
            "Failed to upload final results"
        ]):
            logger.exception(f"Unexpected marking error: {e}")
            await queue_marking_event(
                file_id=file_id,
                status=STATUS.FAILED,
                error_code=ERROR_CODES.MARKING_ERROR,
                error_reason=str(e),
            )
        raise

