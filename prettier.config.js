/** @type {import("prettier").Config} */
module.exports = {
  semi: true, // Add semicolons at the end of statements
  singleQuote: false, // Use double quotes
  trailingComma: "es5", // Trailing commas where valid in ES5 (objects, arrays, etc.)
  tabWidth: 2, // Indentation level = 2 spaces
  printWidth: 100, // Max line width before wrapping
  bracketSpacing: true, // { foo: bar } instead of {foo: bar}
  bracketSameLine: false, // Put > of JSX on its own line
  arrowParens: "always", // Always include parens around arrow fn args
  endOfLine: "lf", // Use LF for line endings
  plugins: [require.resolve("prettier-plugin-tailwindcss")], // Sort Tailwind classes
};
