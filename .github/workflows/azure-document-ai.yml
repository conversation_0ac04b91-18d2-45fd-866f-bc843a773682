name: <PERSON>uild and Push Document AI Docker Image

on:
  workflow_dispatch:

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write

    steps:
      # 1. Checkout repo
      - name: Checkout repository
        uses: actions/checkout@v3

      # 2. Log in to Azure Container Registry
      - name: Log in to Azure Container Registry
        uses: azure/docker-login@v1
        with:
          login-server: pmsdev.azurecr.io
          username: ${{ secrets.ACR_USERNAME }} # set in GitHub Secrets
          password: ${{ secrets.ACR_PASSWORD }} # set in GitHub Secrets

      # 3. Set up Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # 4. Cache Docker layers (optional but speeds up builds)
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      # 5. Build and push the image
      - name: Build and push Docker image
        run: |
          cd ./apps/document-ai && \
          docker buildx build \
            --platform linux/amd64 \
            -t pmsdev.azurecr.io/cadetlabs-document-ai:latest \
            -f ./Dockerfile \
            . \
            --push
