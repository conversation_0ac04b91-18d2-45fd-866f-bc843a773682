name: Azure Static Web Apps CI/CD

on:
  workflow_dispatch:

jobs:
  build_and_deploy_job:
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          lfs: false
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8.15.6
      - name: Install dependencies
        run: pnpm install
      - name: Build web app
        run: pnpm build --filter web
      - name: Build And Deploy
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_YELLOW_POND_0D818B400 }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for Github integrations (i.e. PR comments)
          action: "upload"
          ###### Repository/Build Configurations - These values can be configured to match your app requirements. ######
          # For more information regarding Static Web App workflow configurations, please visit: https://aka.ms/swaworkflowconfig
          app_location: "/apps/web" # App source code path
          api_location: "" # Api source code path - optional
          output_location: "" # Built app content directory - optional
          app_build_command: npm run build
          skip_api_build: true
          ###### End of Repository/Build Configurations ######
