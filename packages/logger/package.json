{"name": "@repo/logger", "version": "0.0.0", "type": "module", "private": true, "files": ["dist"], "main": "./dist/es/index.js", "module": "./dist/es/index.js", "types": "./dist/es/index.d.ts", "exports": {".": {"import": {"types": "./dist/es/index.d.ts", "default": "./dist/es/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}}, "scripts": {"build": "bunchee", "dev": "bunchee --watch", "lint": "eslint src/", "check-types": "tsc --noEmit", "test": "jest"}, "jest": {"preset": "@repo/jest-presets/node"}, "devDependencies": {"@jest/globals": "^29.7.0", "@repo/eslint-config": "*", "@repo/jest-presets": "*", "@repo/typescript-config": "*", "@types/node": "^22.15.3", "bunchee": "^6.4.0", "eslint": "^9.31.0", "jest": "^29.7.0", "typescript": "5.8.2"}}