const log = (...args: unknown[]): void => {
  // eslint-disable-next-line no-console -- logger
  console.log("LOGGER: ", ...args);
};

const logInfo = (...args: unknown[]): void => {
  // eslint-disable-next-line no-console -- logger
  console.info("LOGGER: ", ...args);
};

const logWarn = (...args: unknown[]): void => {
  // eslint-disable-next-line no-console -- logger
  console.warn("LOGGER: ", ...args);
};

const logError = (...args: unknown[]): void => {
  // eslint-disable-next-line no-console -- logger
  console.error("LOGGER: ", ...args);
};

export { log, logInfo, logWarn, logError };
