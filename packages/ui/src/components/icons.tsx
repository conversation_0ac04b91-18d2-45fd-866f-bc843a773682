import {
  Mail,
  Lock,
  Eye,
  EyeOff,
  ArrowRight,
  Loader2,
  <PERSON>tings,
  AlertCircle,
  CalendarDaysIcon,
  Bell,
  ChevronDown,
  Search,
  ArrowLeft,
  Download,
  Filter,
  MoreVertical,
  SortAsc,
  ArrowDownIcon,
  ArrowUpIcon,
  ChevronsUpDownIcon,
  UserRoundPen,
  History,
  Users,
  Headset,
  LogOut,
  Plus,
  Boxes,
  Workflow,
  ChartBarIncreasing,
  AlertTriangle,
  RefreshCw,
  ChevronUp,
  Home,
  Shield,
  ShieldX,
  FileQuestion,
  Building,
  X,
  Folder,
  Check,
  Edit,
  ChevronRight,
  Upload,
  type LucideIcon,
} from "lucide-react";

export type Icon = LucideIcon;

export const Icons = {
  mail: Mail,
  edit: Edit,
  lock: Lock,
  eye: Eye,
  eyeOff: EyeOff,
  arrowRight: ArrowRight,
  spinner: Loader2,
  settings: Settings,
  bell: Bell,
  calendar: CalendarDaysIcon,
  chevronDown: ChevronDown,
  search: Search,
  arrowLeft: ArrowLeft,
  download: Download,
  filter: Filter,
  moreVertical: MoreVertical,
  sortAsc: SortAsc,
  arrowDownIcon: ArrowDownIcon,
  arrowUpIcon: ArrowUpIcon,
  chevronUpDownIcon: ChevronsUpDownIcon,
  user: UserRoundPen,
  history: History,
  users: Users,
  headset: Headset,
  logout: LogOut,
  boxes: Boxes,
  workFlow: Workflow,
  ChartBarIncreasing,
  AlertTriangle,
  RefreshCw,
  ChevronUp,
  home: Home,
  shield: Shield,
  shieldX: ShieldX,
  plus: Plus,
  fileQuestion: FileQuestion,
  x: X,
  building: Building,
  folder: Folder,
  check: Check,
  ChevronRight,
  upload: Upload,
  alertCircle: AlertCircle,
} as const;

export type IconName = keyof typeof Icons;
