services:
  supertokens:
    image: supertokens/supertokens-postgresql
    container_name: supertokens
    restart: unless-stopped
    ports:
      - "3567:3567"
    environment:
      POSTGRESQL_USER: app_user_sk
      POSTGRESQL_PASSWORD: app_user_sk
      POSTGRESQL_HOST: cl-pab-dev-sindia-db-1.postgres.database.azure.com
      POSTGRESQL_PORT: 5432
      LOG_LEVEL: DEBUG
      POSTGRESQL_DATABASE_NAME: sk_dev
      POSTGRESQL_TABLE_SCHEMA: auth
