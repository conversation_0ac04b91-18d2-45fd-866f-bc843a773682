{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "start": {"dependsOn": ["^build"], "cache": false}, "dev": {"dependsOn": ["^build"], "cache": false, "persistent": true}}}