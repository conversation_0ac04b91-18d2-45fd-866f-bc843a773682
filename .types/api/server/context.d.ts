import { TenantContext } from "@/shared/model/tenant.context";
import { UserContext } from "@/shared/model/user.context";
export interface RequestContext {
    requestId: string;
    user?: UserContext;
    tenant?: TenantContext;
}
/**
 * Initializes a new request context and runs the provided callback within it.
 * This should only be called once per request, typically in the first middleware.
 */
export declare function runWithRequestContext<T>(requestId: string, callback: () => T): T;
export declare const RequestContextHelper: {
    getRequestId: () => string | undefined;
    getUser: () => Readonly<UserContext> | undefined;
    setUser: (user: UserContext) => void;
    getTenant: () => Readonly<TenantContext> | undefined;
    setTenant: (tenant: TenantContext) => void;
};
//# sourceMappingURL=context.d.ts.map