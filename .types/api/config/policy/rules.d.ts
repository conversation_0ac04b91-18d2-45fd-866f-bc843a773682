import { UserRole } from "@repo/prisma/client";
export declare enum Action {
    Create = "create",
    Read = "read",
    Update = "update",
    Delete = "delete"
}
export declare enum Resource {
    Tenant = "tenant",
    User = "user",
    Glossary = "glossary",
    GlossaryItem = "glossary-item",
    Project = "project"
}
export type ResourcePermissions = {
    [key in Resource]?: Action[];
};
export interface RoleDefinition {
    resources: ResourcePermissions;
}
export type RolesMap = {
    [key in UserRole]?: RoleDefinition;
};
export interface TenantRoleConfig {
    roles: RolesMap;
}
export interface PermissionConfig {
    default: TenantRoleConfig;
    [tenantId: string]: TenantRoleConfig;
}
export declare const policy_rules: PermissionConfig;
//# sourceMappingURL=rules.d.ts.map