import "reflect-metadata";
export declare enum API_ENVIRONMENT {
    DEVELOPMENT = "development",
    PRODUCTION = "production",
    TEST = "test",
    STAGING = "staging"
}
export declare class DatabaseConfig {
    user: string;
    password: string;
    host: string;
    port: number;
    database: string;
    schema: string;
}
export declare class EmailConfig {
    connectionString: string;
    senderEmail: string;
}
export declare class AuthConfig {
    websiteDomain: string;
    serverDomain: string;
    connectionURI: string;
}
export declare class ServerConfig {
    port: number;
    environment: API_ENVIRONMENT;
}
export declare class BlobConfig {
    connectionString: string;
    rootFolderName: string;
    maxFileSize?: number;
}
export declare class AppConfig {
    server: ServerConfig;
    auth: AuthConfig;
    database: DatabaseConfig;
    email: EmailConfig;
    blob: BlobConfig;
    constructor();
    private validate;
}
export declare const appConfig: AppConfig;
//# sourceMappingURL=index.d.ts.map