import { BaseResponse } from "@/shared/model/base.response";
import { GlossaryItemListResponse } from "@/features/glossary-item-management/model/glossary-item-list.response";
import { GlossaryItemListRequest } from "@/features/glossary-item-management/model/glossary-item-list.request";
import { IdParam } from "@/features/glossary-item-management/model/glossary-item-list.request";
export declare class GlossaryItemListController {
    process(params: IdParam, payload: GlossaryItemListRequest): Promise<BaseResponse<GlossaryItemListResponse>>;
    private buildWhereClause;
    private buildOrderByClause;
    private buildCursorClause;
    private generateCursor;
}
//# sourceMappingURL=glossary-item.list.d.ts.map