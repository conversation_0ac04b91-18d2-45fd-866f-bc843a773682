import { GlossaryStatus, GlossaryType } from "@repo/prisma/client";
export interface GlossaryItemListResponse {
    glossaryItems: GlossaryItemResponse[];
    glossaryName: string;
    glossaryId: string;
    nextCursor?: string;
    hasMore: boolean;
}
export interface GlossaryItemResponse {
    id: string;
    code: string;
    name: string;
    referenceCode: string;
    type: GlossaryType;
    status: GlossaryStatus;
    createdAt: Date;
    updatedAt: Date;
}
//# sourceMappingURL=glossary-item-list.response.d.ts.map