import { GlossaryStatus } from "@repo/prisma/client";
/**
 * Glossary Item-specific filters
 */
export declare class GlossaryItemFilters {
    name?: string;
    code?: string;
    referenceCode?: string;
    status?: GlossaryStatus[];
}
/**
 * Sorting configuration
 */
export declare class GlossaryItemSort {
    field?: string;
    direction?: "asc" | "desc";
}
/**
 * Glossary Item list request with pagination, filtering, and sorting
 */
export declare class GlossaryItemListRequest {
    filters?: GlossaryItemFilters;
    limit?: number;
    cursor?: string;
    sort?: GlossaryItemSort;
}
export declare class IdParam {
    id: string;
}
//# sourceMappingURL=glossary-item-list.request.d.ts.map