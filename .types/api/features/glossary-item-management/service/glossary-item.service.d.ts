import { DatabaseClient } from "@/providers/db.provider";
import { GlossaryItem, GlossaryStatus, GlossaryType } from "@repo/prisma/client";
import { GlossaryItemCreateRequest } from "@/features/glossary-item-management/model/glossary-item-create.request";
import { GlossaryItemUpdateRequest } from "@/features/glossary-item-management/model/glossary-item-update.request";
export declare class GlossaryItemService {
    static create(glossaryId: string, glossaryItemType: GlossaryType, payload: GlossaryItemCreateRequest, prismaClient?: DatabaseClient): Promise<GlossaryItem>;
    static update(glossaryItemId: string, payload: GlossaryItemUpdateRequest, prismaClient?: DatabaseClient): Promise<GlossaryItem>;
    static getById(glossaryItemId: string, prismaClient?: DatabaseClient): Promise<{
        id: string;
        name: string;
        status: import("@repo/prisma/index").$Enums.GlossaryStatus;
        code: string;
        type: import("@repo/prisma/index").$Enums.GlossaryType;
        updatedAt: Date;
        referenceCode: string;
        createdAt: Date;
        tenantId: string;
        isDeleted: boolean;
        lastModifiedBy: string;
        glossaryId: string;
    } | null>;
    static getByIds(glossaryItemIds: string[], prismaClient?: DatabaseClient): Promise<{
        id: string;
        name: string;
        status: import("@repo/prisma/index").$Enums.GlossaryStatus;
        code: string;
        type: import("@repo/prisma/index").$Enums.GlossaryType;
        updatedAt: Date;
        referenceCode: string;
        createdAt: Date;
        tenantId: string;
        isDeleted: boolean;
        lastModifiedBy: string;
        glossaryId: string;
    }[]>;
    static updateStatus(glossaryItemId: string, status: GlossaryStatus, prismaClient?: DatabaseClient): Promise<void>;
}
//# sourceMappingURL=glossary-item.service.d.ts.map