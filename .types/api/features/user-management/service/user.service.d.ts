import { UserProfileResponse } from "@/features/user-management/model/user-profile.response";
import { DatabaseClient } from "@/providers/db.provider";
import { UserStatus } from "@repo/prisma/client";
import { UserSignupRequest, UserUpdateRequest } from "@/features/user-management/model";
export declare class UserService {
    static getUserById(userId: string, prismaClient?: DatabaseClient): Promise<UserProfileResponse | undefined>;
    static getUserByEmailId(emailId: string, prismaClient?: DatabaseClient): Promise<UserProfileResponse | undefined>;
    static getActiveUserById(userId: string, prismaClient?: DatabaseClient): Promise<UserProfileResponse | undefined>;
    static getActiveUserByEmailId(emailId: string, prismaClient?: DatabaseClient): Promise<UserProfileResponse | undefined>;
    static createUser(payload: UserSignupRequest, prismaClient?: DatabaseClient): Promise<UserProfileResponse>;
    static updateUser(payload: UserUpdateRequest, prismaClient?: DatabaseClient): Promise<UserProfileResponse>;
    static updateUserStatus(userId: string, status: UserStatus, prismaClient?: DatabaseClient): Promise<UserProfileResponse>;
    private static getUserProfile;
}
//# sourceMappingURL=user.service.d.ts.map