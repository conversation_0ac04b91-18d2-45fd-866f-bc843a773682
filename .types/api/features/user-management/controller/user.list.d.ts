import { UserListRequest } from "@/features/user-management/model/user-list.request";
import { UserListResponse } from "@/features/user-management/model/user-list.response";
import { BaseResponse } from "@/shared/model/base.response";
export declare class UserListController {
    process(payload: UserListRequest): Promise<BaseResponse<UserListResponse>>;
    private buildWhereClause;
    private buildOrderByClause;
    private buildCursorClause;
    private generateCursor;
}
//# sourceMappingURL=user.list.d.ts.map