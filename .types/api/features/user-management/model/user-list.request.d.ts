import { UserRole, UserStatus } from "@repo/prisma/client";
/**
 * User-specific filters
 */
export declare class UserFilters {
    name?: string;
    email?: string;
    role?: UserRole[];
    status?: UserStatus[];
}
/**
 * Sorting configuration
 */
export declare class UserSort {
    field?: string;
    direction?: "asc" | "desc";
}
/**
 * User list request with pagination, filtering, and sorting
 */
export declare class UserListRequest {
    filters?: UserFilters;
    limit?: number;
    cursor?: string;
    sort?: UserSort;
}
//# sourceMappingURL=user-list.request.d.ts.map