import { TenantStatus, TenantSubscriptionType } from "@repo/prisma/client";
/**
 * User-specific filters
 */
export declare class TenantFilters {
    name?: string;
    code?: string;
    subscriptionType?: TenantSubscriptionType[];
    status?: TenantStatus[];
}
/**
 * Sorting configuration
 */
export declare class TenantSort {
    field?: string;
    direction?: "asc" | "desc";
}
/**
 * User list request with pagination, filtering, and sorting
 */
export declare class TenantListRequest {
    filters?: TenantFilters;
    limit?: number;
    cursor?: string;
    sort?: TenantSort;
}
//# sourceMappingURL=tenant-list.request.d.ts.map