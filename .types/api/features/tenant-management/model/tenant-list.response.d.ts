import { TenantStatus, TenantSubscriptionType } from "@repo/prisma/client";
export interface TenantListResponse {
    tenants: TenantResponse[];
    nextCursor?: string;
    hasMore: boolean;
}
export interface TenantResponse {
    id: string;
    name: string;
    code: string;
    subscriptionType: TenantSubscriptionType;
    maxActiveUsers: number;
    currentActiveUsers: number;
    status: TenantStatus;
}
//# sourceMappingURL=tenant-list.response.d.ts.map