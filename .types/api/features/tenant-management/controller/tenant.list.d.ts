import { TenantListRequest } from "@/features/tenant-management/model/tenant-list.request";
import { TenantListResponse } from "@/features/tenant-management/model/tenant-list.response";
import { BaseResponse } from "@/shared/model/base.response";
export declare class TenantListController {
    process(payload: TenantListRequest): Promise<BaseResponse<TenantListResponse>>;
    private buildWhereClause;
    private buildOrderByClause;
    private buildCursorClause;
    private generateCursor;
}
//# sourceMappingURL=tenant.list.d.ts.map