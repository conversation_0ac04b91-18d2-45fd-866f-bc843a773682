import { DatabaseClient } from "@/providers/db.provider";
import { Tenant, TenantStatus, User } from "@repo/prisma/client";
import { TenantRegisterRequest, TenantUpdateRequest } from "@/features/tenant-management/model";
export declare class TenantService {
    static getTenantById(tenantId: string, includeUserData?: boolean, prismaClient?: DatabaseClient): Promise<{
        users: {
            id: string;
            name: string;
            role: import("@repo/prisma/index").$Enums.UserRole;
            displayName: string;
            status: import("@repo/prisma/index").$Enums.UserStatus;
            emailId: string;
            updatedAt: Date;
            createdAt: Date;
            tenantId: string;
            isDeleted: boolean;
            lastModifiedBy: string;
        }[];
        id?: string | undefined;
        name?: string | undefined;
        status?: import("@repo/prisma/index").$Enums.TenantStatus | undefined;
        code?: string | undefined;
        subscriptionType?: import("@repo/prisma/index").$Enums.TenantSubscriptionType | undefined;
        type?: import("@repo/prisma/index").$Enums.TenantType | undefined;
        updatedAt?: Date | undefined;
        createdAt?: Date | undefined;
        maxActiveUsers?: number | undefined;
        isDeleted?: boolean | undefined;
        lastModifiedBy?: string | undefined;
    }>;
    static getActiveTenantById(tenantId: string, includeUserData?: boolean, prismaClient?: DatabaseClient): Promise<{
        users: {
            id: string;
            name: string;
            role: import("@repo/prisma/index").$Enums.UserRole;
            displayName: string;
            status: import("@repo/prisma/index").$Enums.UserStatus;
            emailId: string;
            updatedAt: Date;
            createdAt: Date;
            tenantId: string;
            isDeleted: boolean;
            lastModifiedBy: string;
        }[];
        id?: string | undefined;
        name?: string | undefined;
        status?: import("@repo/prisma/index").$Enums.TenantStatus | undefined;
        code?: string | undefined;
        subscriptionType?: import("@repo/prisma/index").$Enums.TenantSubscriptionType | undefined;
        type?: import("@repo/prisma/index").$Enums.TenantType | undefined;
        updatedAt?: Date | undefined;
        createdAt?: Date | undefined;
        maxActiveUsers?: number | undefined;
        isDeleted?: boolean | undefined;
        lastModifiedBy?: string | undefined;
    } | undefined>;
    static createTenant(payload: TenantRegisterRequest, prismaClient?: DatabaseClient): Promise<{
        tenant: Tenant;
        user: User;
    }>;
    static updateTenant(payload: TenantUpdateRequest, prismaClient?: DatabaseClient): Promise<{
        id: string;
        name: string;
        status: import("@repo/prisma/index").$Enums.TenantStatus;
        code: string;
        subscriptionType: import("@repo/prisma/index").$Enums.TenantSubscriptionType;
        type: import("@repo/prisma/index").$Enums.TenantType;
        updatedAt: Date;
        createdAt: Date;
        maxActiveUsers: number;
        isDeleted: boolean;
        lastModifiedBy: string;
    }>;
    static updateTenantStatus(tenantId: string, status: TenantStatus, prismaClient?: DatabaseClient): Promise<{
        id: string;
        name: string;
        status: import("@repo/prisma/index").$Enums.TenantStatus;
        code: string;
        subscriptionType: import("@repo/prisma/index").$Enums.TenantSubscriptionType;
        type: import("@repo/prisma/index").$Enums.TenantType;
        updatedAt: Date;
        createdAt: Date;
        maxActiveUsers: number;
        isDeleted: boolean;
        lastModifiedBy: string;
    }>;
}
//# sourceMappingURL=tenant.service.d.ts.map