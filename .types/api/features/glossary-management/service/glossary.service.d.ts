import { DatabaseClient } from "@/providers/db.provider";
import { Glossary, GlossaryStatus } from "@repo/prisma/client";
import { GlossaryCreateRequest } from "@/features/glossary-management/model/glossary-create.request";
import { GlossaryUpdateRequest } from "@/features/glossary-management/model/glossary-update.request";
export declare class GlossaryService {
    static createGlossary(payload: GlossaryCreateRequest, prismaClient?: DatabaseClient): Promise<Glossary>;
    static updateGlossary(id: string, payload: GlossaryUpdateRequest, prismaClient?: DatabaseClient): Promise<Glossary>;
    static getGlossaryById(id: string, prismaClient?: DatabaseClient): Promise<{
        id: string;
        name: string;
        status: import("@repo/prisma/index").$Enums.GlossaryStatus;
        type: import("@repo/prisma/index").$Enums.GlossaryType;
        remark: string | null;
        updatedAt: Date;
        createdAt: Date;
        tenantId: string;
        isDeleted: boolean;
        lastModifiedBy: string;
    } | null>;
    static updateGlossaryStatus(id: string, status: GlossaryStatus, prismaClient?: DatabaseClient): Promise<void>;
}
//# sourceMappingURL=glossary.service.d.ts.map