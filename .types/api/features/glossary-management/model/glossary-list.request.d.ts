import { GlossaryStatus, GlossaryType } from "@repo/prisma/client";
/**
 * Glossary-specific filters
 */
export declare class GlossaryFilters {
    name?: string;
    type?: GlossaryType[];
    status?: GlossaryStatus[];
}
/**
 * Sorting configuration
 */
export declare class GlossarySort {
    field?: string;
    direction?: "asc" | "desc";
}
/**
 * User list request with pagination, filtering, and sorting
 */
export declare class GlossaryListRequest {
    filters?: GlossaryFilters;
    limit?: number;
    cursor?: string;
    sort?: GlossarySort;
}
//# sourceMappingURL=glossary-list.request.d.ts.map