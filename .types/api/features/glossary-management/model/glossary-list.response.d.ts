import { GlossaryStatus, GlossaryType } from "@repo/prisma/client";
export interface GlossaryListResponse {
    glossaries: GlossaryResponse[];
    nextCursor?: string;
    hasMore: boolean;
}
export interface GlossaryResponse {
    id: string;
    name: string;
    type: GlossaryType;
    remark: string | null;
    status: GlossaryStatus;
    createdAt: Date;
    updatedAt: Date;
}
//# sourceMappingURL=glossary-list.response.d.ts.map