import { BaseResponse } from "@/shared/model/base.response";
import { GlossaryListRequest } from "@/features/glossary-management/model/glossary-list.request";
import { GlossaryListResponse } from "@/features/glossary-management/model/glossary-list.response";
export declare class GlossaryListController {
    process(payload: GlossaryListRequest): Promise<BaseResponse<GlossaryListResponse>>;
    private buildWhereClause;
    private buildOrderByClause;
    private buildCursorClause;
    private generateCursor;
}
//# sourceMappingURL=glossary.list.d.ts.map