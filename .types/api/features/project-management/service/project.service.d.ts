import { DatabaseClient } from "@/providers/db.provider";
import { ProjectCreateRequest } from "@/features/project-management/model/project-create.request";
import { ProjectUpdateRequest } from "@/features/project-management/model/project-update.request";
import { ProjectStatus } from "@/types-export";
import { ProjectFile, ProjectFileStatus, ProjectFileSubStatus, FileState, FileStateStatus } from "@repo/prisma/client";
import { FileUploadCreateRequest } from "../model/file-upload.create";
export declare class ProjectService {
    static create(payload: ProjectCreateRequest, prismaClient?: DatabaseClient): Promise<{
        id: string;
        status: import("@/types-export").$Enums.ProjectStatus;
        code: string;
        updatedAt: Date;
        createdAt: Date;
        vessel: string;
        hullNumber: string;
        imoNumber: string;
        vesselDeliveryDate: Date | null;
        tenantId: string;
        isDeleted: boolean;
        lastModifiedBy: string;
    }>;
    static update(projectId: string, payload: ProjectUpdateRequest, prismaClient?: DatabaseClient): Promise<{
        id: string;
        status: import("@/types-export").$Enums.ProjectStatus;
        code: string;
        updatedAt: Date;
        createdAt: Date;
        vessel: string;
        hullNumber: string;
        imoNumber: string;
        vesselDeliveryDate: Date | null;
        tenantId: string;
        isDeleted: boolean;
        lastModifiedBy: string;
    }>;
    static getById(id: string, prismaClient?: DatabaseClient): Promise<{
        id: string;
        status: import("@/types-export").$Enums.ProjectStatus;
        code: string;
        updatedAt: Date;
        createdAt: Date;
        vessel: string;
        hullNumber: string;
        imoNumber: string;
        vesselDeliveryDate: Date | null;
        tenantId: string;
        isDeleted: boolean;
        lastModifiedBy: string;
    } | null>;
    static createFileEntry(projectId: string, file: FileUploadCreateRequest, prismaClient?: DatabaseClient): Promise<ProjectFile>;
    static updateStatus(projectId: string, status: ProjectStatus, prismaClient?: DatabaseClient): Promise<void>;
    static getFileById(projectId: string, fileId: string, prismaClient?: DatabaseClient): Promise<{
        id: string;
        name: string;
        status: import("@/types-export").$Enums.ProjectFileStatus;
        updatedAt: Date;
        createdAt: Date;
        size: number;
        contentType: string;
        tenantId: string;
        isDeleted: boolean;
        lastModifiedBy: string;
        projectId: string;
        rootFolderPath: string;
        originalFilePath: string | null;
        objectStoragePath: string;
        batchNumber: number;
        subStatus: import("@/types-export").$Enums.ProjectFileSubStatus;
        assetName: string | null;
        manualsCategory: string | null;
        pageCount: number | null;
    } | null>;
    /**
     * Update project file status and sub-status
     */
    static updateFileStatus(fileId: string, status: ProjectFileStatus, subStatus: ProjectFileSubStatus, prismaClient?: DatabaseClient): Promise<{
        id: string;
        name: string;
        status: import("@/types-export").$Enums.ProjectFileStatus;
        updatedAt: Date;
        createdAt: Date;
        size: number;
        contentType: string;
        tenantId: string;
        isDeleted: boolean;
        lastModifiedBy: string;
        projectId: string;
        rootFolderPath: string;
        originalFilePath: string | null;
        objectStoragePath: string;
        batchNumber: number;
        subStatus: import("@/types-export").$Enums.ProjectFileSubStatus;
        assetName: string | null;
        manualsCategory: string | null;
        pageCount: number | null;
    }>;
    /**
     * Create file state history entry
     */
    static createFileStateHistory(fileId: string, runId: number, state: FileState, status: FileStateStatus, systemData?: any, prismaClient?: DatabaseClient): Promise<{
        id: string;
        status: import("@/types-export").$Enums.FileStateStatus;
        updatedAt: Date;
        createdAt: Date;
        tenantId: string;
        isDeleted: boolean;
        lastModifiedBy: string;
        fileId: string;
        runId: number;
        state: import("@/types-export").$Enums.FileState;
        systemData: import("@repo/prisma/runtime/library").JsonValue | null;
        userFeedback: import("@repo/prisma/runtime/library").JsonValue | null;
    }>;
    /**
     * Get files by IDs with project validation
     */
    static getFilesByIds(projectId: string, fileIds: string[], prismaClient?: DatabaseClient): Promise<{
        id: string;
        name: string;
        status: import("@/types-export").$Enums.ProjectFileStatus;
        updatedAt: Date;
        createdAt: Date;
        size: number;
        contentType: string;
        tenantId: string;
        isDeleted: boolean;
        lastModifiedBy: string;
        projectId: string;
        rootFolderPath: string;
        originalFilePath: string | null;
        objectStoragePath: string;
        batchNumber: number;
        subStatus: import("@/types-export").$Enums.ProjectFileSubStatus;
        assetName: string | null;
        manualsCategory: string | null;
        pageCount: number | null;
    }[]>;
    /**
     * Check if user is project handler for the given project
     */
    static isUserProjectHandler(projectId: string, userId: string, prismaClient?: DatabaseClient): Promise<boolean>;
}
//# sourceMappingURL=project.service.d.ts.map