import { BaseResponse } from "@/shared/model/base.response";
import { ProjectFileListRequest } from "../model/project-file-list.request";
import { ProjectFileListResponse } from "../model/project-file-list.response";
export declare class ProjectFileListController {
    process(projectId: string, payload: ProjectFileListRequest): Promise<BaseResponse<ProjectFileListResponse>>;
    private buildWhereClause;
    private buildOrderByClause;
    private buildCursorClause;
    private generateCursor;
}
//# sourceMappingURL=project-file.list.d.ts.map