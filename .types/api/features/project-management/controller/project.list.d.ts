import { ProjectListRequest } from "@/features/project-management/model/project-list.request";
import { ProjectListResponse } from "@/features/project-management/model/project-list.response";
import { BaseResponse } from "@/shared/model/base.response";
export declare class ProjectListController {
    process(payload: ProjectListRequest): Promise<BaseResponse<ProjectListResponse>>;
    private buildWhereClause;
    private buildOrderByClause;
    private buildCursorClause;
    private generateCursor;
}
//# sourceMappingURL=project.list.d.ts.map