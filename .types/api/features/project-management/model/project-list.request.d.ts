import { ProjectStatus } from "@repo/prisma/client";
/**
 * Project-specific filters
 */
export declare class ProjectFilters {
    code?: string;
    vessel?: string;
    vesselType?: string;
    status?: ProjectStatus[];
    projectHandler?: string;
}
/**
 * Sorting configuration
 */
export declare class ProjectSort {
    field?: string;
    direction?: "asc" | "desc";
}
/**
 * Project list request with pagination, filtering, and sorting
 */
export declare class ProjectListRequest {
    filters?: ProjectFilters;
    limit?: number;
    cursor?: string;
    sort?: ProjectSort;
}
//# sourceMappingURL=project-list.request.d.ts.map