import { UserRole } from "@/types-export";
export declare class ProjectCategoryItemBase {
    glossaryItemId: string;
    glossaryItemName?: string;
}
export declare class ProjectCategoryBase {
    glossaryCategoryId: string;
    glossaryCategoryName?: string;
    items: ProjectCategoryItemBase[];
}
export declare class ProjectUserBase {
    id: string;
    name?: string;
    role?: UserRole;
}
//# sourceMappingURL=project.base.d.ts.map