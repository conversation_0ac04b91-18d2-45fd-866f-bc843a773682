import { ProjectFileStatus, ProjectFileSubStatus } from "@repo/prisma/client";
export interface ProjectFileListResponse {
    files: ProjectFileResponse[];
    nextCursor?: string;
    hasMore: boolean;
}
export interface ProjectFileResponse {
    id: string;
    name: string;
    createdAt: Date;
    size: number;
    path: string | null;
    batchNumber: number;
    status: ProjectFileStatus;
    subStatus: ProjectFileSubStatus;
}
//# sourceMappingURL=project-file-list.response.d.ts.map