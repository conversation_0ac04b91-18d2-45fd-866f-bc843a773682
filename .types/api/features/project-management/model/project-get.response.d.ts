import { ProjectStatus, ProjectFileStatus } from "@repo/prisma/client";
import { ProjectCategoryBase, ProjectUserBase } from "@/features/project-management/model/project.base";
export interface ProjectGetResponse {
    id: string;
    code: string;
    vessel: string;
    assignee: ProjectUserBase;
    categories: ProjectCategoryBase[];
    hullNumber: string;
    imoNumber: string;
    vesselDeliveryDate: Date | null;
    status: ProjectStatus;
    fileCount: number;
    fileCountMap: {
        [key in ProjectFileStatus]: number;
    };
    createdAt: Date;
    updatedAt: Date;
}
//# sourceMappingURL=project-get.response.d.ts.map