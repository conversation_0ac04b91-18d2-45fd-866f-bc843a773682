import { ProjectStatus } from "@/types-export";
import { ProjectCategoryBase, ProjectUserBase } from "@/features/project-management/model/project.base";
export declare class ProjectUpdateRequest {
    assignee: ProjectUserBase;
    code: string;
    vessel: string;
    categories: ProjectCategoryBase[];
    hullNumber: string;
    imoNumber: string;
    vesselDeliveryDate: Date;
    status: ProjectStatus;
}
//# sourceMappingURL=project-update.request.d.ts.map