import { ProjectFileStatus } from "@repo/prisma/client";
/**
 * Project-specific filters
 */
export declare class ProjectFileFilter {
    name?: string;
    path?: string;
    status?: ProjectFileStatus[];
}
/**
 * Sorting configuration
 */
export declare class ProjectFileSort {
    field?: string;
    direction?: "asc" | "desc";
}
/**
 * Project list request with pagination, filtering, and sorting
 */
export declare class ProjectFileListRequest {
    filters?: ProjectFileFilter;
    limit?: number;
    cursor?: string;
    sort?: ProjectFileSort;
}
//# sourceMappingURL=project-file-list.request.d.ts.map