export declare enum AuthErrorType {
    UserAlreadyExists = "UserAlreadyExists",
    UserDoesNotExists = "UserDoesNotExists",
    UserAlreadyVerified = "UserAlreadyVerified",
    InvalidToken = "InvalidToken",
    TokenExpired = "TokenExpired",
    PermissionDenied = "PermissionDenied",
    ServiceUnavailable = "ServiceUnavailable",
    UnAuthorized = "UnAuthorized"
}
export declare class AuthError extends Error {
    type: AuthErrorType;
    details?: Record<string, any> | undefined;
    constructor(type: AuthErrorType, details?: Record<string, any> | undefined);
}
//# sourceMappingURL=auth.error.d.ts.map