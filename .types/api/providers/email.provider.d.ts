export declare const initEmailProvider: () => Promise<void>;
export declare function sendVerificationEmail(email: string, name: string, verifyUrl: string): Promise<void>;
export declare function sendTenantVerificationEmail(email: string, name: string, verifyUrl: string): Promise<void>;
export declare function sendPasswordResetEmail(email: string, name: string, resetLink: string): Promise<void>;
export declare function sendUserDeactivatedEmail(email: string, name: string): Promise<void>;
export declare function sendTenantDeactivatedEmail(email: string[], name: string): Promise<void>;
//# sourceMappingURL=email.provider.d.ts.map