import Session from "supertokens-node/recipe/session";
import { AuthUser } from "@/shared/model/auth.user";
import { UserClaims } from "@/shared/model/auth.user-claims";
export declare const initAuthProvider: () => Promise<void>;
export declare const getUserDetailsByEmail: (email: string) => Promise<AuthUser>;
export declare const getAuthUserMetadata: (user_id: string) => Promise<UserClaims>;
export declare const signUpUsingEmailPassword: (userClaims: UserClaims, password: string) => Promise<AuthUser>;
export declare const getEmailVerificationToken: (email: string, revokePreviousEmailVerificationTokens?: boolean) => Promise<string>;
export declare const getResetPasswordToken: (email: string, user_id: string) => Promise<{
    status: "OK";
    token: string;
} | {
    status: "UNKNOWN_USER_ID_ERROR";
}>;
export declare const verifyEmailByToken: (token: string) => Promise<AuthUser>;
export declare const rollbackEmailVerification: (id: string, email: string) => Promise<void>;
export declare const updateAuthUser: (userClaims: UserClaims) => Promise<void>;
export declare const deactivateUser: (email: string) => Promise<void>;
export declare const authProviderMiddleware: (req: import("express").Request, res: import("express").Response, next: import("express").NextFunction) => Promise<void>;
export declare const authProviderErrorHandler: (err: any, req: import("express").Request, res: import("express").Response, next: import("express").NextFunction) => Promise<void>;
export declare const statelessVerifySession: (req: import("supertokens-node/framework/express").SessionRequest, res: import("express").Response, next: import("express").NextFunction) => Promise<void>;
export declare const statefullVerifySession: (req: import("supertokens-node/framework/express").SessionRequest, res: import("express").Response, next: import("express").NextFunction) => Promise<void>;
export { Session };
//# sourceMappingURL=auth.provider.d.ts.map