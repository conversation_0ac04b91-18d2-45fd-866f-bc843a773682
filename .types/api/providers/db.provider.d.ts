import { PrismaClient } from "@repo/prisma/client";
export declare const initDB: () => Promise<void>;
export declare const closeDB: () => Promise<void>;
export type DatabaseClient = Omit<PrismaClient, "$extends" | "$transaction" | "$disconnect" | "$connect" | "$on">;
export declare const dbClient: () => DatabaseClient;
export declare const withTransaction: <T>(callback: (tx: DatabaseClient) => Promise<T>) => Promise<T>;
//# sourceMappingURL=db.provider.d.ts.map