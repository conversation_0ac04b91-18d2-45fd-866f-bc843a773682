import { Readable } from "stream";
/**
 * Configuration interface for Azure Blob Storage
 */
export interface AzureBlobConfig {
    connectionString: string;
    containerName: string;
}
/**
 * Upload options for blob storage
 */
export interface BlobUploadOptions {
    /**
     * The name/path for the blob in the container
     */
    blobName: string;
    /**
     * Content type of the file (e.g., 'image/jpeg', 'application/pdf')
     */
    contentType?: string;
    /**
     * Custom metadata to attach to the blob
     */
    metadata?: Record<string, string>;
    /**
     * Tags to attach to the blob for categorization
     */
    tags?: Record<string, string>;
    /**
     * Whether to overwrite existing blob with same name
     * @default true
     */
    overwrite?: boolean;
    /**
     * AbortSignal to cancel the upload operation
     */
    abortSignal?: AbortSignal;
}
/**
 * Result of a successful blob upload
 */
export interface BlobUploadResult {
    /**
     * The full URL to access the uploaded blob
     */
    url: string;
    /**
     * The name of the uploaded blob
     */
    blobName: string;
    /**
     * ETag of the uploaded blob
     */
    etag: string;
    /**
     * Size of the uploaded blob in bytes
     */
    contentLength?: number;
    /**
     * Last modified timestamp
     */
    lastModified?: Date;
    /**
     * Request ID for tracking
     */
    requestId?: string;
}
/**
 * Azure Blob Storage File Management Provider
 *
 * Provides stream-based file upload capabilities to Azure Blob Storage
 * without loading files into memory or temporary local storage.
 */
export declare class BlobProvider {
    private blobServiceClient;
    private containerName;
    constructor(config: AzureBlobConfig);
    /**
     * Upload a file stream directly to Azure Blob Storage
     *
     * This method accepts a readable stream and uploads it directly to Azure Blob Storage
     * without buffering the entire file in memory or writing to temporary storage.
     *
     * @param stream - Readable stream containing the file data
     * @param options - Upload configuration options
     * @returns Promise resolving to upload result with blob URL and metadata
     *
     * @example
     * ```typescript
     * const fileStream = fs.createReadStream('path/to/file.pdf');
     * const result = await fileProvider.uploadStream(fileStream, {
     *   blobName: 'documents/file.pdf',
     *   contentType: 'application/pdf',
     *   metadata: { uploadedBy: 'user123' },
     *   tags: { department: 'finance' }
     * });
     * console.log('File uploaded to:', result.url);
     * ```
     */
    uploadStream(stream: Readable, options: BlobUploadOptions): Promise<BlobUploadResult>;
    downloadStream(blobName: string): Promise<any>;
}
export declare const blobProvider: BlobProvider;
//# sourceMappingURL=blob.provider.d.ts.map