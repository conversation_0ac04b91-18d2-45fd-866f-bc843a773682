import { UserProfileResponse } from "@/features/user-management/model/user-profile.response";
import { TenantStatus, TenantSubscriptionType, TenantType, UserRole, UserStatus } from "@repo/prisma/client";
export type UserClaims = {
    id: string;
    name: string;
    role: UserRole;
    displayName: string;
    email: string;
    status: UserStatus;
    tenant: {
        id: string;
        name: string;
        code: string;
        subscriptionType: TenantSubscriptionType;
        type: TenantType;
        status: TenantStatus;
    };
};
export declare const mapToUserClaims: (profile: UserProfileResponse) => UserClaims;
//# sourceMappingURL=auth.user-claims.d.ts.map