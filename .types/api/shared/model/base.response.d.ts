export declare class BaseResponse<T = any> {
    readonly success: boolean;
    readonly data?: T | undefined;
    readonly message?: string | undefined;
    readonly error?: {
        code: string;
        message: string;
    } | undefined;
    readonly meta?: Record<string, any> | undefined;
    constructor(success: boolean, data?: T | undefined, message?: string | undefined, error?: {
        code: string;
        message: string;
    } | undefined, meta?: Record<string, any> | undefined);
    static ok(): BaseResponse;
    static success<T>(data?: T, message?: string): BaseResponse<T>;
    static error(code: string, message: string, meta?: Record<string, any>): BaseResponse;
}
//# sourceMappingURL=base.response.d.ts.map