import type { Request } from "express";
import { FileServiceUploadResponse } from "@/features/project-management/model/file-upload.response";
declare class FileService {
    streamUpload(req: Request, pathPrefix: string, allowedTypes: string[]): Promise<FileServiceUploadResponse>;
    /**
     * Sanitize file path to prevent directory traversal and ensure valid format
     */
    private sanitizePath;
    /**
     * Sanitize filename to ensure valid format
     */
    private sanitizeFileName;
}
export declare const fileService: FileService;
export {};
//# sourceMappingURL=file.service.d.ts.map