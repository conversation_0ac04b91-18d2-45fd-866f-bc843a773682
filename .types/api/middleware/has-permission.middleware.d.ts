import { NextFunction, Request, Response } from "express";
import { Resource, Action } from "@/config/policy/rules";
/**
 * Middleware to check if the authenticated user has permission to perform specific actions on a resource
 *
 * @param resourceName - The resource name to check permission for (e.g., "user", "tenant")
 * @param actionNames - Array of action names that are allowed (e.g., ["create", "update"])
 * @returns Express middleware function
 */
export declare const hasPermission: (resourceName: Resource, actionNames: Action[]) => (req: Request, res: Response, next: NextFunction) => void;
/**
 * Convenience function to create permission middleware for single action
 *
 * @param resourceName - The resource name to check permission for
 * @param actionName - Single action name that is required
 * @returns Express middleware function
 */
export declare const requirePermission: (resourceName: Resource, actionName: Action) => (req: Request, res: Response, next: NextFunction) => void;
/**
 * Convenience function to create permission middleware for read access
 *
 * @param resourceName - The resource name to check read permission for
 * @returns Express middleware function
 */
export declare const requireReadPermission: (resourceName: Resource) => (req: Request, res: Response, next: NextFunction) => void;
/**
 * Convenience function to create permission middleware for write access (create/update)
 *
 * @param resourceName - The resource name to check write permission for
 * @returns Express middleware function
 */
export declare const requireWritePermission: (resourceName: Resource) => (req: Request, res: Response, next: NextFunction) => void;
/**
 * Convenience function to create permission middleware for delete access
 *
 * @param resourceName - The resource name to check delete permission for
 * @returns Express middleware function
 */
export declare const requireDeletePermission: (resourceName: Resource) => (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=has-permission.middleware.d.ts.map